"""
兼容性装饰器
"""
import time
import functools
from django.http import JsonResponse
from django.contrib.auth import get_user_model
from rest_framework_simplejwt.authentication import JWTAuthentication
from apps.authentication.authentication import APIKeyAuthentication
from apps.authentication.models import LoginLog
from loguru import logger
from .loggers import <PERSON><PERSON>og<PERSON>, PerformanceLogger, SecurityLogger, api_logger, performance_logger, security_logger

User = get_user_model()


def enhanced_api_compatibility(view_func):
    """
    增强API兼容性装饰器
    - 支持多种认证方式
    - 记录API调用日志
    - 统计API使用情况
    - 错误处理和监控
    """
    @functools.wraps(view_func)
    def wrapper(request, *args, **kwargs):
        start_time = time.time()
        
        # 尝试进行认证
        user = authenticate_request(request)
        if user:
            request.user = user
        
        # 记录API调用开始
        logger.info(f"API调用开始: {request.method} {request.path}")
        APILogger.log_request(request)

        try:
            # 调用原始视图函数
            response = view_func(request, *args, **kwargs)

            # 记录成功调用
            duration = time.time() - start_time
            log_api_call(request, response.status_code, duration)
            APILogger.log_response(request, response, duration)
            
            # 添加响应头
            if hasattr(response, '__setitem__'):
                response['X-API-Version'] = '2.0.0'
                response['X-Compatible-Version'] = '1.0.0'
                response['X-Response-Time'] = f"{duration:.3f}s"
            
            return response
            
        except Exception as e:
            # 记录错误调用
            duration = time.time() - start_time
            log_api_call(request, 500, duration, str(e))
            
            logger.error(f"API调用失败: {request.path}, 错误: {e}")
            
            return JsonResponse({
                'error': 'Internal Server Error',
                'message': '服务器内部错误，请稍后重试'
            }, status=500)
    
    return wrapper


def authenticate_request(request):
    """
    尝试认证请求
    支持JWT Token和API Key两种方式
    """
    # 尝试JWT认证
    jwt_auth = JWTAuthentication()
    try:
        jwt_result = jwt_auth.authenticate(request)
        if jwt_result:
            user, token = jwt_result
            logger.debug(f"JWT认证成功: {user.username}")
            return user
    except Exception:
        pass
    
    # 尝试API Key认证
    api_key_auth = APIKeyAuthentication()
    try:
        api_key_result = api_key_auth.authenticate(request)
        if api_key_result:
            user, api_key = api_key_result
            logger.debug(f"API Key认证成功: {user.username}")
            return user
    except Exception:
        pass
    
    return None


def log_api_call(request, status_code, duration, error_message=None):
    """记录API调用日志"""
    try:
        # 获取客户端IP
        x_forwarded_for = request.META.get('HTTP_X_FORWARDED_FOR')
        if x_forwarded_for:
            client_ip = x_forwarded_for.split(',')[0]
        else:
            client_ip = request.META.get('REMOTE_ADDR')
        
        # 记录到数据库（如果用户已认证）
        if hasattr(request, 'user') and request.user.is_authenticated:
            LoginLog.objects.create(
                user=request.user,
                ip_address=client_ip,
                user_agent=request.META.get('HTTP_USER_AGENT', ''),
                login_type='api_call',
                success=status_code < 400,
                failure_reason=error_message if status_code >= 400 else ''
            )
        
        # 记录到日志文件
        log_data = {
            'method': request.method,
            'path': request.path,
            'status_code': status_code,
            'duration': duration,
            'client_ip': client_ip,
            'user_agent': request.META.get('HTTP_USER_AGENT', ''),
            'user': str(request.user) if hasattr(request, 'user') and request.user.is_authenticated else 'Anonymous'
        }
        
        if error_message:
            log_data['error'] = error_message
        
        logger.info(f"API调用记录: {log_data}")
        
    except Exception as e:
        logger.error(f"记录API调用日志失败: {e}")


def rate_limit(max_requests=60, window_seconds=60):
    """
    API限流装饰器
    """
    def decorator(view_func):
        @functools.wraps(view_func)
        def wrapper(request, *args, **kwargs):
            from django.core.cache import cache
            
            # 获取客户端标识
            if hasattr(request, 'user') and request.user.is_authenticated:
                client_id = f"user_{request.user.id}"
            else:
                x_forwarded_for = request.META.get('HTTP_X_FORWARDED_FOR')
                if x_forwarded_for:
                    client_ip = x_forwarded_for.split(',')[0]
                else:
                    client_ip = request.META.get('REMOTE_ADDR')
                client_id = f"ip_{client_ip}"
            
            # 检查限流
            cache_key = f"rate_limit:{client_id}"
            current_requests = cache.get(cache_key, 0)
            
            if current_requests >= max_requests:
                return JsonResponse({
                    'error': 'Rate limit exceeded',
                    'message': f'请求频率过高，每{window_seconds}秒最多{max_requests}次请求'
                }, status=429)
            
            # 增加请求计数
            cache.set(cache_key, current_requests + 1, window_seconds)
            
            return view_func(request, *args, **kwargs)
        
        return wrapper
    return decorator


def require_quota(quota_cost=1):
    """
    配额检查装饰器
    """
    def decorator(view_func):
        @functools.wraps(view_func)
        def wrapper(request, *args, **kwargs):
            # 只对已认证用户检查配额
            if hasattr(request, 'user') and request.user.is_authenticated:
                if not request.user.can_use_quota(quota_cost):
                    return JsonResponse({
                        'error': '配额不足',
                        'message': f'当前配额: {request.user.quota_used}/{request.user.quota_limit}',
                        'required_quota': quota_cost
                    }, status=429)
                
                # 使用配额
                request.user.use_quota(quota_cost)
            
            return view_func(request, *args, **kwargs)
        
        return wrapper
    return decorator


def monitor_performance(threshold_seconds=5.0):
    """
    性能监控装饰器
    """
    def decorator(view_func):
        @functools.wraps(view_func)
        def wrapper(request, *args, **kwargs):
            start_time = time.time()
            
            try:
                response = view_func(request, *args, **kwargs)
                duration = time.time() - start_time
                
                # 如果响应时间超过阈值，记录警告
                if duration > threshold_seconds:
                    logger.warning(f"API响应时间过长: {request.path}, 耗时: {duration:.3f}s")
                
                return response
                
            except Exception as e:
                duration = time.time() - start_time
                logger.error(f"API执行异常: {request.path}, 耗时: {duration:.3f}s, 错误: {e}")
                raise
        
        return wrapper
    return decorator


def cors_headers(view_func):
    """
    CORS头部装饰器
    """
    @functools.wraps(view_func)
    def wrapper(request, *args, **kwargs):
        response = view_func(request, *args, **kwargs)
        
        # 添加CORS头部
        if hasattr(response, '__setitem__'):
            response['Access-Control-Allow-Origin'] = '*'
            response['Access-Control-Allow-Methods'] = 'GET, POST, PUT, DELETE, OPTIONS'
            response['Access-Control-Allow-Headers'] = 'Content-Type, Authorization, X-API-Key'
            response['Access-Control-Max-Age'] = '86400'
        
        return response
    
    return wrapper
