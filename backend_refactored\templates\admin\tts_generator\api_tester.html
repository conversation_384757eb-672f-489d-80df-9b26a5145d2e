<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>API测试器 - CosyVoice管理后台</title>
    <style>
        body {
            margin: 0;
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, 'Helvetica Neue', Arial, sans-serif;
            background: #f5f5f5;
            color: #333;
        }
        
        .api-container {
            padding: 20px;
            max-width: 1400px;
            margin: 0 auto;
        }
        
        .api-header {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 30px;
            border-radius: 12px;
            margin-bottom: 30px;
            text-align: center;
        }
        
        .api-header h1 {
            margin: 0 0 10px 0;
            font-size: 28px;
            font-weight: 600;
        }
        
        .api-header p {
            margin: 0;
            opacity: 0.9;
            font-size: 16px;
        }
        
        .api-grid {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 30px;
            margin-bottom: 30px;
        }
        
        .api-endpoints {
            background: white;
            padding: 24px;
            border-radius: 12px;
            box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
            border: 1px solid #f0f0f0;
        }
        
        .api-endpoints h3 {
            margin: 0 0 20px 0;
            font-size: 18px;
            font-weight: 600;
            color: #262626;
        }
        
        .endpoint-list {
            list-style: none;
            padding: 0;
            margin: 0;
        }
        
        .endpoint-item {
            padding: 12px 0;
            border-bottom: 1px solid #f0f0f0;
            cursor: pointer;
            transition: background 0.3s;
        }
        
        .endpoint-item:hover {
            background: #f8f9fa;
        }
        
        .endpoint-item:last-child {
            border-bottom: none;
        }
        
        .endpoint-method {
            display: inline-block;
            padding: 2px 8px;
            border-radius: 4px;
            font-size: 12px;
            font-weight: 600;
            margin-right: 8px;
        }
        
        .method-get {
            background: #e6f7ff;
            color: #1890ff;
        }
        
        .method-post {
            background: #f6ffed;
            color: #52c41a;
        }
        
        .endpoint-path {
            font-family: 'Courier New', monospace;
            font-size: 14px;
            color: #262626;
        }
        
        .endpoint-desc {
            font-size: 12px;
            color: #8c8c8c;
            margin-top: 4px;
        }
        
        .test-panel {
            background: white;
            padding: 24px;
            border-radius: 12px;
            box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
            border: 1px solid #f0f0f0;
        }
        
        .test-panel h3 {
            margin: 0 0 20px 0;
            font-size: 18px;
            font-weight: 600;
            color: #262626;
        }
        
        .form-group {
            margin-bottom: 20px;
        }
        
        .form-label {
            display: block;
            margin-bottom: 8px;
            font-weight: 500;
            color: #262626;
        }
        
        .form-input {
            width: 100%;
            padding: 12px;
            border: 1px solid #d9d9d9;
            border-radius: 6px;
            font-size: 14px;
            transition: border-color 0.3s;
        }
        
        .form-input:focus {
            outline: none;
            border-color: #1890ff;
            box-shadow: 0 0 0 2px rgba(24, 144, 255, 0.2);
        }
        
        .form-textarea {
            min-height: 100px;
            resize: vertical;
            font-family: 'Courier New', monospace;
        }
        
        .test-button {
            width: 100%;
            padding: 16px 24px;
            background: #1890ff;
            color: white;
            border: none;
            border-radius: 6px;
            font-size: 16px;
            font-weight: 600;
            cursor: pointer;
            transition: background 0.3s;
        }
        
        .test-button:hover {
            background: #40a9ff;
        }
        
        .test-button:disabled {
            background: #d9d9d9;
            cursor: not-allowed;
        }
        
        .result-section {
            background: white;
            padding: 24px;
            border-radius: 12px;
            box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
            border: 1px solid #f0f0f0;
            margin-top: 30px;
        }
        
        .result-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 16px;
        }
        
        .result-status {
            padding: 4px 12px;
            border-radius: 12px;
            font-size: 12px;
            font-weight: 500;
        }
        
        .status-success {
            background: #f6ffed;
            color: #52c41a;
        }
        
        .status-error {
            background: #fff2f0;
            color: #ff4d4f;
        }
        
        .result-content {
            background: #f8f9fa;
            padding: 16px;
            border-radius: 6px;
            font-family: 'Courier New', monospace;
            font-size: 14px;
            white-space: pre-wrap;
            max-height: 400px;
            overflow-y: auto;
        }
        
        .recent-tests {
            background: white;
            padding: 24px;
            border-radius: 12px;
            box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
            border: 1px solid #f0f0f0;
            margin-top: 30px;
        }
        
        .recent-tests h3 {
            margin: 0 0 20px 0;
            font-size: 18px;
            font-weight: 600;
            color: #262626;
        }
        
        .test-history {
            list-style: none;
            padding: 0;
            margin: 0;
        }
        
        .test-history-item {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 12px 0;
            border-bottom: 1px solid #f0f0f0;
        }
        
        .test-history-item:last-child {
            border-bottom: none;
        }
        
        .back-to-admin {
            position: fixed;
            top: 20px;
            right: 20px;
            background: #1890ff;
            color: white;
            padding: 10px 20px;
            border-radius: 6px;
            text-decoration: none;
            font-size: 14px;
            box-shadow: 0 2px 8px rgba(24, 144, 255, 0.3);
            transition: all 0.3s;
        }
        
        .back-to-admin:hover {
            background: #40a9ff;
            color: white;
            text-decoration: none;
            transform: translateY(-1px);
            box-shadow: 0 4px 12px rgba(24, 144, 255, 0.4);
        }
        
        /* 响应式设计 */
        @media (max-width: 768px) {
            .api-grid {
                grid-template-columns: 1fr;
            }
            
            .api-container {
                padding: 10px;
            }
        }
    </style>
</head>
<body>
    <!-- 返回管理后台按钮 -->
    <a href="/admin/" class="back-to-admin">← 返回管理后台</a>
    
    <div class="api-container">
        <!-- 头部区域 -->
        <div class="api-header">
            <h1>🧪 API测试器</h1>
            <p>测试所有TTS API接口，验证功能和性能</p>
        </div>
        
        <!-- API测试区域 -->
        <div class="api-grid">
            <!-- API端点列表 -->
            <div class="api-endpoints">
                <h3>API端点列表</h3>
                <ul class="endpoint-list">
                    <li class="endpoint-item" onclick="selectEndpoint('sft')">
                        <div>
                            <span class="endpoint-method method-post">POST</span>
                            <span class="endpoint-path">/direct/inference_sft</span>
                        </div>
                        <div class="endpoint-desc">SFT语音合成</div>
                    </li>
                    <li class="endpoint-item" onclick="selectEndpoint('zero_shot')">
                        <div>
                            <span class="endpoint-method method-post">POST</span>
                            <span class="endpoint-path">/direct/inference_zero_shot</span>
                        </div>
                        <div class="endpoint-desc">零样本语音克隆</div>
                    </li>
                    <li class="endpoint-item" onclick="selectEndpoint('cross_lingual')">
                        <div>
                            <span class="endpoint-method method-post">POST</span>
                            <span class="endpoint-path">/direct/inference_cross_lingual</span>
                        </div>
                        <div class="endpoint-desc">跨语种复刻</div>
                    </li>
                    <li class="endpoint-item" onclick="selectEndpoint('instruct')">
                        <div>
                            <span class="endpoint-method method-post">POST</span>
                            <span class="endpoint-path">/direct/inference_instruct</span>
                        </div>
                        <div class="endpoint-desc">指令控制</div>
                    </li>
                    <li class="endpoint-item" onclick="selectEndpoint('list_speakers')">
                        <div>
                            <span class="endpoint-method method-get">GET</span>
                            <span class="endpoint-path">/list_speakers</span>
                        </div>
                        <div class="endpoint-desc">获取说话人列表</div>
                    </li>
                    <li class="endpoint-item" onclick="selectEndpoint('engine_status')">
                        <div>
                            <span class="endpoint-method method-get">GET</span>
                            <span class="endpoint-path">/direct/engine_status</span>
                        </div>
                        <div class="endpoint-desc">引擎状态检查</div>
                    </li>
                </ul>
            </div>
            
            <!-- 测试面板 -->
            <div class="test-panel">
                <h3>API测试</h3>
                <form onsubmit="testAPI(event)">
                    <div class="form-group">
                        <label class="form-label">API端点</label>
                        <input type="text" id="endpoint" class="form-input" placeholder="选择左侧端点或手动输入" required>
                    </div>
                    <div class="form-group">
                        <label class="form-label">请求方法</label>
                        <select id="method" class="form-input">
                            <option value="GET">GET</option>
                            <option value="POST">POST</option>
                        </select>
                    </div>
                    <div class="form-group">
                        <label class="form-label">测试数据 (JSON格式)</label>
                        <textarea id="testData" class="form-input form-textarea" placeholder='{"tts_text": "测试文本", "spk_id": "中文女"}'></textarea>
                    </div>
                    <button type="submit" class="test-button">🧪 开始测试</button>
                </form>
            </div>
        </div>
        
        <!-- 测试结果 -->
        <div id="result-section" class="result-section" style="display: none;">
            <div class="result-header">
                <h3>测试结果</h3>
                <span id="result-status" class="result-status">测试中...</span>
            </div>
            <div id="result-content" class="result-content"></div>
        </div>
        
        <!-- 最近测试记录 -->
        <div class="recent-tests">
            <h3>最近测试记录</h3>
            <ul class="test-history" id="test-history">
                <li class="test-history-item">
                    <span>暂无测试记录</span>
                    <span>-</span>
                </li>
            </ul>
        </div>
    </div>

    <script>
        let testHistory = [];
        
        function selectEndpoint(type) {
            const endpoint = document.getElementById('endpoint');
            const method = document.getElementById('method');
            const testData = document.getElementById('testData');
            
            switch(type) {
                case 'sft':
                    endpoint.value = '/direct/inference_sft';
                    method.value = 'POST';
                    testData.value = JSON.stringify({
                        "tts_text": "你好，这是SFT模式测试。",
                        "spk_id": "中文女"
                    }, null, 2);
                    break;
                case 'zero_shot':
                    endpoint.value = '/direct/inference_zero_shot';
                    method.value = 'POST';
                    testData.value = JSON.stringify({
                        "tts_text": "这是零样本克隆测试。",
                        "prompt_text": "参考音频文本"
                    }, null, 2);
                    break;
                case 'cross_lingual':
                    endpoint.value = '/direct/inference_cross_lingual';
                    method.value = 'POST';
                    testData.value = JSON.stringify({
                        "tts_text": "This is cross-lingual test."
                    }, null, 2);
                    break;
                case 'instruct':
                    endpoint.value = '/direct/inference_instruct';
                    method.value = 'POST';
                    testData.value = JSON.stringify({
                        "tts_text": "今天天气很好。",
                        "spk_id": "中文女",
                        "instruct_text": "用温柔的语气说"
                    }, null, 2);
                    break;
                case 'list_speakers':
                    endpoint.value = '/list_speakers';
                    method.value = 'GET';
                    testData.value = '{}';
                    break;
                case 'engine_status':
                    endpoint.value = '/direct/engine_status';
                    method.value = 'GET';
                    testData.value = '{}';
                    break;
            }
        }
        
        async function testAPI(event) {
            event.preventDefault();
            
            const endpoint = document.getElementById('endpoint').value;
            const method = document.getElementById('method').value;
            const testData = document.getElementById('testData').value;
            
            // 显示结果区域
            const resultSection = document.getElementById('result-section');
            const resultStatus = document.getElementById('result-status');
            const resultContent = document.getElementById('result-content');
            
            resultSection.style.display = 'block';
            resultStatus.className = 'result-status';
            resultStatus.textContent = '测试中...';
            resultContent.textContent = '正在发送请求...';
            
            const startTime = Date.now();
            
            try {
                // 发送测试请求
                const response = await fetch('/admin/tts-generator/test-api/', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/x-www-form-urlencoded',
                        'X-CSRFToken': getCookie('csrftoken')
                    },
                    body: new URLSearchParams({
                        'endpoint': endpoint,
                        'method': method,
                        'test_data': testData
                    })
                });
                
                const result = await response.json();
                const responseTime = Date.now() - startTime;
                
                if (result.success) {
                    resultStatus.className = 'result-status status-success';
                    resultStatus.textContent = `成功 (${result.response_time})`;
                    resultContent.textContent = JSON.stringify(result.response_data, null, 2);
                } else {
                    resultStatus.className = 'result-status status-error';
                    resultStatus.textContent = '失败';
                    resultContent.textContent = result.error || '未知错误';
                }
                
                // 添加到测试历史
                addToHistory(endpoint, method, result.success, responseTime);
                
            } catch (error) {
                const responseTime = Date.now() - startTime;
                resultStatus.className = 'result-status status-error';
                resultStatus.textContent = '网络错误';
                resultContent.textContent = `错误: ${error.message}`;
                
                addToHistory(endpoint, method, false, responseTime);
            }
        }
        
        function addToHistory(endpoint, method, success, responseTime) {
            const historyItem = {
                endpoint,
                method,
                success,
                responseTime,
                timestamp: new Date().toLocaleString()
            };
            
            testHistory.unshift(historyItem);
            if (testHistory.length > 10) {
                testHistory.pop();
            }
            
            updateHistoryDisplay();
        }
        
        function updateHistoryDisplay() {
            const historyList = document.getElementById('test-history');
            
            if (testHistory.length === 0) {
                historyList.innerHTML = '<li class="test-history-item"><span>暂无测试记录</span><span>-</span></li>';
                return;
            }
            
            historyList.innerHTML = testHistory.map(item => `
                <li class="test-history-item">
                    <span>
                        <span class="endpoint-method ${item.method.toLowerCase() === 'get' ? 'method-get' : 'method-post'}">${item.method}</span>
                        ${item.endpoint}
                        <span style="color: ${item.success ? '#52c41a' : '#ff4d4f'};">
                            ${item.success ? '✓' : '✗'}
                        </span>
                    </span>
                    <span style="font-size: 12px; color: #8c8c8c;">${item.timestamp}</span>
                </li>
            `).join('');
        }
        
        function getCookie(name) {
            let cookieValue = null;
            if (document.cookie && document.cookie !== '') {
                const cookies = document.cookie.split(';');
                for (let i = 0; i < cookies.length; i++) {
                    const cookie = cookies[i].trim();
                    if (cookie.substring(0, name.length + 1) === (name + '=')) {
                        cookieValue = decodeURIComponent(cookie.substring(name.length + 1));
                        break;
                    }
                }
            }
            return cookieValue;
        }
        
        // 页面加载完成后默认选择SFT接口
        document.addEventListener('DOMContentLoaded', function() {
            selectEndpoint('sft');
        });
    </script>
</body>
</html>
