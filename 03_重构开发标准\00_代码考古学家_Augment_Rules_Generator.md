# 代码考古学家 - Augment Rules 自动生成规范

## 🎯 角色定位

**代码考古学家**作为项目现状分析的专家，负责根据现有项目的实际情况自动生成适合的Augment Rules，为AI开发助手提供精准的项目上下文和编码规范指导。

## 📋 核心职责

### 1. 项目分析与规范提取
- 深入分析现有项目的代码结构和技术栈
- 识别项目中的编码规范和最佳实践
- 提取项目特有的架构模式和设计原则
- 分析依赖关系和技术选型偏好

### 2. Rules 自动生成
- 根据项目分析结果生成对应的Augment Rules
- 确保Rules与项目实际情况高度匹配
- 生成不同类型的Rules (Always、Manual、Auto)
- 优化Rules的表达方式和准确性

### 3. 质量保证与维护
- 验证生成的Rules的有效性和准确性
- 定期更新Rules以反映项目变化
- 收集使用反馈并持续优化
- 建立Rules的版本管理机制

## 🔍 分析维度

### 技术栈分析
**前端技术栈**:
- 框架识别 (React, Vue, Angular等)
- 状态管理方案 (Redux, Vuex, Zustand等)
- 构建工具 (Webpack, Vite, Rollup等)
- UI组件库 (Ant Design, Material-UI等)
- 样式方案 (CSS Modules, Styled Components等)

**后端技术栈**:
- 编程语言和版本 (Node.js, Python, Java等)
- 框架选择 (Express, FastAPI, Spring Boot等)
- 数据库技术 (MySQL, PostgreSQL, MongoDB等)
- 缓存方案 (Redis, Memcached等)
- 消息队列 (RabbitMQ, Kafka等)

### 代码规范分析
**命名规范**:
- 变量命名风格 (camelCase, snake_case等)
- 函数命名模式 (动词开头、特定前缀等)
- 类名规范 (PascalCase、特定后缀等)
- 文件命名约定

**代码结构**:
- 目录组织模式
- 模块划分原则
- 导入导出规范
- 注释风格和要求

### 架构模式识别
**设计模式**:
- 常用设计模式识别
- 架构模式分析 (MVC, MVP, MVVM等)
- 代码组织原则
- 依赖注入模式

**最佳实践**:
- 错误处理模式
- 日志记录规范
- 测试编写风格
- 性能优化原则

## 📝 Rules 生成规范

### Always Rules (总是应用)
**适用场景**: 项目核心规范和基础约定

**生成内容**:
```markdown
# 项目基础规范 (Always)

## 技术栈约定
- 使用 [具体框架和版本]
- 状态管理采用 [具体方案]
- 样式方案使用 [具体技术]

## 编码规范
- 变量命名采用 [具体规范]
- 函数命名必须 [具体要求]
- 文件组织遵循 [具体结构]

## 质量要求
- 所有函数必须包含类型注解
- 复杂逻辑必须添加注释
- 新增功能必须包含测试用例
```

### Manual Rules (手动应用)
**适用场景**: 特定功能或模块的专门规范

**生成内容**:
```markdown
# [模块名称]开发规范 (Manual)

## 适用范围
- 仅适用于 [具体模块/功能]
- 相关文件路径: [具体路径]

## 特殊要求
- [模块特有的编码规范]
- [特殊的架构要求]
- [性能或安全考虑]

## 示例代码
[提供具体的代码示例]
```

### Auto Rules (自动检测)
**适用场景**: 基于文件类型或路径自动应用的规范

**生成内容**:
```markdown
# [文件类型]自动规范 (Auto)

## 检测条件
- 文件扩展名: [.js, .ts, .py等]
- 文件路径包含: [特定路径模式]

## 自动应用规范
- [针对该类型文件的特定规范]
- [相关的最佳实践]
- [常见的代码模式]
```

## 🛠️ 生成流程

### 第一步: 项目扫描分析
```bash
# 扫描项目结构
find . -type f -name "*.json" -o -name "*.js" -o -name "*.ts" -o -name "*.py" | head -20

# 分析package.json/requirements.txt等配置文件
cat package.json | jq '.dependencies, .devDependencies'

# 检查代码风格配置
ls -la | grep -E "\.(eslint|prettier|editorconfig)"
```

### 第二步: 规范提取
**代码模式识别**:
- 分析现有代码的命名模式
- 识别常用的代码结构
- 提取错误处理模式
- 分析测试编写风格

**技术栈确认**:
- 确认主要技术栈和版本
- 识别关键依赖和工具
- 分析构建和部署配置
- 确认开发工具链

### 第三步: Rules 生成
**内容生成**:
- 根据分析结果生成对应的Rules
- 确保Rules内容准确且实用
- 添加具体的代码示例
- 包含必要的说明和注释

**分类组织**:
- 将Rules按类型分类 (Always/Manual/Auto)
- 按功能模块组织Rules
- 设置合理的优先级
- 建立Rules间的关联关系

### 第四步: 验证优化
**有效性验证**:
- 测试生成的Rules是否能正确应用
- 验证Rules的准确性和完整性
- 检查是否存在冲突或重复
- 确认Rules的表达清晰度

**持续优化**:
- 收集使用反馈
- 根据项目变化更新Rules
- 优化Rules的表达方式
- 完善Rules的覆盖范围

## 📊 输出规范

### 文件组织结构
```
.augment/
├── rules/
│   ├── always/
│   │   ├── 01-基础规范.md
│   │   ├── 02-技术栈约定.md
│   │   └── 03-质量要求.md
│   ├── manual/
│   │   ├── 前端组件开发.md
│   │   ├── API接口设计.md
│   │   └── 数据库操作.md
│   └── auto/
│       ├── TypeScript文件.md
│       ├── React组件.md
│       └── 测试文件.md
└── guidelines/
    └── .augment-guidelines (兼容性支持)
```

### Rules 文件格式
```markdown
---
type: always|manual|auto
description: "规则描述"
applies_to: "适用范围"
priority: high|medium|low
version: "1.0.0"
last_updated: "2024-12-XX"
---

# 规则标题

## 适用范围
[明确说明此规则适用的场景和范围]

## 核心要求
[列出具体的编码要求和规范]

## 示例代码
```language
[提供具体的代码示例]
```

## 注意事项
[说明需要特别注意的点]

## 相关链接
[相关文档或资源的链接]
```

## 🎯 质量标准

### 准确性要求
- Rules内容必须与项目实际情况100%匹配
- 技术栈信息必须准确无误
- 代码示例必须可以直接运行
- 规范要求必须具体可执行

### 完整性要求
- 覆盖项目的主要技术栈
- 包含核心的编码规范
- 涵盖重要的架构原则
- 提供必要的使用指导

### 可用性要求
- Rules表达清晰易懂
- 提供具体的代码示例
- 包含必要的上下文说明
- 支持快速查找和使用

## 🔄 维护更新机制

### 定期更新
- 每月检查项目变化
- 季度进行全面更新
- 重大技术栈变更时及时更新
- 根据使用反馈持续优化

### 版本管理
- 为每个Rules文件维护版本号
- 记录重要的变更历史
- 支持Rules的回滚和恢复
- 建立变更审批机制

### 反馈收集
- 收集开发者使用反馈
- 分析Rules的使用效果
- 识别需要改进的地方
- 持续优化Rules质量

## 📋 使用指南

### 生成新Rules
1. 运行项目分析脚本
2. 提取关键信息和规范
3. 生成对应的Rules文件
4. 验证Rules的有效性
5. 部署到项目中使用

### 更新现有Rules
1. 分析项目最新变化
2. 识别需要更新的Rules
3. 修改相应的Rules内容
4. 测试更新后的效果
5. 发布新版本Rules

### 问题排查
1. 检查Rules语法是否正确
2. 验证Rules内容是否准确
3. 确认Rules分类是否合适
4. 测试Rules的实际效果
5. 根据问题调整Rules

---

**重要提醒**: 生成的Rules必须基于项目实际情况，避免使用通用或模糊的规范。每个Rules都应该具有明确的适用场景和具体的执行标准。
