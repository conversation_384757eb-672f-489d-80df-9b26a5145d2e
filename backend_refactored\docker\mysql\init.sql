-- CosyVoice数据库初始化脚本

-- 设置字符集
SET NAMES utf8mb4;
SET CHARACTER SET utf8mb4;

-- 创建数据库（如果不存在）
CREATE DATABASE IF NOT EXISTS cvapi
    CHARACTER SET utf8mb4
    COLLATE utf8mb4_unicode_ci;

-- 使用数据库
USE cvapi;

-- 创建用户（如果不存在）
CREATE USER IF NOT EXISTS 'cvapi'@'%' IDENTIFIED BY 'NsF3wEe75HNPDeYj';

-- 授权
GRANT ALL PRIVILEGES ON cvapi.* TO 'cvapi'@'%';

-- 刷新权限
FLUSH PRIVILEGES;

-- 设置数据库参数
SET GLOBAL innodb_buffer_pool_size = 268435456;  -- 256MB
SET GLOBAL max_connections = 200;
SET GLOBAL wait_timeout = 28800;
SET GLOBAL interactive_timeout = 28800;

-- 创建索引优化配置
SET GLOBAL innodb_file_per_table = ON;
SET GLOBAL innodb_flush_log_at_trx_commit = 2;

-- 日志配置
SET GLOBAL slow_query_log = ON;
SET GLOBAL long_query_time = 2;
SET GLOBAL log_queries_not_using_indexes = ON;
