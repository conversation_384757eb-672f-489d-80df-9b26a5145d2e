# Generated by Django 4.2.7 on 2025-07-24 03:28

from django.conf import settings
from django.db import migrations, models
import django.db.models.deletion


class Migration(migrations.Migration):
    initial = True

    dependencies = [
        ("files", "0001_initial"),
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
    ]

    operations = [
        migrations.AddField(
            model_name="fileoperation",
            name="user",
            field=models.ForeignKey(
                blank=True,
                null=True,
                on_delete=django.db.models.deletion.CASCADE,
                related_name="file_operations",
                to=settings.AUTH_USER_MODEL,
            ),
        ),
        migrations.AddField(
            model_name="audiofile",
            name="user",
            field=models.ForeignKey(
                blank=True,
                null=True,
                on_delete=django.db.models.deletion.CASCADE,
                related_name="audio_files",
                to=settings.AUTH_USER_MODEL,
            ),
        ),
        migrations.AddIndex(
            model_name="fileshare",
            index=models.Index(
                fields=["share_token"], name="file_shares_share_t_68bcb5_idx"
            ),
        ),
        migrations.AddIndex(
            model_name="fileshare",
            index=models.Index(
                fields=["expires_at"], name="file_shares_expires_1bc2a0_idx"
            ),
        ),
        migrations.AddIndex(
            model_name="fileoperation",
            index=models.Index(
                fields=["user", "created_at"], name="file_operat_user_id_af43f3_idx"
            ),
        ),
        migrations.AddIndex(
            model_name="fileoperation",
            index=models.Index(
                fields=["audio_file", "operation"],
                name="file_operat_audio_f_99b63f_idx",
            ),
        ),
        migrations.AddIndex(
            model_name="fileoperation",
            index=models.Index(
                fields=["created_at"], name="file_operat_created_0eae00_idx"
            ),
        ),
        migrations.AddIndex(
            model_name="audiofile",
            index=models.Index(
                fields=["user", "created_at"], name="audio_files_user_id_9b4fa3_idx"
            ),
        ),
        migrations.AddIndex(
            model_name="audiofile",
            index=models.Index(
                fields=["file_type"], name="audio_files_file_ty_3113a9_idx"
            ),
        ),
        migrations.AddIndex(
            model_name="audiofile",
            index=models.Index(
                fields=["expires_at"], name="audio_files_expires_65455e_idx"
            ),
        ),
        migrations.AddIndex(
            model_name="audiofile",
            index=models.Index(
                fields=["created_at"], name="audio_files_created_3eaddd_idx"
            ),
        ),
        migrations.AddIndex(
            model_name="audiofile",
            index=models.Index(
                fields=["filename"], name="audio_files_filenam_b213f1_idx"
            ),
        ),
    ]
