"""
认证相关URL配置
"""
from django.urls import path
from rest_framework_simplejwt.views import TokenRefreshView
from . import views

urlpatterns = [
    # 用户认证
    path('register/', views.UserRegistrationView.as_view(), name='user_register'),
    path('login/', views.UserLoginView.as_view(), name='user_login'),
    path('token/refresh/', TokenRefreshView.as_view(), name='token_refresh'),
    path('password/change/', views.PasswordChangeView.as_view(), name='password_change'),
    path('password/reset/', views.password_reset_request, name='password_reset_request'),
    path('password/reset/confirm/', views.password_reset_confirm, name='password_reset_confirm'),

    # API密钥管理
    path('api-keys/', views.APIKeyListCreateView.as_view(), name='api_key_list_create'),
    path('api-keys/<uuid:pk>/', views.APIKeyDetailView.as_view(), name='api_key_detail'),

    # 登录日志
    path('login-logs/', views.LoginLogListView.as_view(), name='login_log_list'),
]
