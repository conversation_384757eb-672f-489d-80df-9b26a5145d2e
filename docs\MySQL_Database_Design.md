# CosyVoice MySQL数据库设计文档 v1.0

**文档版本**: v1.0  
**创建日期**: 2025-01-23  
**负责人**: 技术架构师  
**项目代号**: CosyVoice-TaskManager  

---

## 1. 数据库设计概述

### 1.1 设计原则

**简洁高效**:
- 🎯 **表结构简洁**: 避免过度设计，保持表结构清晰
- 🎯 **索引合理**: 基于查询模式设计索引
- 🎯 **数据类型优化**: 选择合适的数据类型，节省存储空间

**兼容性优先**:
- 🛡️ **MySQL 8.0特性**: 充分利用JSON、CTE等新特性
- 🛡️ **向后兼容**: 保持与MySQL 5.7的基本兼容
- 🛡️ **标准SQL**: 优先使用标准SQL语法

### 1.2 技术选型说明

**MySQL 8.0+ 选择理由**:
```yaml
团队优势:
  - 团队MySQL经验丰富
  - 学习成本最低
  - 开发效率最高

技术优势:
  - JSON数据类型支持
  - 窗口函数支持
  - 公共表表达式(CTE)
  - 改进的优化器

运维优势:
  - 工具链成熟
  - 监控方案完善
  - 备份恢复简单
  - 社区支持充分
```

---

## 2. 数据库架构设计

### 2.1 数据库实例规划

**主从架构**:
```yaml
主库 (Master):
  - 处理所有写操作
  - 处理实时查询
  - 数据一致性保证

从库 (Slave):
  - 处理只读查询
  - 报表和分析查询
  - 备份和恢复

配置参数:
  - innodb_buffer_pool_size: 70%内存
  - max_connections: 1000
  - query_cache_size: 256MB
  - slow_query_log: 启用
```

### 2.2 数据库和表设计

**数据库结构**:
```sql
-- 创建数据库
CREATE DATABASE cosyvoice_taskmanager 
CHARACTER SET utf8mb4 
COLLATE utf8mb4_unicode_ci;

USE cosyvoice_taskmanager;
```

**核心表设计**:

### 2.3 用户管理表

```sql
-- 用户表
CREATE TABLE users (
    id BIGINT UNSIGNED AUTO_INCREMENT PRIMARY KEY,
    username VARCHAR(50) NOT NULL UNIQUE COMMENT '用户名',
    email VARCHAR(100) NOT NULL UNIQUE COMMENT '邮箱',
    password_hash VARCHAR(255) NOT NULL COMMENT '密码哈希',
    full_name VARCHAR(100) COMMENT '真实姓名',
    avatar_url VARCHAR(255) COMMENT '头像URL',
    role ENUM('admin', 'premium', 'basic', 'anonymous') DEFAULT 'basic' COMMENT '用户角色',
    status ENUM('active', 'inactive', 'suspended') DEFAULT 'active' COMMENT '账户状态',
    quota_limit INT UNSIGNED DEFAULT 1000 COMMENT '配额限制',
    quota_used INT UNSIGNED DEFAULT 0 COMMENT '已使用配额',
    last_login_at TIMESTAMP NULL COMMENT '最后登录时间',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    
    INDEX idx_username (username),
    INDEX idx_email (email),
    INDEX idx_role (role),
    INDEX idx_status (status),
    INDEX idx_created_at (created_at)
) ENGINE=InnoDB COMMENT='用户表';

-- API密钥表
CREATE TABLE api_keys (
    id BIGINT UNSIGNED AUTO_INCREMENT PRIMARY KEY,
    user_id BIGINT UNSIGNED NOT NULL COMMENT '用户ID',
    key_name VARCHAR(100) NOT NULL COMMENT '密钥名称',
    key_hash VARCHAR(255) NOT NULL UNIQUE COMMENT '密钥哈希',
    key_prefix VARCHAR(20) NOT NULL COMMENT '密钥前缀(显示用)',
    permissions JSON COMMENT '权限配置',
    expires_at TIMESTAMP NULL COMMENT '过期时间',
    last_used_at TIMESTAMP NULL COMMENT '最后使用时间',
    is_active BOOLEAN DEFAULT TRUE COMMENT '是否激活',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    
    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE,
    INDEX idx_user_id (user_id),
    INDEX idx_key_hash (key_hash),
    INDEX idx_expires_at (expires_at),
    INDEX idx_is_active (is_active)
) ENGINE=InnoDB COMMENT='API密钥表';
```

### 2.4 任务管理表

```sql
-- 任务表
CREATE TABLE tasks (
    id BIGINT UNSIGNED AUTO_INCREMENT PRIMARY KEY,
    task_uuid VARCHAR(36) NOT NULL UNIQUE COMMENT '任务UUID',
    user_id BIGINT UNSIGNED COMMENT '用户ID',
    task_type ENUM('sft', 'zero_shot', 'cross_lingual', 'instruct') NOT NULL COMMENT '任务类型',
    status ENUM('pending', 'processing', 'completed', 'failed', 'cancelled') DEFAULT 'pending' COMMENT '任务状态',
    priority INT DEFAULT 5 COMMENT '优先级(1-10)',
    
    -- 请求参数 (JSON格式)
    request_params JSON NOT NULL COMMENT '请求参数',
    
    -- 响应结果 (JSON格式)
    response_data JSON COMMENT '响应数据',
    
    -- 执行信息
    started_at TIMESTAMP NULL COMMENT '开始时间',
    completed_at TIMESTAMP NULL COMMENT '完成时间',
    processing_time_ms INT UNSIGNED COMMENT '处理时间(毫秒)',
    
    -- 错误信息
    error_code VARCHAR(20) COMMENT '错误码',
    error_message TEXT COMMENT '错误信息',
    
    -- 回调信息
    callback_url VARCHAR(500) COMMENT '回调URL',
    callback_status ENUM('pending', 'success', 'failed') COMMENT '回调状态',
    
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    
    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE SET NULL,
    INDEX idx_task_uuid (task_uuid),
    INDEX idx_user_id (user_id),
    INDEX idx_task_type (task_type),
    INDEX idx_status (status),
    INDEX idx_priority (priority),
    INDEX idx_created_at (created_at),
    INDEX idx_status_priority (status, priority)
) ENGINE=InnoDB COMMENT='任务表';

-- 任务执行日志表
CREATE TABLE task_logs (
    id BIGINT UNSIGNED AUTO_INCREMENT PRIMARY KEY,
    task_id BIGINT UNSIGNED NOT NULL COMMENT '任务ID',
    log_level ENUM('DEBUG', 'INFO', 'WARNING', 'ERROR') DEFAULT 'INFO' COMMENT '日志级别',
    message TEXT NOT NULL COMMENT '日志消息',
    context_data JSON COMMENT '上下文数据',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    
    FOREIGN KEY (task_id) REFERENCES tasks(id) ON DELETE CASCADE,
    INDEX idx_task_id (task_id),
    INDEX idx_log_level (log_level),
    INDEX idx_created_at (created_at)
) ENGINE=InnoDB COMMENT='任务执行日志表';
```

### 2.5 文件管理表

```sql
-- 音频文件表
CREATE TABLE audio_files (
    id BIGINT UNSIGNED AUTO_INCREMENT PRIMARY KEY,
    file_uuid VARCHAR(36) NOT NULL UNIQUE COMMENT '文件UUID',
    task_id BIGINT UNSIGNED COMMENT '关联任务ID',
    user_id BIGINT UNSIGNED COMMENT '用户ID',
    
    -- 文件信息
    original_filename VARCHAR(255) COMMENT '原始文件名',
    file_path VARCHAR(500) NOT NULL COMMENT '文件路径',
    file_size BIGINT UNSIGNED NOT NULL COMMENT '文件大小(字节)',
    file_hash VARCHAR(64) COMMENT '文件哈希(SHA256)',
    mime_type VARCHAR(100) DEFAULT 'audio/wav' COMMENT 'MIME类型',
    
    -- 音频属性
    duration_seconds DECIMAL(10,3) COMMENT '时长(秒)',
    sample_rate INT UNSIGNED COMMENT '采样率',
    channels TINYINT UNSIGNED COMMENT '声道数',
    bit_depth TINYINT UNSIGNED COMMENT '位深度',
    
    -- 访问控制
    is_public BOOLEAN DEFAULT FALSE COMMENT '是否公开',
    access_token VARCHAR(100) COMMENT '访问令牌',
    download_count INT UNSIGNED DEFAULT 0 COMMENT '下载次数',
    
    -- 生命周期
    expires_at TIMESTAMP NULL COMMENT '过期时间',
    last_accessed_at TIMESTAMP NULL COMMENT '最后访问时间',
    
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    
    FOREIGN KEY (task_id) REFERENCES tasks(id) ON DELETE SET NULL,
    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE SET NULL,
    INDEX idx_file_uuid (file_uuid),
    INDEX idx_task_id (task_id),
    INDEX idx_user_id (user_id),
    INDEX idx_file_hash (file_hash),
    INDEX idx_expires_at (expires_at),
    INDEX idx_created_at (created_at)
) ENGINE=InnoDB COMMENT='音频文件表';

-- 文件访问日志表
CREATE TABLE file_access_logs (
    id BIGINT UNSIGNED AUTO_INCREMENT PRIMARY KEY,
    file_id BIGINT UNSIGNED NOT NULL COMMENT '文件ID',
    user_id BIGINT UNSIGNED COMMENT '用户ID',
    access_type ENUM('view', 'download', 'delete') NOT NULL COMMENT '访问类型',
    ip_address VARCHAR(45) COMMENT 'IP地址',
    user_agent TEXT COMMENT '用户代理',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    
    FOREIGN KEY (file_id) REFERENCES audio_files(id) ON DELETE CASCADE,
    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE SET NULL,
    INDEX idx_file_id (file_id),
    INDEX idx_user_id (user_id),
    INDEX idx_access_type (access_type),
    INDEX idx_created_at (created_at)
) ENGINE=InnoDB COMMENT='文件访问日志表';
```

### 2.6 系统配置表

```sql
-- 系统配置表
CREATE TABLE system_configs (
    id INT UNSIGNED AUTO_INCREMENT PRIMARY KEY,
    config_key VARCHAR(100) NOT NULL UNIQUE COMMENT '配置键',
    config_value JSON NOT NULL COMMENT '配置值',
    description TEXT COMMENT '配置描述',
    is_public BOOLEAN DEFAULT FALSE COMMENT '是否公开(API可访问)',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    
    INDEX idx_config_key (config_key),
    INDEX idx_is_public (is_public)
) ENGINE=InnoDB COMMENT='系统配置表';

-- 操作审计日志表
CREATE TABLE audit_logs (
    id BIGINT UNSIGNED AUTO_INCREMENT PRIMARY KEY,
    user_id BIGINT UNSIGNED COMMENT '用户ID',
    action VARCHAR(100) NOT NULL COMMENT '操作动作',
    resource_type VARCHAR(50) COMMENT '资源类型',
    resource_id VARCHAR(100) COMMENT '资源ID',
    old_values JSON COMMENT '变更前值',
    new_values JSON COMMENT '变更后值',
    ip_address VARCHAR(45) COMMENT 'IP地址',
    user_agent TEXT COMMENT '用户代理',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    
    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE SET NULL,
    INDEX idx_user_id (user_id),
    INDEX idx_action (action),
    INDEX idx_resource_type (resource_type),
    INDEX idx_created_at (created_at)
) ENGINE=InnoDB COMMENT='操作审计日志表';
```

---

## 3. 索引优化策略

### 3.1 查询模式分析

**高频查询场景**:
```sql
-- 1. 用户登录验证
SELECT id, password_hash, role, status FROM users 
WHERE username = ? OR email = ?;

-- 2. 任务状态查询
SELECT * FROM tasks 
WHERE user_id = ? AND status IN ('pending', 'processing') 
ORDER BY priority DESC, created_at ASC;

-- 3. 文件下载
SELECT file_path, file_size, mime_type FROM audio_files 
WHERE file_uuid = ? AND (is_public = TRUE OR user_id = ?);

-- 4. 用户配额检查
SELECT quota_limit, quota_used FROM users WHERE id = ?;
```

### 3.2 复合索引设计

```sql
-- 任务查询优化索引
CREATE INDEX idx_tasks_user_status_priority ON tasks(user_id, status, priority, created_at);

-- 文件访问优化索引
CREATE INDEX idx_files_user_created ON audio_files(user_id, created_at);

-- API密钥查询优化索引
CREATE INDEX idx_apikeys_user_active ON api_keys(user_id, is_active, expires_at);

-- 审计日志查询优化索引
CREATE INDEX idx_audit_user_action_time ON audit_logs(user_id, action, created_at);
```

### 3.3 JSON字段索引

```sql
-- 为JSON字段创建虚拟列和索引
ALTER TABLE tasks 
ADD COLUMN request_text_length INT GENERATED ALWAYS AS (JSON_LENGTH(JSON_EXTRACT(request_params, '$.tts_text'))) STORED,
ADD INDEX idx_request_text_length (request_text_length);

-- 任务类型快速查询
ALTER TABLE tasks 
ADD COLUMN speaker_id VARCHAR(50) GENERATED ALWAYS AS (JSON_UNQUOTE(JSON_EXTRACT(request_params, '$.spk_id'))) STORED,
ADD INDEX idx_speaker_id (speaker_id);
```

---

## 4. 数据迁移策略

### 4.1 现有数据映射

**文件数据映射**:
```yaml
现有文件命名: sft_20250123_14:30:25_001.wav
映射策略:
  - 解析文件名获取类型和时间
  - 生成UUID作为新文件标识
  - 创建默认用户归属
  - 建立任务记录关联
```

### 4.2 数据初始化脚本

```sql
-- 创建默认用户
INSERT INTO users (username, email, password_hash, role, quota_limit) VALUES
('admin', '<EMAIL>', '$2b$12$...', 'admin', 999999),
('system', '<EMAIL>', '$2b$12$...', 'admin', 999999),
('anonymous', '<EMAIL>', '$2b$12$...', 'anonymous', 100);

-- 初始化系统配置
INSERT INTO system_configs (config_key, config_value, description, is_public) VALUES
('max_file_size', '52428800', '最大文件大小(50MB)', TRUE),
('supported_formats', '["wav", "mp3", "flac"]', '支持的音频格式', TRUE),
('default_quota', '1000', '默认用户配额', FALSE),
('cleanup_days', '30', '文件清理天数', FALSE);
```

---

## 5. 性能优化建议

### 5.1 配置优化

```ini
# MySQL配置优化
[mysqld]
# 基础配置
default-storage-engine = InnoDB
character-set-server = utf8mb4
collation-server = utf8mb4_unicode_ci

# 内存配置
innodb_buffer_pool_size = 2G
innodb_log_file_size = 256M
innodb_log_buffer_size = 64M
query_cache_size = 256M

# 连接配置
max_connections = 1000
max_connect_errors = 100000
wait_timeout = 28800

# 日志配置
slow_query_log = 1
slow_query_log_file = /var/log/mysql/slow.log
long_query_time = 2
```

### 5.2 查询优化

**分页查询优化**:
```sql
-- 避免OFFSET大数值分页
-- 使用游标分页
SELECT * FROM tasks 
WHERE id > ? AND user_id = ? 
ORDER BY id LIMIT 20;
```

**JSON查询优化**:
```sql
-- 使用虚拟列代替JSON函数
SELECT * FROM tasks 
WHERE speaker_id = 'Chinese_Female' 
AND created_at >= '2025-01-01';
```

---

**文档状态**: ✅ 完成  
**审核状态**: 待技术评审  
**相关文档**: 
- [重构技术架构设计](Refactoring_Technical_Architecture.md)
- [数据迁移架构设计](Data_Migration_Architecture.md)
