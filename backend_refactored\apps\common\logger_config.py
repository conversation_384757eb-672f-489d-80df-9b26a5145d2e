#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
基于loguru的日志系统配置
参考：日志系统快速开始指南.md
"""

import os
import sys
from pathlib import Path
from loguru import logger
from django.conf import settings

# 获取项目根目录
BASE_DIR = Path(__file__).resolve().parent.parent.parent
LOGS_DIR = BASE_DIR / 'logs'

# 确保日志目录存在
LOGS_DIR.mkdir(exist_ok=True)

def setup_loguru():
    """
    配置loguru日志系统
    参考指南中的Python项目配置
    """
    # 移除默认处理器
    logger.remove()
    
    # 开发环境：彩色控制台输出
    if getattr(settings, 'DEBUG', False):
        logger.add(
            sys.stdout, 
            level="DEBUG",
            format="<green>{time:HH:mm:ss}</green> | <level>{level: <8}</level> | <cyan>{name}</cyan>:<cyan>{line}</cyan> | <level>{message}</level>",
            colorize=True
        )
    else:
        # 生产环境：简化控制台输出
        logger.add(
            sys.stdout,
            level="INFO", 
            format="{time:YYYY-MM-DD HH:mm:ss} | {level: <8} | {name}:{line} | {message}",
            colorize=False
        )
    
    # 应用主日志：按天轮转
    logger.add(
        LOGS_DIR / "app_{time:YYYY-MM-DD}.log",
        level="DEBUG",
        rotation="1 day",
        retention="30 days",
        compression="zip",
        format="{time:YYYY-MM-DD HH:mm:ss.SSS} | {level: <8} | {name}:{function}:{line} | {message}",
        encoding="utf-8"
    )
    
    # API日志：专门记录API调用
    logger.add(
        LOGS_DIR / "api_{time:YYYY-MM-DD}.log",
        level="DEBUG",
        rotation="1 day", 
        retention="30 days",
        compression="zip",
        format="{time:YYYY-MM-DD HH:mm:ss.SSS} | {level: <8} | API | {message}",
        filter=lambda record: "API" in record["extra"].get("category", ""),
        encoding="utf-8"
    )
    
    # 性能日志：记录性能监控数据
    logger.add(
        LOGS_DIR / "performance_{time:YYYY-MM-DD}.log",
        level="DEBUG",
        rotation="1 day",
        retention="30 days", 
        compression="zip",
        format="{time:YYYY-MM-DD HH:mm:ss.SSS} | {level: <8} | PERF | {message}",
        filter=lambda record: "PERFORMANCE" in record["extra"].get("category", ""),
        encoding="utf-8"
    )
    
    # 安全日志：记录安全相关事件
    logger.add(
        LOGS_DIR / "security_{time:YYYY-MM-DD}.log",
        level="INFO",
        rotation="1 day",
        retention="90 days",  # 安全日志保留更久
        compression="zip",
        format="{time:YYYY-MM-DD HH:mm:ss.SSS} | {level: <8} | SEC | {message}",
        filter=lambda record: "SECURITY" in record["extra"].get("category", ""),
        encoding="utf-8"
    )
    
    # 任务日志：记录任务执行情况
    logger.add(
        LOGS_DIR / "tasks_{time:YYYY-MM-DD}.log",
        level="DEBUG",
        rotation="1 day",
        retention="30 days",
        compression="zip",
        format="{time:YYYY-MM-DD HH:mm:ss.SSS} | {level: <8} | TASK | {message}",
        filter=lambda record: "TASK" in record["extra"].get("category", ""),
        encoding="utf-8"
    )
    
    # 错误日志：单独记录错误和异常
    logger.add(
        LOGS_DIR / "error_{time:YYYY-MM-DD}.log",
        level="ERROR",
        rotation="1 day",
        retention="90 days",  # 错误日志保留更久
        compression="zip",
        format="{time:YYYY-MM-DD HH:mm:ss.SSS} | {level: <8} | {name}:{function}:{line} | {message} | {extra}",
        encoding="utf-8"
    )
    
    logger.info("Loguru日志系统初始化完成", extra={"category": "SYSTEM"})

def get_logger(name: str = None):
    """
    获取logger实例
    """
    if name:
        return logger.bind(name=name)
    return logger

# 创建专门的日志记录器
api_logger = logger.bind(category="API")
performance_logger = logger.bind(category="PERFORMANCE") 
security_logger = logger.bind(category="SECURITY")
tasks_logger = logger.bind(category="TASK")
cosyvoice_logger = logger.bind(category="COSYVOICE")

# 导出
__all__ = [
    'setup_loguru',
    'get_logger',
    'logger',
    'api_logger',
    'performance_logger',
    'security_logger', 
    'tasks_logger',
    'cosyvoice_logger',
]
