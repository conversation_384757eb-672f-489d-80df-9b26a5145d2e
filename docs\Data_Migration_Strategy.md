# CosyVoice数据迁移策略

**文档版本**: v1.0  
**创建日期**: 2025-01-23  
**迁移范围**: 现有音频文件、配置数据、用户数据  
**迁移周期**: 4周 (与重构第一阶段并行)  

---

## 1. 迁移概述

### 1.1 迁移目标
- **数据完整性**: 确保所有现有数据100%完整迁移
- **业务连续性**: 迁移过程中服务不中断
- **数据安全**: 迁移过程中数据不丢失、不泄露
- **向后兼容**: 保持现有文件访问链接有效

### 1.2 迁移范围
```yaml
文件数据:
  - 生成的音频文件 (~10GB)
  - 参考音频文件 (~1GB)
  - 临时文件和缓存
  
配置数据:
  - 模型配置文件
  - 系统配置参数
  - 环境变量设置
  
运行时数据:
  - 当前会话信息
  - 临时处理状态
  - 缓存数据
```

### 1.3 迁移原则
- **零停机迁移**: 服务不中断
- **渐进式迁移**: 分批次进行，降低风险
- **可回滚**: 每个步骤都可以回滚
- **数据验证**: 迁移后完整性验证

---

## 2. 现状数据分析

### 2.1 文件系统现状
```yaml
音频文件存储:
  位置: ./generated_audio/
  文件数量: ~1000个文件
  总大小: ~10GB
  文件格式: WAV (16-bit, 22050Hz)
  命名规则: {prefix}_{timestamp}.wav
  
参考音频:
  位置: ./asset/
  文件数量: ~50个文件
  总大小: ~1GB
  文件格式: WAV, MP3, FLAC
  
临时文件:
  位置: /tmp/, ./cache/
  文件数量: 变动
  总大小: ~1GB
  清理策略: 手动清理
```

### 2.2 配置数据现状
```yaml
模型配置:
  位置: ./pretrained_models/*/cosyvoice.yaml
  数量: 5个模型配置
  格式: YAML配置文件
  
系统配置:
  位置: 代码中硬编码
  内容: 端口、路径、参数等
  格式: Python变量
  
环境配置:
  位置: 环境变量、命令行参数
  内容: 模型路径、服务配置
  格式: 环境变量
```

### 2.3 数据依赖关系
```yaml
文件访问链接:
  - HTTP下载链接: /download/{filename}
  - 静态文件链接: /asset/{filename}
  - 绝对路径引用: 返回结果中的file_path
  
配置依赖:
  - 模型文件路径依赖
  - 输出目录路径依赖
  - 静态资源路径依赖
```

---

## 3. 目标架构设计

### 3.1 新数据架构
```yaml
数据库存储:
  用户数据: MySQL
  任务记录: MySQL
  文件元数据: MySQL
  缓存数据: Redis
  
文件存储:
  音频文件: 文件系统 + 数据库索引
  参考文件: 文件系统 + 数据库索引
  临时文件: 文件系统 + 自动清理
  
配置管理:
  系统配置: 配置文件 + 环境变量
  用户配置: 数据库存储
  模型配置: 文件系统 + 数据库索引
```

### 3.2 数据模型设计
```sql
-- 用户表
CREATE TABLE users (
    id CHAR(36) PRIMARY KEY DEFAULT (UUID()),
    username VARCHAR(50) UNIQUE,
    email VARCHAR(100) UNIQUE,
    api_key VARCHAR(64) UNIQUE,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- 音频文件表
CREATE TABLE audio_files (
    id CHAR(36) PRIMARY KEY DEFAULT (UUID()),
    user_id CHAR(36) NOT NULL,
    original_filename VARCHAR(255),
    stored_filename VARCHAR(255),
    file_path VARCHAR(500),
    file_size BIGINT,
    duration FLOAT,
    sample_rate INTEGER,
    created_at TIMESTAMP,
    legacy_path VARCHAR(500), -- 兼容旧路径
    FOREIGN KEY (user_id) REFERENCES users(id)
);

-- 任务表
CREATE TABLE tasks (
    id CHAR(36) PRIMARY KEY DEFAULT (UUID()),
    user_id CHAR(36) NOT NULL,
    task_type VARCHAR(20),
    status VARCHAR(20),
    request_data JSON,
    audio_file_id CHAR(36),
    created_at TIMESTAMP,
    FOREIGN KEY (user_id) REFERENCES users(id),
    FOREIGN KEY (audio_file_id) REFERENCES audio_files(id)
);
```

### 3.3 文件组织结构
```
新文件结构:
├── data/
│   ├── users/
│   │   └── {user_id}/
│   │       ├── audio/
│   │       │   └── {file_id}.wav
│   │       └── uploads/
│   │           └── {file_id}.{ext}
│   ├── shared/
│   │   └── reference/
│   │       └── {file_id}.{ext}
│   └── temp/
│       └── {session_id}/
└── legacy/
    ├── generated_audio/  # 原有文件保留
    └── asset/           # 原有文件保留
```

---

## 4. 迁移实施计划

### 4.1 迁移阶段规划

#### 阶段零: MySQL环境准备
```yaml
MySQL安装配置:
  - MySQL 8.0+ 安装和配置
  - 字符集设置为utf8mb4
  - InnoDB引擎优化配置
  - JSON数据类型支持验证

连接库准备:
  - Python mysql-connector-python
  - SQLAlchemy MySQL驱动
  - 连接池配置优化
```

#### 阶段一: 准备阶段 (Week 1)
```yaml
数据库准备:
  - 创建新数据库结构
  - 设置数据库连接和权限
  - 创建必要的索引
  
文件系统准备:
  - 创建新的目录结构
  - 设置文件权限和访问控制
  - 准备迁移脚本和工具
  
备份准备:
  - 完整备份现有数据
  - 验证备份完整性
  - 准备回滚方案
```

#### 阶段二: 数据迁移 (Week 2)
```yaml
用户数据迁移:
  - 为现有使用创建默认用户
  - 生成API密钥
  - 设置用户权限和配额
  
文件数据迁移:
  - 扫描现有音频文件
  - 创建文件元数据记录
  - 建立新旧路径映射关系
  
配置数据迁移:
  - 提取硬编码配置到配置文件
  - 迁移模型配置到数据库
  - 设置环境变量和系统配置
```

#### 阶段三: 兼容性处理 (Week 3)
```yaml
API兼容性:
  - 实现旧API路径的重定向
  - 保持响应格式兼容
  - 添加兼容性中间件
  
文件访问兼容性:
  - 实现旧文件路径的映射
  - 保持下载链接有效
  - 添加路径转换逻辑
  
功能兼容性:
  - 保持现有功能完全可用
  - 添加新功能的向后兼容
  - 实现渐进式功能启用
```

#### 阶段四: 验证和优化 (Week 4)
```yaml
数据验证:
  - 验证数据迁移完整性
  - 检查文件访问正确性
  - 测试API功能兼容性
  
性能优化:
  - 数据库查询优化
  - 文件访问性能优化
  - 缓存策略实施
  
清理工作:
  - 清理临时迁移数据
  - 优化存储空间使用
  - 完善监控和日志
```

### 4.2 迁移时间表
```yaml
Week 1 (准备阶段):
  Day 1-2: 数据库结构创建和测试
  Day 3-4: 文件系统准备和脚本开发
  Day 5-7: 数据备份和验证

Week 2 (数据迁移):
  Day 1-2: 用户数据和权限迁移
  Day 3-5: 文件数据迁移和索引
  Day 6-7: 配置数据迁移和验证

Week 3 (兼容性处理):
  Day 1-3: API兼容性实现
  Day 4-5: 文件访问兼容性
  Day 6-7: 功能兼容性测试

Week 4 (验证优化):
  Day 1-3: 全面数据验证
  Day 4-5: 性能优化和调试
  Day 6-7: 清理和文档更新
```

---

## 5. 迁移技术方案

### 5.1 数据迁移脚本

#### 5.1.1 文件迁移脚本
```python
#!/usr/bin/env python3
"""
音频文件迁移脚本
"""
import os
import shutil
import hashlib
from pathlib import Path
from datetime import datetime
import mysql.connector
import uuid

class AudioFileMigrator:
    def __init__(self, source_dir, target_dir, db_conn):
        self.source_dir = Path(source_dir)
        self.target_dir = Path(target_dir)
        self.db_conn = db_conn
        
    def migrate_files(self):
        """迁移所有音频文件"""
        for audio_file in self.source_dir.glob("*.wav"):
            self.migrate_single_file(audio_file)
            
    def migrate_single_file(self, file_path):
        """迁移单个文件"""
        # 生成新的文件ID
        file_id = str(uuid.uuid4())
        
        # 计算文件信息
        file_info = self.get_file_info(file_path)
        
        # 复制文件到新位置
        new_path = self.target_dir / "legacy" / file_path.name
        shutil.copy2(file_path, new_path)
        
        # 记录到数据库
        self.record_file_metadata(file_id, file_path, new_path, file_info)
        
    def get_file_info(self, file_path):
        """获取文件信息"""
        stat = file_path.stat()
        return {
            'size': stat.st_size,
            'created_at': datetime.fromtimestamp(stat.st_ctime),
            'modified_at': datetime.fromtimestamp(stat.st_mtime)
        }
        
    def record_file_metadata(self, file_id, old_path, new_path, file_info):
        """记录文件元数据到数据库"""
        cursor = self.db_conn.cursor()
        cursor.execute("""
            INSERT INTO audio_files (
                id, original_filename, stored_filename, 
                file_path, legacy_path, file_size, created_at
            ) VALUES (%s, %s, %s, %s, %s, %s, %s)
        """, (
            file_id, old_path.name, new_path.name,
            str(new_path), str(old_path), 
            file_info['size'], file_info['created_at']
        ))
        self.db_conn.commit()
```

#### 5.1.2 用户数据迁移脚本
```python
#!/usr/bin/env python3
"""
用户数据迁移脚本
"""
import uuid
import secrets
import mysql.connector
from datetime import datetime

class UserMigrator:
    def __init__(self, db_conn):
        self.db_conn = db_conn
        
    def create_default_users(self):
        """创建默认用户"""
        # 创建系统默认用户
        default_user = self.create_user(
            username="legacy_user",
            email="<EMAIL>",
            role="admin"
        )
        
        # 创建匿名用户
        anonymous_user = self.create_user(
            username="anonymous",
            email="<EMAIL>", 
            role="basic"
        )
        
        return default_user, anonymous_user
        
    def create_user(self, username, email, role="basic"):
        """创建用户"""
        user_id = str(uuid.uuid4())
        api_key = self.generate_api_key()
        
        cursor = self.db_conn.cursor()
        cursor.execute("""
            INSERT INTO users (
                id, username, email, api_key, role, created_at
            ) VALUES (%s, %s, %s, %s, %s, %s)
        """, (
            user_id, username, email, api_key, role, datetime.now()
        ))
        self.db_conn.commit()
        
        return {
            'id': user_id,
            'username': username,
            'email': email,
            'api_key': api_key
        }
        
    def generate_api_key(self):
        """生成API密钥"""
        return secrets.token_urlsafe(32)
```

### 5.2 兼容性处理

#### 5.2.1 路径映射中间件
```python
from fastapi import Request, HTTPException
import os

class LegacyPathMiddleware:
    """处理旧路径的兼容性中间件"""
    
    def __init__(self, app):
        self.app = app
        
    async def __call__(self, scope, receive, send):
        if scope["type"] == "http":
            request = Request(scope, receive)
            
            # 处理旧的下载路径
            if request.url.path.startswith("/download/"):
                filename = request.url.path.split("/")[-1]
                new_path = await self.map_legacy_file(filename)
                if new_path:
                    # 重定向到新路径
                    scope["path"] = new_path
                    
        await self.app(scope, receive, send)
        
    async def map_legacy_file(self, filename):
        """映射旧文件路径到新路径"""
        # 从数据库查询文件映射
        # 返回新的文件路径
        pass
```

#### 5.2.2 API兼容性包装器
```python
from fastapi import APIRouter, Depends
from typing import Optional

# 保持原有API接口不变
@app.post("/inference_sft")
async def inference_sft_legacy(
    tts_text: str = Form(...),
    spk_id: str = Form("中文女"),
    # 新增可选认证参数
    api_key: Optional[str] = Header(None),
    user_id: Optional[str] = Depends(get_user_from_api_key)
):
    """
    保持原有API完全兼容
    内部增加用户识别和任务记录
    """
    # 如果没有提供认证信息，使用默认用户
    if not user_id:
        user_id = get_default_user_id()
    
    # 调用原有推理逻辑
    result = await original_inference_sft(tts_text, spk_id)
    
    # 记录任务信息（异步，不影响响应）
    await record_task_async(user_id, "sft", {
        "tts_text": tts_text,
        "spk_id": spk_id
    }, result)
    
    return result
```

---

## 6. 数据验证策略

### 6.1 迁移前验证
```yaml
数据完整性检查:
  - 统计现有文件数量和大小
  - 验证文件格式和完整性
  - 检查配置文件语法正确性
  
系统状态检查:
  - 确认系统正常运行
  - 检查磁盘空间充足
  - 验证数据库连接正常
```

### 6.2 迁移中验证
```yaml
实时监控:
  - 监控迁移进度和速度
  - 检查错误日志和异常
  - 验证数据库写入正确性
  
增量验证:
  - 每批次迁移后验证
  - 对比源文件和目标文件
  - 检查数据库记录一致性
```

### 6.3 迁移后验证
```yaml
完整性验证:
  - 对比迁移前后文件数量
  - 验证文件MD5校验和
  - 检查数据库记录完整性
  
功能验证:
  - 测试所有API接口功能
  - 验证文件下载链接有效
  - 检查新功能正常工作
  
性能验证:
  - 测试API响应时间
  - 验证文件访问速度
  - 检查数据库查询性能
```

---

## 7. 回滚方案

### 7.1 回滚触发条件
```yaml
数据问题:
  - 数据丢失或损坏
  - 迁移验证失败
  - 数据不一致
  
功能问题:
  - API功能异常
  - 文件访问失败
  - 性能严重下降
  
用户反馈:
  - 大量用户投诉
  - 业务中断报告
  - 关键用户要求回滚
```

### 7.2 回滚步骤
```yaml
紧急回滚 (30分钟内):
  1. 停止新系统服务
  2. 启动备份系统
  3. 恢复DNS指向
  4. 通知用户和团队
  
完整回滚 (2小时内):
  1. 恢复原始文件系统
  2. 恢复原始配置
  3. 清理迁移数据
  4. 验证系统功能
  
数据回滚 (4小时内):
  1. 从备份恢复数据
  2. 验证数据完整性
  3. 重新启动服务
  4. 全面功能测试
```

### 7.3 回滚验证
```yaml
功能验证:
  - 所有API接口正常
  - 文件下载链接有效
  - Web界面功能正常
  
数据验证:
  - 文件数量和大小正确
  - 配置参数恢复正确
  - 用户数据完整
  
性能验证:
  - 响应时间恢复正常
  - 系统资源使用正常
  - 并发处理能力正常
```

---

## 8. 风险控制

### 8.1 技术风险
```yaml
数据丢失风险:
  - 多重备份策略
  - 实时同步验证
  - 分批次迁移降低影响
  
性能影响风险:
  - 非高峰时间迁移
  - 渐进式切换
  - 性能监控和调优
  
兼容性风险:
  - 充分的兼容性测试
  - 渐进式功能启用
  - 快速回滚机制
```

### 8.2 业务风险
```yaml
服务中断风险:
  - 零停机迁移策略
  - 热切换机制
  - 故障快速恢复
  
用户体验风险:
  - 保持API完全兼容
  - 提前用户沟通
  - 实时技术支持
```

---

**迁移策略状态**: ✅ 完成  
**风险评估**: 可控  
**建议执行**: 分阶段实施，重点关注数据安全和业务连续性
