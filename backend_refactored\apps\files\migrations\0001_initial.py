# Generated by Django 4.2.7 on 2025-07-24 03:28

from django.db import migrations, models
import django.db.models.deletion
import uuid


class Migration(migrations.Migration):
    initial = True

    dependencies = []

    operations = [
        migrations.CreateModel(
            name="AudioFile",
            fields=[
                (
                    "id",
                    models.UUIDField(
                        default=uuid.uuid4,
                        editable=False,
                        primary_key=True,
                        serialize=False,
                    ),
                ),
                (
                    "created_at",
                    models.DateTimeField(auto_now_add=True, verbose_name="创建时间"),
                ),
                (
                    "updated_at",
                    models.DateTimeField(auto_now=True, verbose_name="更新时间"),
                ),
                (
                    "deleted_at",
                    models.DateTimeField(blank=True, null=True, verbose_name="删除时间"),
                ),
                ("is_active", models.BooleanField(default=True, verbose_name="是否激活")),
                ("filename", models.CharField(max_length=255, verbose_name="文件名")),
                (
                    "original_filename",
                    models.CharField(blank=True, max_length=255, verbose_name="原始文件名"),
                ),
                (
                    "file_type",
                    models.CharField(
                        choices=[
                            ("audio_input", "输入音频"),
                            ("audio_output", "输出音频"),
                            ("reference", "参考音频"),
                            ("temp", "临时文件"),
                        ],
                        max_length=20,
                        verbose_name="文件类型",
                    ),
                ),
                (
                    "storage_type",
                    models.CharField(
                        choices=[
                            ("local", "本地存储"),
                            ("oss", "阿里云OSS"),
                            ("s3", "Amazon S3"),
                        ],
                        default="local",
                        max_length=10,
                        verbose_name="存储类型",
                    ),
                ),
                ("file_path", models.CharField(max_length=500, verbose_name="文件路径")),
                (
                    "file_url",
                    models.URLField(blank=True, max_length=500, verbose_name="文件URL"),
                ),
                ("file_size", models.BigIntegerField(verbose_name="文件大小(字节)")),
                (
                    "mime_type",
                    models.CharField(blank=True, max_length=100, verbose_name="MIME类型"),
                ),
                (
                    "duration",
                    models.FloatField(blank=True, null=True, verbose_name="时长(秒)"),
                ),
                (
                    "sample_rate",
                    models.IntegerField(blank=True, null=True, verbose_name="采样率"),
                ),
                ("channels", models.IntegerField(default=1, verbose_name="声道数")),
                (
                    "bit_rate",
                    models.IntegerField(blank=True, null=True, verbose_name="比特率"),
                ),
                ("is_public", models.BooleanField(default=False, verbose_name="是否公开")),
                ("access_count", models.IntegerField(default=0, verbose_name="访问次数")),
                (
                    "last_accessed",
                    models.DateTimeField(blank=True, null=True, verbose_name="最后访问时间"),
                ),
                (
                    "expires_at",
                    models.DateTimeField(blank=True, null=True, verbose_name="过期时间"),
                ),
                ("auto_delete", models.BooleanField(default=True, verbose_name="自动删除")),
                (
                    "metadata",
                    models.JSONField(blank=True, default=dict, verbose_name="元数据"),
                ),
                ("tags", models.JSONField(blank=True, default=list, verbose_name="标签")),
            ],
            options={
                "verbose_name": "音频文件",
                "verbose_name_plural": "音频文件",
                "db_table": "audio_files",
                "ordering": ["-created_at"],
            },
        ),
        migrations.CreateModel(
            name="FileShare",
            fields=[
                (
                    "id",
                    models.UUIDField(
                        default=uuid.uuid4,
                        editable=False,
                        primary_key=True,
                        serialize=False,
                    ),
                ),
                (
                    "created_at",
                    models.DateTimeField(auto_now_add=True, verbose_name="创建时间"),
                ),
                (
                    "updated_at",
                    models.DateTimeField(auto_now=True, verbose_name="更新时间"),
                ),
                (
                    "deleted_at",
                    models.DateTimeField(blank=True, null=True, verbose_name="删除时间"),
                ),
                ("is_active", models.BooleanField(default=True, verbose_name="是否激活")),
                (
                    "share_token",
                    models.CharField(max_length=255, unique=True, verbose_name="分享令牌"),
                ),
                (
                    "password",
                    models.CharField(blank=True, max_length=100, verbose_name="访问密码"),
                ),
                (
                    "expires_at",
                    models.DateTimeField(blank=True, null=True, verbose_name="过期时间"),
                ),
                (
                    "max_downloads",
                    models.IntegerField(blank=True, null=True, verbose_name="最大下载次数"),
                ),
                ("download_count", models.IntegerField(default=0, verbose_name="下载次数")),
                (
                    "audio_file",
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.CASCADE,
                        related_name="shares",
                        to="files.audiofile",
                    ),
                ),
            ],
            options={
                "verbose_name": "文件分享",
                "verbose_name_plural": "文件分享",
                "db_table": "file_shares",
            },
        ),
        migrations.CreateModel(
            name="FileOperation",
            fields=[
                (
                    "id",
                    models.UUIDField(
                        default=uuid.uuid4,
                        editable=False,
                        primary_key=True,
                        serialize=False,
                    ),
                ),
                (
                    "created_at",
                    models.DateTimeField(auto_now_add=True, verbose_name="创建时间"),
                ),
                (
                    "updated_at",
                    models.DateTimeField(auto_now=True, verbose_name="更新时间"),
                ),
                (
                    "deleted_at",
                    models.DateTimeField(blank=True, null=True, verbose_name="删除时间"),
                ),
                ("is_active", models.BooleanField(default=True, verbose_name="是否激活")),
                (
                    "operation",
                    models.CharField(
                        choices=[
                            ("upload", "上传"),
                            ("download", "下载"),
                            ("delete", "删除"),
                            ("share", "分享"),
                            ("access", "访问"),
                        ],
                        max_length=20,
                        verbose_name="操作类型",
                    ),
                ),
                (
                    "ip_address",
                    models.GenericIPAddressField(
                        blank=True, null=True, verbose_name="IP地址"
                    ),
                ),
                ("user_agent", models.TextField(blank=True, verbose_name="用户代理")),
                (
                    "extra_data",
                    models.JSONField(blank=True, null=True, verbose_name="额外数据"),
                ),
                (
                    "audio_file",
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.CASCADE,
                        related_name="operations",
                        to="files.audiofile",
                    ),
                ),
            ],
            options={
                "verbose_name": "文件操作记录",
                "verbose_name_plural": "文件操作记录",
                "db_table": "file_operations",
                "ordering": ["-created_at"],
            },
        ),
    ]
