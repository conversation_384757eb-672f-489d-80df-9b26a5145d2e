---
description: 重构开发流程中各角色的提问示例，体现重构项目的特殊需求和工作重点
globs: 
alwaysApply: false
---

# 重构开发流程提问示例合集

## **1. 给 代码考古学家 Agent 的提问示例 (`00_代码考古学家_Code_Archaeologist_Agent.md`)**

```
你好，顶级的代码考古学家专家！

我们有一个现有的项目需要进行重构和功能扩展，现在需要你对这个"信息黑盒"进行彻底的勘探和分析，为后续的重构团队提供详细的项目现状蓝图。

**必要信息：**
*   **项目代码库访问**：已提供完整的项目代码访问权限
*   **项目核心背景**：这是一个运行了2年的生产环境项目，主要功能是[具体描述现有项目的核心功能，如：用户管理系统、电商平台、内容管理系统等]
*   **重构意图**：我们希望 [具体描述重构目标，如：提升性能、增加新功能、解决技术债务、迁移到新技术栈等]
*   **当前面临的主要问题**：[描述已知问题，如：性能瓶颈、代码维护困难、用户反馈的问题等]

**可选信息：**
*   **技术栈信息**：项目当前使用的主要技术栈（如果已知）
*   **团队反馈**：开发团队反映的技术债务和维护难点
*   **用户反馈**：用户使用过程中遇到的问题和改进建议
*   **业务发展需求**：未来业务发展对系统提出的新要求
*   **现有文档**：任何现有的技术文档、API文档或设计文档
*   **部署环境**：当前的部署环境和运维情况

请你按照代码考古学家的专业方法论，对项目进行全面的"勘探与测绘"，重点关注：
1. 项目的整体架构和模块结构
2. 技术栈的使用情况和版本兼容性
3. 代码质量和潜在的技术债务
4. 性能瓶颈和优化机会
5. 安全隐患和合规性问题
6. 模块间的依赖关系和耦合度

请开始你的代码考古工作，如果需要更多信息或遇到访问问题，请随时告知。
```

### **1.2 给代码考古学家的Augment Rules生成提问示例**

```
你好，顶级的代码考古学家专家！

我们需要你根据当前项目的实际情况自动生成Augment Rules，为AI开发助手提供精准的项目上下文和编码规范指导。

**生成目标**:
基于项目现状分析，生成完整的Augment Rules体系，确保AI助手能够理解项目的技术栈、编码规范和最佳实践。

**分析要求**:
1. **技术栈深度分析**: 识别项目使用的所有技术栈、框架版本、主要依赖
2. **编码规范提取**: 从现有代码中提取命名规范、代码结构、注释风格等
3. **架构模式识别**: 分析项目采用的设计模式、架构原则、代码组织方式
4. **最佳实践总结**: 提取项目中的优秀代码模式和开发约定

**生成内容**:
- **Always Rules**: 项目基础规范和核心技术栈约定
- **Manual Rules**: 特定模块或功能的专门开发规范
- **Auto Rules**: 基于文件类型或路径自动应用的编码规范

**输出要求**:
- 按照Augment Rules标准格式生成 (参考: https://docs.augmentcode.com/setup-augment/guidelines)
- 包含具体的代码示例和使用说明
- 确保Rules与项目实际情况100%匹配
- 提供Rules的分类、优先级和适用范围

**模板参考**:
请使用《Augment Rules 生成模板》来确保输出的完整性和准确性。

**特别关注**:
- 项目特有的编码风格和命名约定
- 常用的代码模式和架构原则
- 错误处理和日志记录规范
- 测试编写和质量保证要求
- 性能优化和安全考虑

请开始分析项目并生成对应的Augment Rules，如果需要更多项目信息或有任何疑问，请随时告知。
```

---

## **2. 给 产品经理 (PM Agent) 的提问示例 (`01_产品经理_重构需求管理.md`)**

```
你好，资深产品经理专家！

我们有一个现有的产品项目需要进行重构和功能扩展，代码考古学家已经完成了深度的现状分析。现在需要你基于这些现状分析，制定科学的重构策略和需求规划。

**必要信息：**
*   **项目现状蓝图**：请参考 `analysis/Project_Status_Blueprint.md` 
*   **技术栈分析报告**：请参考 `analysis/Tech_Stack_Analysis.md`
*   **代码质量评估报告**：请参考 `analysis/Code_Quality_Assessment.md`
*   **当前项目的核心功能**：[描述现有项目的主要功能模块和业务流程]
*   **重构的业务目标**：[明确描述为什么要重构，期望达到什么业务目标]

**可选信息：**
*   **现有用户群体**：当前产品的用户规模、用户画像和使用习惯
*   **用户反馈数据**：来自用户的功能需求、问题反馈和改进建议
*   **业务发展规划**：未来6-12个月的业务发展方向和新功能需求
*   **竞品变化**：行业内竞品的发展和新趋势
*   **资源约束**：重构项目的时间、人力和预算限制
*   **技术团队反馈**：开发团队对当前系统的意见和改进建议

**重构重点关注：**
*   **用户体验连续性**：如何在重构过程中保持用户使用的流畅性
*   **数据安全保障**：现有用户数据的迁移和保护策略
*   **功能演进规划**：新功能与现有功能的整合方案
*   **渐进式改进**：如何避免激进变更，采用渐进式的改进策略

请你基于现状分析，制定重构需求文档(RRD)、功能演进路线图、用户影响评估和数据迁移策略。特别需要平衡创新需求与现有用户习惯的保护。

如果对现状分析有任何疑问，或需要更多业务背景信息，请随时与我沟通。
```

---

## **3. 给 技术架构师 Agent 的提问示例 (`02_技术架构师_重构架构设计.md`)**

```
你好，资深技术架构师！

我们正在对一个现有项目进行重构，代码考古学家已经完成了全面的现状分析，产品经理也制定了重构策略。现在需要你设计渐进式的架构演进方案，确保重构过程的安全性和可控性。

**必要信息（核心输入）：**
*   **项目现状蓝图**：请参考 `analysis/Project_Status_Blueprint.md`
*   **代码质量评估报告**：请参考 `analysis/Code_Quality_Assessment.md`
*   **重构风险评估**：请参考 `analysis/Refactoring_Risk_Assessment.md`
*   **重构需求文档 (RRD)**：请参考 `docs/RRD.md`
*   **功能演进路线图**：请参考 `docs/Feature_Evolution_Roadmap.md`

**重构架构设计重点：**
*   **渐进式演进策略**：设计分阶段的架构改进方案，避免大爆炸式重构
*   **向后兼容性保障**：确保新架构能够与现有系统和数据兼容
*   **技术债务清偿**：制定技术债务的优先级处理方案
*   **数据迁移安全**：设计安全可靠的数据迁移和系统切换方案
*   **回滚机制设计**：为每个重构阶段设计回滚预案

**可选信息：**
*   **现有系统负载情况**：当前系统的性能指标和负载情况
*   **团队技术能力**：开发团队对不同技术栈的熟悉程度
*   **运维约束**：生产环境的部署和运维限制
*   **合规要求**：行业或企业的技术合规要求
*   **预算和时间约束**：重构项目的资源限制

**特别关注的技术挑战：**
*   **零停机迁移**：如何在不影响用户使用的情况下完成系统升级
*   **数据一致性**：重构过程中如何保证数据的完整性和一致性
*   **性能优化**：解决现有系统的性能瓶颈
*   **安全加固**：修复已识别的安全隐患

请你设计重构技术架构文档、架构演进策略、技术债务清偿计划，并记录关键的架构决策(ADRs)。

如果需要与产品经理协调需求优先级，或需要更多现状分析的技术细节，请随时告知。
```

---

## **4. 给 UI/UX 设计师 Agent 的提问示例 (`03_UI设计师_界面重构设计.md`)**

```
你好，顶尖的UI/UX设计重构专家！

我们正在对一个现有产品进行重构，需要在保持用户使用习惯的基础上优化界面设计和用户体验。请你基于重构需求和用户影响评估，设计渐进式的界面改进方案。

**必要信息：**
*   **重构需求文档 (RRD)**：请参考 `docs/RRD.md`，特别关注用户体验改进需求
*   **用户影响评估**：请参考 `docs/User_Impact_Assessment.md`，了解重构对用户的潜在影响
*   **重构技术架构文档**：请参考 `docs/Refactoring_Technical_Architecture.md`，了解技术约束
*   **现有界面截图或访问**：[提供现有系统的界面访问方式或截图]

**重构设计的核心原则：**
*   **用户习惯保护**：在优化设计的同时，尽量保持用户熟悉的操作流程
*   **渐进式改进**：设计分阶段的界面改进方案，避免激进的界面变更
*   **学习成本最小化**：确保界面变更不会给用户带来过大的学习负担
*   **一致性维护**：保持整体设计语言的一致性

**可选信息：**
*   **用户反馈数据**：用户对现有界面的意见和改进建议
*   **使用行为分析**：用户在现有系统中的操作习惯和路径
*   **竞品界面分析**：同类产品的界面设计趋势和最佳实践
*   **设备兼容要求**：需要支持的设备类型和屏幕尺寸
*   **品牌设计要求**：企业的品牌色彩和视觉规范

**需要重点设计的界面类型：**
1. **核心功能界面重构**：对主要功能模块的界面进行优化
2. **新功能界面设计**：为新增功能设计配套的界面
3. **导航和信息架构**：优化整体的信息组织和导航结构
4. **响应式适配**：确保界面在不同设备上的良好表现
5. **过渡状态设计**：设计重构过程中的界面过渡方案

**交付期望：**
*   界面重构设计方案（高保真原型）
*   用户体验改进方案文档
*   界面迁移指南（新旧界面对比和迁移说明）
*   设计一致性规范

请开始你的重构设计工作，如果对用户需求或技术约束有疑问，请及时沟通。
```

---

## **5. 给 后端开发者 Agent 的提问示例 (`04_后端开发者_服务重构实施.md`)**

```
你好，资深的后端重构专家！

我们正在对一个现有系统进行后端重构，需要你基于重构需求和架构设计，实施渐进式的代码重构，确保服务的连续性和数据的安全性。

**必要信息：**
*   **重构需求文档 (RRD)**：从 `docs/RRD.md` 获取功能重构需求
*   **重构技术架构文档**：从 `docs/Refactoring_Technical_Architecture.md` 获取架构设计
*   **技术债务清偿计划**：从 `docs/Technical_Debt_Resolution_Plan.md` 获取债务处理优先级
*   **数据迁移策略**：从 `docs/Data_Migration_Strategy.md` 获取数据处理方案
*   **现有代码库完整访问**：[提供代码库访问方式]
*   **现有数据库结构**：[提供数据库访问和结构说明]

**重构实施的核心要求：**
*   **渐进式重构**：采用分阶段的方式进行代码重构，每个阶段都能独立发布
*   **服务连续性**：确保重构过程中用户服务不中断
*   **数据安全**：开发完整的数据迁移脚本和回滚方案
*   **API兼容性**：维护API的向后兼容性，或提供详细的迁移指南

**技术重构重点：**
*   **性能优化**：解决已识别的性能瓶颈和资源使用问题
*   **代码质量提升**：重构代码结构，提高可维护性
*   **安全加固**：修复安全漏洞，提升系统安全性
*   **架构优化**：改进模块结构和依赖关系

**可选信息：**
*   **现有API文档**：当前系统的API文档和接口说明
*   **性能监控数据**：生产环境的性能指标和瓶颈数据
*   **用户使用模式**：高峰期的访问模式和负载特征
*   **第三方依赖**：现有系统依赖的外部服务和组件
*   **部署环境信息**：生产环境的部署架构和约束

**开发输出要求：**
*   **重构后端服务代码**：位于 `backend_refactored/`
*   **数据迁移脚本和文档**：位于 `migration/backend/`
*   **API兼容性文档**：详细说明API变更和兼容性处理
*   **重构实施文档**：记录重构过程和关键技术决策

**特别关注的技术挑战：**
1. **零停机部署**：如何在不停服的情况下完成系统升级
2. **数据迁移验证**：确保迁移后数据的完整性和正确性
3. **性能回归测试**：验证重构后系统性能是否达到预期
4. **监控和告警**：完善系统监控，及时发现重构引入的问题

请开始重构实施工作，确保每个步骤都有详细的文档记录和测试验证。如果遇到技术难题或需要与架构师讨论方案调整，请及时沟通。
```

---

## **6. 给 前端开发者_移动端 Agent 的提问示例 (`05_前端开发者_移动端重构.md`)**

```
你好，移动端重构专家！

我们正在对一个现有的移动应用进行重构，需要你基于界面重构设计和后端API变更，实施移动端的渐进式重构，确保用户体验的平滑过渡。

**必要信息：**
*   **重构需求文档 (RRD)**：从 `docs/RRD.md` 获取移动端重构需求
*   **界面重构设计方案**：从 `design/refactoring_prototypes/` 获取新的界面设计
*   **重构技术架构文档**：从 `docs/Refactoring_Technical_Architecture.md` 获取技术约束
*   **后端API兼容性文档**：从 `backend_refactored/API_Compatibility.md` 获取API变更说明
*   **现有移动端代码库**：[提供现有代码库访问方式]

**移动端重构的特殊考虑：**
*   **用户升级体验**：设计平滑的应用升级和数据迁移体验
*   **版本兼容管理**：处理新旧版本并存期间的兼容性问题
*   **本地数据迁移**：升级本地存储结构和数据格式
*   **渐进式功能发布**：通过功能开关控制新功能的逐步发布

**可选信息：**
*   **用户使用数据**：现有应用的用户行为分析和使用统计
*   **设备兼容要求**：需要支持的设备型号和操作系统版本
*   **应用商店政策**：发布平台的审核要求和限制
*   **用户反馈历史**：用户对现有应用的意见和改进建议
*   **竞品分析**：同类应用的功能特点和用户体验

**重构实施重点：**
1. **界面渐进式改进**：分阶段更新界面，减少用户学习成本
2. **性能优化**：解决现有应用的性能问题和内存使用
3. **新功能集成**：无缝集成新功能，保持整体体验一致
4. **离线功能增强**：改进离线使用体验和数据同步机制

**技术实施要求：**
*   **客户端数据迁移**：处理本地数据库升级和数据迁移
*   **API适配层**：处理新旧API的兼容性问题
*   **错误处理增强**：改进错误处理和用户反馈机制
*   **监控集成**：集成性能监控和用户行为分析

**交付物：**
*   **重构移动端项目代码**：保存到 `frontend_mobile_refactored/`
*   **界面迁移实施文档**：详细说明界面变更和实施方案
*   **用户数据迁移客户端处理**：保存到 `migration/frontend_mobile/`

请开始移动端重构工作，特别注意用户体验的连续性和数据安全。如果对设计方案或API变更有疑问，请及时与相关团队协调。
```

---

## **7. 给 测试/质量保证 (QA) 工程师 Agent 的提问示例 (`06_测试工程师_重构质量保证.md`)**

```
你好，资深重构测试专家！

我们正在进行一个复杂的系统重构项目，需要你设计全面的测试策略，确保重构过程的质量和安全性，特别是要验证现有功能不受影响，以及数据迁移的完整性。

**必要信息：**
*   **重构需求文档 (RRD)**：从 `docs/RRD.md` 获取重构目标和验收标准
*   **重构技术架构文档**：从 `docs/Refactoring_Technical_Architecture.md` 获取技术实施方案
*   **用户影响评估**：从 `docs/User_Impact_Assessment.md` 获取用户影响分析
*   **所有重构后的代码库**：包括后端、移动端、管理后台的重构代码
*   **现有系统测试用例**：当前系统的测试用例集和测试数据

**重构测试的特殊重点：**
*   **回归测试全覆盖**：确保所有现有功能在重构后仍然正常工作
*   **数据迁移验证**：验证数据迁移的完整性、正确性和一致性
*   **性能对比测试**：对比重构前后的系统性能指标
*   **兼容性测试**：验证新旧版本在过渡期的兼容性
*   **用户验收测试**：从用户角度验证重构效果

**可选信息：**
*   **生产环境监控数据**：现有系统的性能基线和监控指标
*   **用户使用场景**：真实的用户操作流程和使用模式
*   **历史bug数据**：过往发现的问题和修复记录
*   **业务关键流程**：最重要的业务流程和数据处理逻辑
*   **第三方集成情况**：与外部系统的集成和依赖关系

**测试策略重点：**
1. **分阶段测试计划**：配合重构的分阶段实施进行测试
2. **数据完整性验证**：多维度验证数据迁移的正确性
3. **性能基准对比**：建立性能测试基准，对比优化效果
4. **安全测试加强**：验证安全加固措施的有效性
5. **用户体验测试**：确保重构不会降低用户体验

**特别关注的测试场景：**
*   **零停机切换测试**：验证系统切换过程的平滑性
*   **数据一致性测试**：验证重构过程中数据的一致性
*   **回滚机制测试**：验证各阶段回滚方案的可行性
*   **负载压力测试**：验证重构后系统的承载能力
*   **边界条件测试**：测试各种异常情况和边界条件

**测试交付物：**
*   **重构测试策略**：保存到 `testing/Refactoring_Test_Strategy.md`
*   **回归测试计划**：保存到 `testing/Regression_Test_Plan.md`
*   **数据迁移测试报告**：保存到 `testing/Data_Migration_Test_Report.md`
*   **性能对比测试报告**：保存到 `testing/Performance_Comparison_Report.md`
*   **用户验收测试方案**：保存到 `testing/User_Acceptance_Test_Plan.md`

**项目时间线：**
*   第1-2周：制定测试策略和测试计划
*   第3-4周：执行回归测试和功能验证
*   第5-6周：数据迁移测试和性能对比测试
*   第7-8周：用户验收测试和生产发布测试

请开始设计重构测试策略，特别注意建立完整的质量保证体系。如果需要了解更多技术实施细节或用户使用场景，请随时与相关团队沟通。
```

这套重构开发流程的提问示例体现了重构项目的核心特点：
- **基于现状分析**：从代码考古开始，深度理解现有系统
- **渐进式改进**：强调分阶段、可控的重构过程
- **用户体验连续性**：重点保护现有用户的使用习惯
- **数据安全保障**：确保数据迁移的完整性和安全性
- **风险控制**：每个阶段都有回滚方案和质量保证
