"""
API根视图
"""
from rest_framework.decorators import api_view, permission_classes
from rest_framework.permissions import AllowAny
from rest_framework.response import Response
from django.urls import reverse
from django.shortcuts import render


@api_view(['GET'])
@permission_classes([AllowAny])
def api_root(request):
    """API根视图"""
    return Response({
        'message': 'CosyVoice API v2.0',
        'version': '2.0.0',
        'endpoints': {
            'docs': request.build_absolute_uri(reverse('api-docs')),
            'schema': request.build_absolute_uri(reverse('api-schema')),
            'auth': request.build_absolute_uri('/api/auth/'),
            'users': request.build_absolute_uri('/api/users/'),
            'tasks': request.build_absolute_uri('/api/tasks/'),
            'files': request.build_absolute_uri('/api/files/'),
        },
        'compatibility': {
            'inference_sft': request.build_absolute_uri('/inference_sft'),
            'inference_zero_shot': request.build_absolute_uri('/inference_zero_shot'),
            'inference_cross_lingual': request.build_absolute_uri('/inference_cross_lingual'),
            'inference_instruct': request.build_absolute_uri('/inference_instruct'),
            'inference_instruct2': request.build_absolute_uri('/inference_instruct2'),
        }
    })


def custom_swagger_ui(request):
    """自定义Swagger UI页面"""
    return render(request, 'swagger_ui.html')
