"""
任务相关URL配置
"""
from django.urls import path
from . import views

urlpatterns = [
    # 任务管理
    path('', views.TaskListView.as_view(), name='task_list'),
    path('create/', views.TaskCreateView.as_view(), name='task_create'),
    path('<uuid:pk>/', views.TaskDetailView.as_view(), name='task_detail'),
    path('<uuid:task_id>/logs/', views.TaskLogListView.as_view(), name='task_log_list'),

    # 任务统计和操作
    path('stats/', views.task_stats, name='task_stats'),
    path('retry/', views.retry_tasks, name='retry_tasks'),
    path('cancel/', views.cancel_tasks, name='cancel_tasks'),
    path('cleanup/', views.cleanup_completed_tasks, name='cleanup_completed_tasks'),
]
