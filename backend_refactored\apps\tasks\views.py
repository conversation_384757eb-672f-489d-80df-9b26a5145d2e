"""
任务相关视图
"""
from rest_framework import generics, permissions, status
from rest_framework.decorators import api_view, permission_classes
from rest_framework.response import Response
from django.db.models import Count, Avg, Q
from django.utils import timezone
from drf_spectacular.utils import extend_schema
from .models import Task, TaskLog, TaskStatus, TaskType
from .serializers import (
    TaskCreateSerializer, TaskSerializer, TaskListSerializer,
    TaskLogSerializer, TaskStatsSerializer, TaskRetrySerializer,
    TaskCancelSerializer
)
from .tasks import process_tts_task
from apps.users.models import UserRole
from loguru import logger


class TaskCreateView(generics.CreateAPIView):
    """创建任务"""
    serializer_class = TaskCreateSerializer
    permission_classes = [permissions.AllowAny]  # 兼容原有API
    
    @extend_schema(
        summary="创建TTS任务",
        description="创建新的文本转语音任务",
        tags=["任务管理"]
    )
    def post(self, request, *args, **kwargs):
        serializer = self.get_serializer(data=request.data)
        
        if serializer.is_valid():
            # 检查用户配额
            if request.user.is_authenticated:
                if not request.user.can_use_quota():
                    return Response({
                        'error': '配额不足',
                        'message': f'当前配额: {request.user.quota_used}/{request.user.quota_limit}'
                    }, status=status.HTTP_429_TOO_MANY_REQUESTS)
                
                # 使用配额
                request.user.use_quota()
            
            # 创建任务
            task = serializer.save()
            
            # 提交到Celery队列
            process_tts_task.delay(str(task.id))
            
            logger.info(f"创建TTS任务: {task.id}, 类型: {task.task_type}")
            
            return Response({
                'task_id': str(task.id),
                'status': task.status,
                'message': '任务已创建并提交处理'
            }, status=status.HTTP_201_CREATED)
        
        return Response(serializer.errors, status=status.HTTP_400_BAD_REQUEST)


class TaskListView(generics.ListAPIView):
    """任务列表"""
    serializer_class = TaskListSerializer
    permission_classes = [permissions.IsAuthenticated]
    
    def get_queryset(self):
        queryset = Task.objects.all()
        
        # 普通用户只能看到自己的任务
        if self.request.user.role != UserRole.ADMIN:
            queryset = queryset.filter(user=self.request.user)
        
        # 过滤参数
        task_type = self.request.query_params.get('task_type')
        if task_type:
            queryset = queryset.filter(task_type=task_type)
        
        status_filter = self.request.query_params.get('status')
        if status_filter:
            queryset = queryset.filter(status=status_filter)
        
        return queryset.order_by('-created_at')
    
    @extend_schema(
        summary="获取任务列表",
        description="获取任务列表，支持按类型和状态过滤",
        tags=["任务管理"]
    )
    def get(self, request, *args, **kwargs):
        return super().get(request, *args, **kwargs)


class TaskDetailView(generics.RetrieveAPIView):
    """任务详情"""
    serializer_class = TaskSerializer
    permission_classes = [permissions.IsAuthenticated]
    
    def get_queryset(self):
        queryset = Task.objects.all()
        
        # 普通用户只能看到自己的任务
        if self.request.user.role != UserRole.ADMIN:
            queryset = queryset.filter(user=self.request.user)
        
        return queryset
    
    @extend_schema(
        summary="获取任务详情",
        description="获取指定任务的详细信息",
        tags=["任务管理"]
    )
    def get(self, request, *args, **kwargs):
        return super().get(request, *args, **kwargs)


class TaskLogListView(generics.ListAPIView):
    """任务日志列表"""
    serializer_class = TaskLogSerializer
    permission_classes = [permissions.IsAuthenticated]
    
    def get_queryset(self):
        task_id = self.kwargs.get('task_id')
        queryset = TaskLog.objects.filter(task_id=task_id)
        
        # 检查任务权限
        try:
            task = Task.objects.get(id=task_id)
            if self.request.user.role != UserRole.ADMIN and task.user != self.request.user:
                return TaskLog.objects.none()
        except Task.DoesNotExist:
            return TaskLog.objects.none()
        
        return queryset.order_by('-created_at')
    
    @extend_schema(
        summary="获取任务日志",
        description="获取指定任务的日志记录",
        tags=["任务管理"]
    )
    def get(self, request, *args, **kwargs):
        return super().get(request, *args, **kwargs)


@extend_schema(
    summary="获取任务统计",
    description="获取任务统计信息",
    responses=TaskStatsSerializer,
    tags=["任务管理"]
)
@api_view(['GET'])
@permission_classes([permissions.IsAuthenticated])
def task_stats(request):
    """任务统计"""
    queryset = Task.objects.all()
    
    # 普通用户只能看到自己的统计
    if request.user.role != UserRole.ADMIN:
        queryset = queryset.filter(user=request.user)
    
    # 基础统计
    stats = queryset.aggregate(
        total_tasks=Count('id'),
        pending_tasks=Count('id', filter=Q(status=TaskStatus.PENDING)),
        processing_tasks=Count('id', filter=Q(status=TaskStatus.PROCESSING)),
        completed_tasks=Count('id', filter=Q(status=TaskStatus.COMPLETED)),
        failed_tasks=Count('id', filter=Q(status=TaskStatus.FAILED)),
        cancelled_tasks=Count('id', filter=Q(status=TaskStatus.CANCELLED)),
        avg_processing_time=Avg('processing_time'),
        avg_queue_time=Avg('queue_time'),
    )
    
    # 计算成功率
    total = stats['total_tasks'] or 0
    completed = stats['completed_tasks'] or 0
    success_rate = (completed / total * 100) if total > 0 else 0
    
    stats_data = {
        **stats,
        'avg_processing_time': stats['avg_processing_time'] or 0,
        'avg_queue_time': stats['avg_queue_time'] or 0,
        'success_rate': round(success_rate, 2),
    }
    
    serializer = TaskStatsSerializer(stats_data)
    return Response(serializer.data)


@extend_schema(
    summary="重试任务",
    description="重试失败的任务",
    request=TaskRetrySerializer,
    tags=["任务管理"]
)
@api_view(['POST'])
@permission_classes([permissions.IsAuthenticated])
def retry_tasks(request):
    """重试任务"""
    serializer = TaskRetrySerializer(data=request.data, context={'request': request})
    
    if serializer.is_valid():
        task_ids = serializer.validated_data['task_ids']
        
        retried_count = 0
        for task_id in task_ids:
            try:
                task = Task.objects.get(id=task_id)
                if task.can_retry:
                    task.increment_retry()
                    process_tts_task.delay(str(task.id))
                    retried_count += 1
                    logger.info(f"重试任务: {task.id}")
            except Task.DoesNotExist:
                continue
        
        return Response({
            'message': f'成功重试 {retried_count} 个任务',
            'retried_count': retried_count
        })
    
    return Response(serializer.errors, status=status.HTTP_400_BAD_REQUEST)


@extend_schema(
    summary="取消任务",
    description="取消等待中或处理中的任务",
    request=TaskCancelSerializer,
    tags=["任务管理"]
)
@api_view(['POST'])
@permission_classes([permissions.IsAuthenticated])
def cancel_tasks(request):
    """取消任务"""
    serializer = TaskCancelSerializer(data=request.data, context={'request': request})
    
    if serializer.is_valid():
        task_ids = serializer.validated_data['task_ids']
        
        cancelled_count = 0
        for task_id in task_ids:
            try:
                task = Task.objects.get(id=task_id)
                if task.status in [TaskStatus.PENDING, TaskStatus.PROCESSING]:
                    task.status = TaskStatus.CANCELLED
                    task.completed_at = timezone.now()
                    task.save(update_fields=['status', 'completed_at'])
                    cancelled_count += 1
                    logger.info(f"取消任务: {task.id}")
            except Task.DoesNotExist:
                continue
        
        return Response({
            'message': f'成功取消 {cancelled_count} 个任务',
            'cancelled_count': cancelled_count
        })
    
    return Response(serializer.errors, status=status.HTTP_400_BAD_REQUEST)


@extend_schema(
    summary="清理已完成任务",
    description="清理指定天数前的已完成任务（仅管理员）",
    tags=["任务管理"]
)
@api_view(['POST'])
@permission_classes([permissions.IsAuthenticated])
def cleanup_completed_tasks(request):
    """清理已完成任务（管理员）"""
    if request.user.role != UserRole.ADMIN:
        return Response({'error': '权限不足'}, status=status.HTTP_403_FORBIDDEN)
    
    days = request.data.get('days', 30)
    if not isinstance(days, int) or days < 1:
        return Response({'error': '天数必须是正整数'}, status=status.HTTP_400_BAD_REQUEST)
    
    cutoff_date = timezone.now() - timezone.timedelta(days=days)
    
    # 软删除已完成的任务
    completed_tasks = Task.objects.filter(
        status=TaskStatus.COMPLETED,
        completed_at__lt=cutoff_date,
        is_active=True
    )
    
    deleted_count = 0
    for task in completed_tasks:
        task.soft_delete()
        deleted_count += 1
    
    logger.info(f"管理员清理已完成任务: {request.user.username}, 清理 {deleted_count} 个任务")
    
    return Response({
        'message': f'成功清理 {deleted_count} 个已完成任务',
        'deleted_count': deleted_count
    })
