#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import os
import sys
import argparse
import numpy as np
import torchaudio
import torch
import io
import wave
from typing import Optional
from fastapi import FastAPI, Form, File, UploadFile, HTTPException
from fastapi.responses import StreamingResponse, FileResponse
from fastapi.middleware.cors import CORSMiddleware
from fastapi.staticfiles import StaticFiles
from pydantic import BaseModel
import uvicorn
import tempfile
import glob

# 添加路径
ROOT_DIR = os.path.dirname(os.path.abspath(__file__))
sys.path.append('{}/third_party/Matcha-TTS'.format(ROOT_DIR))

from cosyvoice.cli.cosyvoice import CosyVoice, CosyVoice2
from cosyvoice.utils.file_utils import load_wav

# 创建FastAPI应用
app = FastAPI(
    title="CosyVoice TTS API",
    description="CosyVoice文本转语音API服务，支持多种推理模式",
    version="1.0.0",
    docs_url="/docs",
    redoc_url="/redoc"
)

# 跨域设置
app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"],
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"]
)

# 挂载静态文件目录（用于提供参考音频）
if os.path.exists("asset"):
    app.mount("/asset", StaticFiles(directory="asset"), name="asset")

# 全局变量
cosyvoice = None
OUTPUT_DIR = "generated_audio"  # 音频输出目录

# 确保输出目录存在
if not os.path.exists(OUTPUT_DIR):
    os.makedirs(OUTPUT_DIR)

class TTSRequest(BaseModel):
    """TTS请求模型"""
    tts_text: str
    spk_id: Optional[str] = "中文女"
    prompt_text: Optional[str] = None
    instruct_text: Optional[str] = None

def generate_audio_data(model_output, sample_rate=22050):
    """生成WAV格式音频数据流"""
    # 收集所有音频片段
    audio_chunks = []
    for i in model_output:
        audio_chunks.append(i['tts_speech'].numpy())

    # 合并所有音频片段
    if audio_chunks:
        full_audio = np.concatenate(audio_chunks, axis=-1)
        # 转换为int16格式
        audio_int16 = (full_audio * (2 ** 15)).astype(np.int16)

        # 创建WAV文件的字节流
        wav_buffer = io.BytesIO()

        # 写入WAV文件头和数据
        with wave.open(wav_buffer, 'wb') as wav_file:
            wav_file.setnchannels(1)  # 单声道
            wav_file.setsampwidth(2)  # 16位
            wav_file.setframerate(sample_rate)  # 采样率
            wav_file.writeframes(audio_int16.tobytes())

        wav_buffer.seek(0)
        return wav_buffer.getvalue()
    else:
        return b''

def save_audio_and_get_path(model_output, sample_rate=22050, prefix="audio"):
    """保存音频文件并返回文件路径"""
    import time
    from datetime import datetime

    # 生成唯一文件名
    timestamp = datetime.now().strftime("%Y%m%d_%H%M%S_%f")[:-3]  # 精确到毫秒
    filename = f"{prefix}_{timestamp}.wav"
    file_path = os.path.join(OUTPUT_DIR, filename)

    # 生成音频数据
    audio_data = generate_audio_data(model_output, sample_rate)

    if audio_data:
        # 保存到文件
        with open(file_path, 'wb') as f:
            f.write(audio_data)

        # 返回绝对路径
        abs_path = os.path.abspath(file_path)

        # 获取文件信息
        file_size = os.path.getsize(abs_path)

        # 验证音频文件
        try:
            with wave.open(abs_path, 'rb') as wav_file:
                duration = wav_file.getnframes() / wav_file.getframerate()
                channels = wav_file.getnchannels()
                sample_rate_actual = wav_file.getframerate()
        except:
            duration = 0
            channels = 1
            sample_rate_actual = sample_rate

        return {
            "file_path": abs_path,
            "filename": filename,
            "file_size": file_size,
            "duration": round(duration, 2),
            "sample_rate": sample_rate_actual,
            "channels": channels,
            "relative_path": file_path
        }
    else:
        raise Exception("音频生成失败，无数据")

@app.get("/", summary="API根路径", description="返回API基本信息")
async def root():
    return {
        "message": "CosyVoice TTS API",
        "version": "1.0.0",
        "docs": "/docs",
        "available_endpoints": [
            "/inference_sft",
            "/inference_zero_shot", 
            "/inference_cross_lingual",
            "/inference_instruct",
            "/list_speakers",
            "/reference_audios"
        ]
    }

@app.get("/list_speakers", summary="获取可用说话人列表")
async def list_speakers():
    """获取所有可用的预训练说话人ID"""
    try:
        speakers = cosyvoice.list_available_spks()
        return {"speakers": speakers}
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"获取说话人列表失败: {str(e)}")

@app.get("/reference_audios", summary="获取参考音频列表")
async def get_reference_audios():
    """获取asset目录下的所有参考音频文件"""
    audio_files = []
    if os.path.exists("asset"):
        for ext in ["*.wav", "*.mp3", "*.flac"]:
            audio_files.extend(glob.glob(f"asset/{ext}"))
    
    reference_audios = []
    for audio_file in audio_files:
        filename = os.path.basename(audio_file)
        name_without_ext = os.path.splitext(filename)[0]
        reference_audios.append({
            "filename": filename,
            "path": f"/asset/{filename}",
            "suggested_prompt_text": name_without_ext,
            "url": f"http://localhost:{args.port}/asset/{filename}"
        })
    
    return {"reference_audios": reference_audios}

@app.post("/inference_sft", summary="SFT模式推理", description="使用预训练音色进行语音合成")
async def inference_sft(
    tts_text: str = Form(..., description="要合成的文本"),
    spk_id: str = Form("中文女", description="说话人ID")
):
    """
    SFT（Supervised Fine-Tuning）模式推理

    - **tts_text**: 要合成的文本内容
    - **spk_id**: 预训练的说话人ID，可通过/list_speakers获取
    """
    try:
        model_output = cosyvoice.inference_sft(tts_text, spk_id)
        audio_info = save_audio_and_get_path(model_output, cosyvoice.sample_rate, "sft")

        return {
            "success": True,
            "message": "SFT推理成功",
            "audio_info": audio_info,
            "text": tts_text,
            "speaker": spk_id
        }
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"SFT推理失败: {str(e)}")

@app.post("/inference_zero_shot", summary="3秒极速复刻", description="基于参考音频进行零样本语音克隆")
async def inference_zero_shot(
    tts_text: str = Form(..., description="要合成的文本"),
    prompt_text: str = Form(..., description="参考音频对应的文本"),
    prompt_wav: UploadFile = File(..., description="参考音频文件（建议3-30秒）")
):
    """
    零样本语音克隆（3秒极速复刻）
    
    - **tts_text**: 要合成的文本内容
    - **prompt_text**: 参考音频对应的文本内容
    - **prompt_wav**: 参考音频文件，支持wav/mp3格式，建议3-30秒
    
    **使用示例**:
    1. 上传一段3-30秒的参考音频
    2. 输入参考音频对应的文本
    3. 输入要合成的目标文本
    4. 系统将使用参考音频的音色合成目标文本
    """
    try:
        # 验证文件格式
        if not prompt_wav.filename.lower().endswith(('.wav', '.mp3', '.flac')):
            raise HTTPException(status_code=400, detail="不支持的音频格式，请使用wav/mp3/flac格式")

        # 加载参考音频
        prompt_speech_16k = load_wav(prompt_wav.file, 16000)

        # 执行推理
        model_output = cosyvoice.inference_zero_shot(tts_text, prompt_text, prompt_speech_16k)
        audio_info = save_audio_and_get_path(model_output, cosyvoice.sample_rate, "zero_shot")

        return {
            "success": True,
            "message": "零样本推理成功",
            "audio_info": audio_info,
            "text": tts_text,
            "prompt_text": prompt_text,
            "reference_audio": prompt_wav.filename
        }
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"零样本推理失败: {str(e)}")

@app.post("/inference_cross_lingual", summary="跨语种复刻", description="跨语言语音克隆")
async def inference_cross_lingual(
    tts_text: str = Form(..., description="要合成的文本（可以是不同语言）"),
    prompt_wav: UploadFile = File(..., description="参考音频文件")
):
    """
    跨语种语音克隆
    
    - **tts_text**: 要合成的文本，可以是与参考音频不同的语言
    - **prompt_wav**: 参考音频文件
    
    支持的语言标记：
    - `<|zh|>`: 中文
    - `<|en|>`: 英文  
    - `<|jp|>`: 日文
    - `<|yue|>`: 粤语
    - `<|ko|>`: 韩语
    """
    try:
        prompt_speech_16k = load_wav(prompt_wav.file, 16000)
        model_output = cosyvoice.inference_cross_lingual(tts_text, prompt_speech_16k)
        audio_info = save_audio_and_get_path(model_output, cosyvoice.sample_rate, "cross_lingual")

        return {
            "success": True,
            "message": "跨语种推理成功",
            "audio_info": audio_info,
            "text": tts_text,
            "reference_audio": prompt_wav.filename
        }
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"跨语种推理失败: {str(e)}")

@app.post("/inference_instruct", summary="自然语言控制", description="通过自然语言指令控制语音合成")
async def inference_instruct(
    tts_text: str = Form(..., description="要合成的文本"),
    spk_id: str = Form("中文女", description="基础说话人ID"),
    instruct_text: str = Form(..., description="控制指令，如'用温柔的语气说'")
):
    """
    自然语言控制模式

    - **tts_text**: 要合成的文本内容
    - **spk_id**: 基础说话人ID
    - **instruct_text**: 自然语言控制指令

    **指令示例**:
    - "用温柔的语气说"
    - "用激动的语气说"
    - "用悲伤的语气说"
    - "说得慢一点"
    - "说得快一点"

    **注意**: 需要使用CosyVoice-300M-Instruct模型
    """
    try:
        # 验证参数
        if not tts_text.strip():
            raise HTTPException(status_code=400, detail="合成文本不能为空")

        if not instruct_text.strip():
            raise HTTPException(status_code=400, detail="控制指令不能为空")

        # 检查说话人ID是否有效
        available_spks = cosyvoice.list_available_spks()
        if spk_id not in available_spks:
            raise HTTPException(
                status_code=400,
                detail=f"无效的说话人ID: {spk_id}，可用的说话人: {available_spks}"
            )

        # 检查模型是否支持instruct模式
        use_fallback = False
        if not hasattr(cosyvoice, 'instruct') or not cosyvoice.instruct:
            # 使用SFT模式作为回退
            print(f"警告: 当前模型不支持自然语言控制，使用SFT模式作为回退")
            model_output = cosyvoice.inference_sft(tts_text, spk_id)
            use_fallback = True
        else:
            # 执行instruct推理
            model_output = cosyvoice.inference_instruct(tts_text, spk_id, instruct_text)

        audio_info = save_audio_and_get_path(model_output, cosyvoice.sample_rate, "instruct")

        result = {
            "success": True,
            "message": "自然语言控制推理成功",
            "audio_info": audio_info,
            "text": tts_text,
            "speaker": spk_id,
            "instruct_text": instruct_text
        }

        if use_fallback:
            result["fallback_mode"] = "SFT"
            result["warning"] = "模型不支持instruct模式，使用SFT模式作为回退"

        return result
    except HTTPException:
        # 重新抛出HTTP异常
        raise
    except ValueError as e:
        # 处理模型相关的值错误
        raise HTTPException(status_code=400, detail=str(e))
    except Exception as e:
        # 处理其他异常
        raise HTTPException(status_code=500, detail=f"指令推理失败: {str(e)}")

@app.post("/inference_instruct2", summary="高级自然语言控制", description="基于参考音频的自然语言控制")
async def inference_instruct2(
    tts_text: str = Form(..., description="要合成的文本"),
    instruct_text: str = Form(..., description="控制指令"),
    prompt_wav: UploadFile = File(..., description="参考音频文件")
):
    """
    高级自然语言控制模式（基于参考音频）

    - **tts_text**: 要合成的文本内容
    - **instruct_text**: 自然语言控制指令
    - **prompt_wav**: 参考音频文件

    **注意**: 需要使用CosyVoice2模型，且支持inference_instruct2方法
    """
    try:
        # 检查模型是否支持inference_instruct2方法
        if not hasattr(cosyvoice, 'inference_instruct2'):
            raise HTTPException(
                status_code=400,
                detail="当前模型不支持高级自然语言控制模式，请使用支持inference_instruct2的模型"
            )

        # 验证参数
        if not tts_text.strip():
            raise HTTPException(status_code=400, detail="合成文本不能为空")

        if not instruct_text.strip():
            raise HTTPException(status_code=400, detail="控制指令不能为空")

        # 验证音频文件
        if not prompt_wav.filename.lower().endswith(('.wav', '.mp3', '.flac')):
            raise HTTPException(status_code=400, detail="不支持的音频格式，请使用wav/mp3/flac格式")

        # 加载参考音频
        prompt_speech_16k = load_wav(prompt_wav.file, 16000)

        # 执行推理
        model_output = cosyvoice.inference_instruct2(tts_text, instruct_text, prompt_speech_16k)
        audio_info = save_audio_and_get_path(model_output, cosyvoice.sample_rate, "instruct2")

        return {
            "success": True,
            "message": "高级自然语言控制推理成功",
            "audio_info": audio_info,
            "text": tts_text,
            "instruct_text": instruct_text,
            "reference_audio": prompt_wav.filename
        }
    except HTTPException:
        # 重新抛出HTTP异常
        raise
    except ValueError as e:
        # 处理模型相关的值错误
        raise HTTPException(status_code=400, detail=str(e))
    except Exception as e:
        # 处理其他异常
        raise HTTPException(status_code=500, detail=f"高级指令推理失败: {str(e)}")

@app.get("/download/{filename}", summary="下载音频文件")
async def download_audio(filename: str):
    """下载生成的音频文件"""
    file_path = os.path.join(OUTPUT_DIR, filename)

    if not os.path.exists(file_path):
        raise HTTPException(status_code=404, detail="文件不存在")

    return FileResponse(
        path=file_path,
        filename=filename,
        media_type="audio/wav"
    )

@app.get("/list_generated_audio", summary="获取生成的音频文件列表")
async def list_generated_audio():
    """获取所有生成的音频文件列表"""
    try:
        if not os.path.exists(OUTPUT_DIR):
            return {"audio_files": []}

        audio_files = []
        for filename in os.listdir(OUTPUT_DIR):
            if filename.lower().endswith('.wav'):
                file_path = os.path.join(OUTPUT_DIR, filename)
                file_size = os.path.getsize(file_path)

                # 获取文件修改时间
                import time
                from datetime import datetime
                mtime = os.path.getmtime(file_path)
                modified_time = datetime.fromtimestamp(mtime).strftime("%Y-%m-%d %H:%M:%S")

                # 尝试获取音频信息
                try:
                    with wave.open(file_path, 'rb') as wav_file:
                        duration = wav_file.getnframes() / wav_file.getframerate()
                        sample_rate = wav_file.getframerate()
                        channels = wav_file.getnchannels()
                except:
                    duration = 0
                    sample_rate = 0
                    channels = 1

                audio_files.append({
                    "filename": filename,
                    "file_size": file_size,
                    "duration": round(duration, 2),
                    "sample_rate": sample_rate,
                    "channels": channels,
                    "modified_time": modified_time,
                    "download_url": f"/download/{filename}",
                    "file_path": os.path.abspath(file_path)
                })

        # 按修改时间排序（最新的在前）
        audio_files.sort(key=lambda x: x["modified_time"], reverse=True)

        return {
            "audio_files": audio_files,
            "total_count": len(audio_files),
            "output_directory": os.path.abspath(OUTPUT_DIR)
        }
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"获取音频文件列表失败: {str(e)}")

@app.delete("/delete_audio/{filename}", summary="删除音频文件")
async def delete_audio(filename: str):
    """删除指定的音频文件"""
    file_path = os.path.join(OUTPUT_DIR, filename)

    if not os.path.exists(file_path):
        raise HTTPException(status_code=404, detail="文件不存在")

    try:
        os.remove(file_path)
        return {
            "success": True,
            "message": f"文件 {filename} 已删除",
            "deleted_file": filename
        }
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"删除文件失败: {str(e)}")

@app.post("/clear_generated_audio", summary="清空所有生成的音频文件")
async def clear_generated_audio():
    """清空所有生成的音频文件"""
    try:
        if not os.path.exists(OUTPUT_DIR):
            return {"message": "输出目录不存在", "deleted_count": 0}

        deleted_count = 0
        for filename in os.listdir(OUTPUT_DIR):
            if filename.lower().endswith('.wav'):
                file_path = os.path.join(OUTPUT_DIR, filename)
                try:
                    os.remove(file_path)
                    deleted_count += 1
                except:
                    pass

        return {
            "success": True,
            "message": f"已清空 {deleted_count} 个音频文件",
            "deleted_count": deleted_count
        }
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"清空音频文件失败: {str(e)}")

@app.get("/health", summary="健康检查")
async def health_check():
    """API健康检查"""
    return {
        "status": "healthy",
        "model_loaded": cosyvoice is not None,
        "output_directory": os.path.abspath(OUTPUT_DIR),
        "output_directory_exists": os.path.exists(OUTPUT_DIR)
    }

if __name__ == '__main__':
    parser = argparse.ArgumentParser(description='CosyVoice FastAPI服务器')
    parser.add_argument('--port', type=int, default=8000, help='服务端口')
    parser.add_argument('--host', type=str, default='0.0.0.0', help='服务主机')
    parser.add_argument('--model_dir', type=str, default='pretrained_models/CosyVoice-300M', 
                       help='模型目录路径')
    args = parser.parse_args()
    
    # 初始化模型
    print(f"正在加载模型: {args.model_dir}")
    try:
        cosyvoice = CosyVoice(args.model_dir)
        print("CosyVoice模型加载成功")
    except Exception as e:
        try:
            cosyvoice = CosyVoice2(args.model_dir)
            print("CosyVoice2模型加载成功")
        except Exception as e2:
            print(f"模型加载失败: {e}, {e2}")
            raise TypeError('无法加载有效的模型类型!')
    
    print(f"启动FastAPI服务器...")
    print(f"服务地址: http://{args.host}:{args.port}")
    print(f"API文档: http://{args.host}:{args.port}/docs")
    print(f"ReDoc文档: http://{args.host}:{args.port}/redoc")
    
    uvicorn.run(app, host=args.host, port=args.port)