"""
文件相关序列化器
"""
from rest_framework import serializers
from .models import AudioFile, FileShare, FileOperation, FileType, StorageType


class AudioFileSerializer(serializers.ModelSerializer):
    """音频文件序列化器"""
    user_display = serializers.CharField(source='user.username', read_only=True)
    file_type_display = serializers.CharField(source='get_file_type_display', read_only=True)
    storage_type_display = serializers.CharField(source='get_storage_type_display', read_only=True)
    file_size_human = serializers.CharField(read_only=True)
    duration_human = serializers.Char<PERSON>ield(read_only=True)
    is_expired = serializers.BooleanField(read_only=True)
    download_url = serializers.SerializerMethodField()
    
    class Meta:
        model = AudioFile
        fields = ('id', 'user_display', 'filename', 'original_filename',
                 'file_type', 'file_type_display', 'storage_type', 'storage_type_display',
                 'file_path', 'file_url', 'file_size', 'file_size_human',
                 'mime_type', 'duration', 'duration_human', 'sample_rate', 'channels',
                 'bit_rate', 'is_public', 'access_count', 'last_accessed',
                 'expires_at', 'is_expired', 'auto_delete', 'metadata', 'tags',
                 'download_url', 'created_at', 'updated_at')
    
    def get_download_url(self, obj):
        """获取下载URL"""
        request = self.context.get('request')
        if request:
            return request.build_absolute_uri(f'/download/{obj.filename}')
        return f'/download/{obj.filename}'


class AudioFileListSerializer(serializers.ModelSerializer):
    """音频文件列表序列化器"""
    user_display = serializers.CharField(source='user.username', read_only=True)
    file_type_display = serializers.CharField(source='get_file_type_display', read_only=True)
    file_size_human = serializers.CharField(read_only=True)
    duration_human = serializers.CharField(read_only=True)
    is_expired = serializers.BooleanField(read_only=True)
    
    class Meta:
        model = AudioFile
        fields = ('id', 'user_display', 'filename', 'original_filename',
                 'file_type', 'file_type_display', 'file_size', 'file_size_human',
                 'duration', 'duration_human', 'is_public', 'access_count',
                 'expires_at', 'is_expired', 'created_at')


class AudioFileUploadSerializer(serializers.ModelSerializer):
    """音频文件上传序列化器"""
    file = serializers.FileField(write_only=True)
    
    class Meta:
        model = AudioFile
        fields = ('file', 'file_type', 'is_public', 'expires_at', 'auto_delete', 'tags')
    
    def validate_file(self, value):
        """验证文件"""
        # 检查文件大小 (50MB)
        max_size = 50 * 1024 * 1024
        if value.size > max_size:
            raise serializers.ValidationError(f'文件大小不能超过 {max_size // (1024*1024)}MB')
        
        # 检查文件类型
        allowed_types = ['audio/wav', 'audio/mpeg', 'audio/mp3', 'audio/flac', 'audio/ogg']
        if value.content_type not in allowed_types:
            raise serializers.ValidationError(f'不支持的文件类型: {value.content_type}')
        
        return value
    
    def create(self, validated_data):
        file = validated_data.pop('file')
        request = self.context.get('request')
        
        # 保存文件并创建记录
        from .services import FileService
        file_service = FileService()
        
        audio_file = file_service.save_uploaded_file(
            file=file,
            user=request.user if request.user.is_authenticated else None,
            **validated_data
        )
        
        return audio_file


class FileShareSerializer(serializers.ModelSerializer):
    """文件分享序列化器"""
    audio_file_name = serializers.CharField(source='audio_file.filename', read_only=True)
    is_expired = serializers.BooleanField(read_only=True)
    is_download_limit_reached = serializers.BooleanField(read_only=True)
    is_valid = serializers.BooleanField(read_only=True)
    share_url = serializers.SerializerMethodField()
    
    class Meta:
        model = FileShare
        fields = ('id', 'audio_file_name', 'share_token', 'password',
                 'expires_at', 'max_downloads', 'download_count',
                 'is_expired', 'is_download_limit_reached', 'is_valid',
                 'share_url', 'created_at')
        read_only_fields = ('share_token', 'download_count')
    
    def get_share_url(self, obj):
        """获取分享URL"""
        request = self.context.get('request')
        if request:
            return request.build_absolute_uri(f'/share/{obj.share_token}')
        return f'/share/{obj.share_token}'
    
    def create(self, validated_data):
        import secrets
        validated_data['share_token'] = secrets.token_urlsafe(32)
        return super().create(validated_data)


class FileOperationSerializer(serializers.ModelSerializer):
    """文件操作记录序列化器"""
    user_display = serializers.CharField(source='user.username', read_only=True)
    audio_file_name = serializers.CharField(source='audio_file.filename', read_only=True)
    operation_display = serializers.CharField(source='get_operation_display', read_only=True)
    
    class Meta:
        model = FileOperation
        fields = ('id', 'user_display', 'audio_file_name', 'operation',
                 'operation_display', 'ip_address', 'user_agent',
                 'extra_data', 'created_at')


class FileStatsSerializer(serializers.Serializer):
    """文件统计序列化器"""
    total_files = serializers.IntegerField()
    total_size = serializers.IntegerField()
    total_size_human = serializers.CharField()
    by_type = serializers.DictField()
    by_storage = serializers.DictField()
    recent_uploads = serializers.IntegerField()
    expired_files = serializers.IntegerField()


class FileBatchOperationSerializer(serializers.Serializer):
    """文件批量操作序列化器"""
    file_ids = serializers.ListField(
        child=serializers.UUIDField(),
        allow_empty=False
    )
    operation = serializers.ChoiceField(choices=[
        ('delete', '删除'),
        ('make_public', '设为公开'),
        ('make_private', '设为私有'),
        ('extend_expiry', '延长过期时间'),
    ])
    
    # 延长过期时间的天数
    extend_days = serializers.IntegerField(min_value=1, max_value=365, required=False)
    
    def validate(self, attrs):
        if attrs['operation'] == 'extend_expiry' and not attrs.get('extend_days'):
            raise serializers.ValidationError('延长过期时间需要指定天数')
        return attrs


class FileCleanupSerializer(serializers.Serializer):
    """文件清理序列化器"""
    cleanup_type = serializers.ChoiceField(choices=[
        ('expired', '过期文件'),
        ('temp', '临时文件'),
        ('orphaned', '孤儿文件'),
        ('old', '旧文件'),
    ])
    days = serializers.IntegerField(min_value=1, max_value=365, required=False)
    
    def validate(self, attrs):
        if attrs['cleanup_type'] == 'old' and not attrs.get('days'):
            raise serializers.ValidationError('清理旧文件需要指定天数')
        return attrs
