"""
任务相关序列化器
"""
from rest_framework import serializers
from .models import Task, TaskLog, TaskType, TaskStatus, TaskPriority
from apps.files.serializers import AudioFileSerializer


class TaskCreateSerializer(serializers.ModelSerializer):
    """任务创建序列化器"""
    
    class Meta:
        model = Task
        fields = ('task_type', 'request_data', 'priority')
        
    def validate_request_data(self, value):
        """验证请求数据"""
        task_type = self.initial_data.get('task_type')
        
        if task_type == TaskType.SFT:
            required_fields = ['tts_text', 'spk_id']
        elif task_type == TaskType.ZERO_SHOT:
            required_fields = ['tts_text', 'prompt_text', 'prompt_wav']
        elif task_type == TaskType.CROSS_LINGUAL:
            required_fields = ['tts_text', 'prompt_wav']
        elif task_type in [TaskType.INSTRUCT, TaskType.INSTRUCT2]:
            required_fields = ['tts_text', 'spk_id', 'instruct_text']
        else:
            raise serializers.ValidationError('不支持的任务类型')
        
        for field in required_fields:
            if field not in value:
                raise serializers.ValidationError(f'缺少必需字段: {field}')
        
        return value
    
    def create(self, validated_data):
        # 从请求上下文中获取用户和客户端信息
        request = self.context.get('request')
        if request:
            validated_data['user'] = request.user if request.user.is_authenticated else None
            validated_data['client_ip'] = self.get_client_ip(request)
            validated_data['user_agent'] = request.META.get('HTTP_USER_AGENT', '')
        
        return Task.objects.create(**validated_data)
    
    def get_client_ip(self, request):
        """获取客户端IP"""
        x_forwarded_for = request.META.get('HTTP_X_FORWARDED_FOR')
        if x_forwarded_for:
            ip = x_forwarded_for.split(',')[0]
        else:
            ip = request.META.get('REMOTE_ADDR')
        return ip


class TaskSerializer(serializers.ModelSerializer):
    """任务序列化器"""
    user_display = serializers.CharField(source='user.username', read_only=True)
    task_type_display = serializers.CharField(source='get_task_type_display', read_only=True)
    status_display = serializers.CharField(source='get_status_display', read_only=True)
    priority_display = serializers.CharField(source='get_priority_display', read_only=True)
    audio_file = AudioFileSerializer(read_only=True)
    can_retry = serializers.BooleanField(read_only=True)
    
    class Meta:
        model = Task
        fields = ('id', 'user_display', 'task_type', 'task_type_display', 
                 'status', 'status_display', 'priority', 'priority_display',
                 'request_data', 'response_data', 'audio_file',
                 'started_at', 'completed_at', 'error_message', 
                 'retry_count', 'max_retries', 'can_retry',
                 'processing_time', 'queue_time', 'client_ip',
                 'created_at', 'updated_at')


class TaskListSerializer(serializers.ModelSerializer):
    """任务列表序列化器"""
    user_display = serializers.CharField(source='user.username', read_only=True)
    task_type_display = serializers.CharField(source='get_task_type_display', read_only=True)
    status_display = serializers.CharField(source='get_status_display', read_only=True)
    
    class Meta:
        model = Task
        fields = ('id', 'user_display', 'task_type', 'task_type_display',
                 'status', 'status_display', 'started_at', 'completed_at',
                 'processing_time', 'created_at')


class TaskLogSerializer(serializers.ModelSerializer):
    """任务日志序列化器"""
    
    class Meta:
        model = TaskLog
        fields = ('id', 'level', 'message', 'extra_data', 'created_at')


class TaskStatsSerializer(serializers.Serializer):
    """任务统计序列化器"""
    total_tasks = serializers.IntegerField()
    pending_tasks = serializers.IntegerField()
    processing_tasks = serializers.IntegerField()
    completed_tasks = serializers.IntegerField()
    failed_tasks = serializers.IntegerField()
    cancelled_tasks = serializers.IntegerField()
    avg_processing_time = serializers.FloatField()
    avg_queue_time = serializers.FloatField()
    success_rate = serializers.FloatField()


class TaskRetrySerializer(serializers.Serializer):
    """任务重试序列化器"""
    task_ids = serializers.ListField(
        child=serializers.UUIDField(),
        allow_empty=False
    )
    
    def validate_task_ids(self, value):
        """验证任务ID"""
        user = self.context['request'].user
        
        # 检查任务是否存在且属于当前用户
        tasks = Task.objects.filter(id__in=value)
        
        if not user.is_superuser:
            tasks = tasks.filter(user=user)
        
        if len(tasks) != len(value):
            raise serializers.ValidationError('部分任务不存在或无权限访问')
        
        # 检查任务是否可以重试
        non_retryable = tasks.exclude(status=TaskStatus.FAILED)
        if non_retryable.exists():
            raise serializers.ValidationError('只能重试失败的任务')
        
        return value


class TaskCancelSerializer(serializers.Serializer):
    """任务取消序列化器"""
    task_ids = serializers.ListField(
        child=serializers.UUIDField(),
        allow_empty=False
    )
    
    def validate_task_ids(self, value):
        """验证任务ID"""
        user = self.context['request'].user
        
        # 检查任务是否存在且属于当前用户
        tasks = Task.objects.filter(id__in=value)
        
        if not user.is_superuser:
            tasks = tasks.filter(user=user)
        
        if len(tasks) != len(value):
            raise serializers.ValidationError('部分任务不存在或无权限访问')
        
        # 检查任务是否可以取消
        non_cancellable = tasks.exclude(status__in=[TaskStatus.PENDING, TaskStatus.PROCESSING])
        if non_cancellable.exists():
            raise serializers.ValidationError('只能取消等待中或处理中的任务')
        
        return value
