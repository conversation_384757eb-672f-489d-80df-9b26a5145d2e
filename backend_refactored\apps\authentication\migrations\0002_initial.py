# Generated by Django 4.2.7 on 2025-07-24 03:28

from django.conf import settings
from django.db import migrations, models
import django.db.models.deletion


class Migration(migrations.Migration):
    initial = True

    dependencies = [
        ("authentication", "0001_initial"),
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
    ]

    operations = [
        migrations.AddField(
            model_name="passwordresettoken",
            name="user",
            field=models.ForeignKey(
                on_delete=django.db.models.deletion.CASCADE,
                related_name="reset_tokens",
                to=settings.AUTH_USER_MODEL,
            ),
        ),
        migrations.AddField(
            model_name="loginlog",
            name="user",
            field=models.ForeignKey(
                on_delete=django.db.models.deletion.CASCADE,
                related_name="login_logs",
                to=settings.AUTH_USER_MODEL,
            ),
        ),
        migrations.AddField(
            model_name="apikey",
            name="user",
            field=models.ForeignKey(
                on_delete=django.db.models.deletion.CASCADE,
                related_name="api_keys",
                to=settings.AUTH_USER_MODEL,
            ),
        ),
        migrations.AddIndex(
            model_name="passwordresettoken",
            index=models.Index(fields=["token"], name="password_re_token_060a1f_idx"),
        ),
        migrations.AddIndex(
            model_name="passwordresettoken",
            index=models.Index(
                fields=["user", "used"], name="password_re_user_id_4cd856_idx"
            ),
        ),
        migrations.AddIndex(
            model_name="passwordresettoken",
            index=models.Index(
                fields=["expires_at"], name="password_re_expires_8e96b7_idx"
            ),
        ),
        migrations.AddIndex(
            model_name="loginlog",
            index=models.Index(
                fields=["user", "created_at"], name="login_logs_user_id_783da5_idx"
            ),
        ),
        migrations.AddIndex(
            model_name="loginlog",
            index=models.Index(
                fields=["ip_address"], name="login_logs_ip_addr_669c36_idx"
            ),
        ),
        migrations.AddIndex(
            model_name="loginlog",
            index=models.Index(
                fields=["success"], name="login_logs_success_78daf7_idx"
            ),
        ),
        migrations.AddIndex(
            model_name="apikey",
            index=models.Index(fields=["user"], name="api_keys_user_id_1863c8_idx"),
        ),
        migrations.AddIndex(
            model_name="apikey",
            index=models.Index(fields=["key_hash"], name="api_keys_key_has_8f2f29_idx"),
        ),
        migrations.AddIndex(
            model_name="apikey",
            index=models.Index(
                fields=["is_active"], name="api_keys_is_acti_73be43_idx"
            ),
        ),
        migrations.AddIndex(
            model_name="apikey",
            index=models.Index(
                fields=["expires_at"], name="api_keys_expires_f68b1a_idx"
            ),
        ),
    ]
