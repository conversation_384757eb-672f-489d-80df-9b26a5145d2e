---
description: 重构项目开发流程，适用于对现有系统进行重构、优化和功能扩展
globs: 
alwaysApply: false
---
# 重构开发流程概述 (Refactoring Development Process Overview)

## 流程特点说明

重构开发流程与新品开发流程的核心差异在于：
- **起点不同**：从现有代码库和系统现状开始，而非产品构想
- **重点不同**：注重分析现状、识别问题、平衡创新与兼容性
- **风险控制**：重点关注数据迁移、向后兼容性和用户使用连续性

## 00_代码考古学家 (Code Archaeologist)

### 输入
- 现有项目代码库完整访问权限
- 项目核心目标和业务背景描述
- 重构意图和期望改进方向
- 已知的技术债务或问题点（可选）
- 现有文档资料（可选）

### 输出
- 项目现状蓝图 (Project Status Blueprint): 保存到 `analysis/Project_Status_Blueprint.md`
- 技术栈分析报告 (Tech Stack Analysis): 保存到 `analysis/Tech_Stack_Analysis.md`
- 代码质量评估报告 (Code Quality Assessment): 保存到 `analysis/Code_Quality_Assessment.md`
- 重构风险评估 (Refactoring Risk Assessment): 保存到 `analysis/Refactoring_Risk_Assessment.md`
- 依赖关系图 (Dependency Map): 保存到 `analysis/Dependency_Map.md`

### 核心职责
- 深度分析现有代码库结构、技术栈和业务逻辑
- 识别技术债务、性能瓶颈和安全隐患
- 梳理模块依赖关系和数据流
- 提取现有系统的隐性规则和约束
- 为后续重构提供全面的现状基础

## 01_产品经理 (Product Manager - Refactoring Focus)

### 输入
- 项目现状蓝图: 从 `analysis/Project_Status_Blueprint.md` 获取
- 技术栈分析报告: 从 `analysis/Tech_Stack_Analysis.md` 获取
- 现有用户反馈和使用数据（可选）
- 业务发展需求和新功能构想
- 重构的业务目标和约束条件

### 输出
- 重构需求文档 (Refactoring Requirements Document - RRD): 保存到 `docs/RRD.md`
- 功能演进路线图 (Feature Evolution Roadmap): 保存到 `docs/Feature_Evolution_Roadmap.md`
- 用户影响评估 (User Impact Assessment): 保存到 `docs/User_Impact_Assessment.md`
- 数据迁移策略 (Data Migration Strategy): 保存到 `docs/Data_Migration_Strategy.md`

### 核心调整
- 基于现状分析制定重构策略，平衡新功能与现有功能的关系
- 重点关注现有用户的使用习惯和数据连续性
- 设计渐进式功能演进方案，避免激进变更
- 制定详细的用户迁移和适应计划

## 02_技术架构师 (Technical Architect - Refactoring Focus)

### 输入
- 项目现状蓝图: 从 `analysis/Project_Status_Blueprint.md` 获取
- 代码质量评估报告: 从 `analysis/Code_Quality_Assessment.md` 获取
- 重构风险评估: 从 `analysis/Refactoring_Risk_Assessment.md` 获取
- 重构需求文档 (RRD): 从 `docs/RRD.md` 获取
- 功能演进路线图: 从 `docs/Feature_Evolution_Roadmap.md` 获取

### 输出
- 重构技术架构文档 (Refactoring Technical Architecture): 保存到 `docs/Refactoring_Technical_Architecture.md`
- 架构演进策略 (Architecture Evolution Strategy): 保存到 `docs/Architecture_Evolution_Strategy.md`
- 技术债务清偿计划 (Technical Debt Resolution Plan): 保存到 `docs/Technical_Debt_Resolution_Plan.md`
- 架构决策记录 (ADRs): 保存到 `docs/ADRs/` 目录下

### 核心调整
- 设计渐进式架构演进策略，确保平滑过渡
- 制定技术债务清偿的优先级和实施计划
- 平衡新架构设计与现有系统兼容性
- 设计数据迁移和系统切换的技术方案

## 03_UI设计师 (UIUX Designer - Refactoring Focus)

### 输入
- 重构需求文档 (RRD): 从 `docs/RRD.md` 获取，关注用户体验改进需求
- 用户影响评估: 从 `docs/User_Impact_Assessment.md` 获取
- 重构技术架构文档: 从 `docs/Refactoring_Technical_Architecture.md` 获取
- 现有界面设计和用户反馈（可选）

### 输出
- 界面重构设计方案: 保存到目录 `design/refactoring_prototypes/`
- 用户体验改进方案: 保存为 `design/UX_Improvement_Plan.md`
- 界面迁移指南: 保存到 `design/UI_Migration_Guide.md`
- 设计一致性规范: 保存到 `design/specs/Design_Consistency_Spec.md`

### 核心调整
- 在保持用户使用习惯的基础上优化界面设计
- 设计渐进式的界面改进方案
- 重点关注用户学习成本和适应性
- 制定新旧界面的过渡策略

## 04_后端开发者 (Backend Developer - Refactoring Focus)

### 输入
- 重构需求文档 (RRD): 从 `docs/RRD.md` 获取
- 重构技术架构文档: 从 `docs/Refactoring_Technical_Architecture.md` 获取
- 技术债务清偿计划: 从 `docs/Technical_Debt_Resolution_Plan.md` 获取
- 数据迁移策略: 从 `docs/Data_Migration_Strategy.md` 获取
- 现有代码库和数据库结构
- UI重构设计方案: 从 `design/refactoring_prototypes/` 获取

### 输出
- 重构后端服务代码: 位于 `backend_refactored/`
- 数据迁移脚本和文档: 位于 `migration/backend/`
- API兼容性文档: 位于 `backend_refactored/API_Compatibility.md`
- 重构实施文档: 位于 `backend_refactored/Refactoring_Implementation.md`

### 核心调整
- 实施渐进式代码重构，确保服务连续性
- 开发数据迁移脚本和回滚方案
- 维护API向后兼容性或提供迁移指南
- 重点解决识别出的技术债务和性能问题

## 05_前端开发者_移动端 (Mobile Frontend Developer - Refactoring Focus)

### 输入
- 重构需求文档 (RRD): 从 `docs/RRD.md` 获取
- 界面重构设计方案: 从 `design/refactoring_prototypes/` 获取
- 重构技术架构文档: 从 `docs/Refactoring_Technical_Architecture.md` 获取
- 后端API兼容性文档: 从 `backend_refactored/API_Compatibility.md` 获取
- 现有移动端代码库

### 输出
- 重构移动端项目代码: 保存到目录 `frontend_mobile_refactored/`
- 界面迁移实施文档: 保存到 `frontend_mobile_refactored/UI_Migration_Implementation.md`
- 用户数据迁移客户端处理: 保存到 `migration/frontend_mobile/`

### 核心调整
- 实施渐进式界面重构，减少用户学习成本
- 处理客户端数据迁移和本地存储升级
- 确保新旧版本的平滑过渡
- 优化性能和用户体验问题

## 05.1_前端开发者_管理后台 (Admin Frontend Developer - Refactoring Focus)

### 输入
- 重构需求文档 (RRD): 从 `docs/RRD.md` 获取，重点关注管理功能改进需求
- 界面重构设计方案: 从 `design/refactoring_prototypes/` 获取
- 重构技术架构文档: 从 `docs/Refactoring_Technical_Architecture.md` 获取
- 后端API兼容性文档: 从 `backend_refactored/API_Compatibility.md` 获取
- 现有管理后台代码库

### 输出
- 重构管理后台项目代码: 保存到目录 `admin_frontend_refactored/`
- 管理功能升级文档: 保存到 `admin_frontend_refactored/Admin_Feature_Upgrade.md`
- 管理员迁移指南: 保存到 `admin_frontend_refactored/Admin_Migration_Guide.md`

### 核心调整
- 重构管理界面，提升操作效率和数据可视化
- 处理管理数据的迁移和权限升级
- 确保管理员工作流程的连续性
- 增强数据分析和监控能力

## 05.2_桌面应用开发者 (PyQt Desktop Developer - Refactoring Focus)

### 输入
- 项目现状蓝图: 从 `analysis/Project_Status_Blueprint.md` 获取现有系统分析
- 重构需求文档 (RRD): 从 `docs/RRD.md` 获取重构目标和新功能需求
- 重构技术架构: 从 `docs/Refactoring_Technical_Architecture.md` 获取架构设计
- 重构界面设计: 从 `design/specs/Refactoring_UI_Design_Specification.md` 获取设计规范
- 用户体验连续性方案: 从 `design/plans/UX_Continuity_Plan.md` 获取体验保障方案

### 输出
- 重构桌面应用代码: 保存到目录 `desktop_app_refactored/`
- PyQt技术实现文档: 保存到 `docs/technical/PyQt_Implementation_Guide.md`
- 桌面应用部署指南: 保存到 `docs/deployment/PyQt_Deployment_Guide.md`
- 用户迁移指南: 保存到 `docs/user/Desktop_User_Migration_Guide.md`

### 核心调整
- 重构PyQt桌面应用，保持跨平台兼容性
- 优化界面响应性能和用户体验
- 确保用户操作习惯和数据的连续性
- 实现现代化的桌面应用界面设计
- 建立可扩展的桌面应用架构

## 06_测试工程师 (QA Engineer - Refactoring Focus)

### 输入
- 重构需求文档 (RRD): 从 `docs/RRD.md` 获取
- 重构技术架构文档: 从 `docs/Refactoring_Technical_Architecture.md` 获取
- 用户影响评估: 从 `docs/User_Impact_Assessment.md` 获取
- 所有重构后的代码库和文档
- 现有系统的测试用例和数据

### 输出
- 重构测试策略: 保存到 `testing/Refactoring_Test_Strategy.md`
- 回归测试计划: 保存到 `testing/Regression_Test_Plan.md`
- 数据迁移测试报告: 保存到 `testing/Data_Migration_Test_Report.md`
- 性能对比测试报告: 保存到 `testing/Performance_Comparison_Report.md`
- 用户验收测试方案: 保存到 `testing/User_Acceptance_Test_Plan.md`

### 核心调整
- 重点进行回归测试，确保现有功能不受影响
- 验证数据迁移的完整性和正确性
- 对比重构前后的性能指标
- 设计用户验收测试，验证重构效果
- 制定生产环境发布的测试策略

## 重构项目协作与风险控制规范

### 核心原则
1. **渐进式重构**：避免大爆炸式的系统替换，采用渐进式改进
2. **向后兼容**：确保现有用户和数据的平滑迁移
3. **风险可控**：每个重构步骤都有回滚方案
4. **用户优先**：重构过程中始终保证用户服务不中断

### 特殊协作流程

1. **现状分析阶段**:
   - 代码考古学家深度分析现有系统
   - 所有角色参与现状分析结果评审
   - 识别重构的关键风险点和约束条件

2. **重构策略制定**:
   - 产品经理基于现状制定重构需求
   - 技术架构师设计渐进式演进方案
   - 所有角色评审重构策略的可行性

3. **分阶段实施**:
   - 按照优先级分阶段重构不同模块
   - 每个阶段完成后进行全面测试和验证
   - 确保每个阶段都是可独立发布的版本

4. **持续监控**:
   - 重构过程中持续监控系统性能和用户反馈
   - 建立快速响应机制处理重构引入的问题
   - 定期评估重构进度和效果

## 重构流程协作图

```
    +----------------------+
    |                      |
    |  00_代码考古学家     |
    |  (项目现状分析)      |
    |                      |
    +----------+-----------+
               |
               v
+----------------------+     +----------------------+     +----------------------+
|                      |     |                      |     |                      |
|   01_产品经理        +---->+   02_技术架构师      +---->+   03_UI设计师        |
|  (重构需求管理)      |     |  (架构演进设计)      |     |  (界面重构设计)      |
|                      |     |                      |     |                      |
+----------------------+     +----------+-----------+     +-----------+----------+
                                        |                             |
                                        v                             v
                               +--------+---------+           +-------+---------+
                               |                  |           |                 |
                               | 04_后端开发者    |<--------->|  05_前端开发者  |
                               | (服务重构实施)   |           |   (移动端重构)  |
                               +--------+---------+           +---------+-------+
                                        |                             |
                                        |                             |
                                        |                             |
                                        |                    +--------v----------+
                                        |                    |                   |
                                        |                    | 05.1_前端开发者   |
                                        |                    |  (管理后台重构)   |
                                        |                    |                   |
                                        |                    +---------+---------+
                                        |                              |
                                        |                              |
                                        |                    +---------v---------+
                                        |                    |                   |
                                        |                    | 05.2_桌面应用开发者|
                                        |                    |   (PyQt重构)      |
                                        |                    |                   |
                                        |                    +---------+---------+
                                        |                              |
                                        v                              v
                               +--------+-----------------------------+---------+
                               |                                                |
                               |            06_测试工程师                        |
                               |           (重构质量保证)                        |
                               |                                                |
                               +------------------------------------------------+
```

上图展示了重构开发流程中各角色的协作关系：
- 代码考古学家为整个重构流程提供现状分析基础
- 产品经理基于现状分析制定重构需求和策略
- 技术架构师设计渐进式的架构演进方案
- 设计师重构界面设计，保持用户体验连续性
- 开发者实施代码和界面重构，确保平滑迁移：
  - 后端开发者负责服务端重构和API演进
  - 前端开发者负责移动端应用重构
  - 管理后台开发者负责管理界面重构
  - PyQt桌面应用开发者负责桌面端重构
- 测试工程师进行全面的回归测试和质量保证

### 重构项目成功关键因素

1. **深度现状分析**：充分理解现有系统的复杂性和约束
2. **渐进式改进**：避免激进变更，确保每一步都是安全的
3. **用户体验连续性**：重构过程中保持用户使用的流畅性
4. **完整的回归测试**：确保重构不破坏现有功能
5. **数据安全保障**：确保数据迁移的完整性和一致性

## 协同检查清单 (Collaboration Checklist)

### 关键协同节点验证

#### 1. 代码考古学家 → 产品经理交接点
- [ ] 《项目现状蓝图》是否包含完整的技术架构分析？
- [ ] 风险评估是否覆盖了所有重构关键点？
- [ ] 现有用户习惯和业务流程是否充分记录？
- [ ] 技术债务清单是否按优先级排序？

#### 2. 产品经理 → 技术架构师交接点
- [ ] RRD是否明确了渐进式重构的阶段划分？
- [ ] 用户影响评估是否提供了具体的缓解措施？
- [ ] 数据迁移策略是否考虑了所有数据类型？
- [ ] 功能演进路线图是否与技术可行性匹配？

#### 3. 技术架构师 → 设计师交接点
- [ ] 重构技术架构是否明确了界面实现的技术约束？
- [ ] 架构演进策略是否考虑了用户界面的渐进式升级？
- [ ] API演进策略是否支持前端的分阶段适配？

#### 4. 设计师 → 开发团队交接点
- [ ] 界面重构设计是否提供了详细的实现规范？
- [ ] 用户体验连续性方案是否可执行？
- [ ] 渐进式升级指南是否技术可行？
- [ ] 设计系统是否与现有技术栈兼容？

#### 5. 开发团队内部协同检查
- [ ] 后端API变更是否及时通知前端团队？
- [ ] 移动端和管理后台是否保持设计一致性？
- [ ] 数据迁移脚本是否经过所有相关开发者评审？
- [ ] 新功能集成是否考虑了所有客户端的适配？

#### 6. 开发团队 → 测试团队交接点
- [ ] 重构实施文档是否提供了完整的测试依据？
- [ ] 数据迁移脚本是否附带了验证方案？
- [ ] 性能优化目标是否可量化测试？
- [ ] 回滚方案是否经过测试验证？

### 跨角色协同要求

#### A. 数据安全协同机制
- **参与角色**: 代码考古学家、产品经理、技术架构师、后端开发者、测试工程师
- **协同要求**: 
  - 数据迁移方案必须经过所有相关角色评审
  - 数据备份和回滚方案必须在实施前测试验证
  - 敏感数据处理必须符合安全和合规要求

#### B. 用户体验连续性保证机制
- **参与角色**: 产品经理、UI设计师、前端开发者、测试工程师
- **协同要求**:
  - 界面变更必须经过用户影响评估
  - 操作流程变化必须有用户引导方案
  - 关键用户路径必须保持功能一致性

#### C. 技术风险控制机制
- **参与角色**: 代码考古学家、技术架构师、所有开发者、测试工程师
- **协同要求**:
  - 重构方案必须有明确的回滚计划
  - 每个重构阶段必须有独立的测试验证
  - 生产环境变更必须有应急响应预案

#### D. 质量门禁机制
- **参与角色**: 所有角色
- **协同要求**:
  - 每个阶段完成必须通过质量检查清单
  - 关键里程碑必须有跨角色的评审确认
  - 问题发现和解决必须有完整的追踪记录

### 协同工具和规范

#### 1. 文档版本管理
- 所有输出文档必须标注版本号和更新时间
- 文档变更必须通知相关协作角色
- 关键决策文档必须保留变更历史

#### 2. 沟通机制
- 建立定期的跨角色沟通会议
- 建立问题升级和决策机制
- 建立知识共享和经验总结机制

#### 3. 质量保证
- 建立跨角色的代码和文档评审机制
- 建立持续的质量监控和反馈机制
- 建立问题根因分析和改进机制

