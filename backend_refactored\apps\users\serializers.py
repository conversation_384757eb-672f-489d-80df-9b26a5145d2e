"""
用户相关序列化器
"""
from rest_framework import serializers
from django.contrib.auth import get_user_model
from .models import UserProfile

User = get_user_model()


class UserProfileSerializer(serializers.ModelSerializer):
    """用户资料序列化器"""
    
    class Meta:
        model = UserProfile
        fields = ('phone', 'address', 'birth_date', 'company', 'website',
                 'language', 'timezone', 'email_notifications')


class UserSerializer(serializers.ModelSerializer):
    """用户序列化器"""
    profile = UserProfileSerializer(read_only=True)
    quota_remaining = serializers.IntegerField(read_only=True)
    quota_usage_percentage = serializers.FloatField(read_only=True)
    
    class Meta:
        model = User
        fields = ('id', 'username', 'email', 'role', 'avatar_url', 'bio',
                 'quota_limit', 'quota_used', 'quota_remaining', 'quota_usage_percentage',
                 'is_active', 'created_at', 'updated_at', 'last_login', 'profile')
        read_only_fields = ('id', 'quota_used', 'created_at', 'updated_at', 'last_login')


class UserUpdateSerializer(serializers.ModelSerializer):
    """用户更新序列化器"""
    profile = UserProfileSerializer(required=False)
    
    class Meta:
        model = User
        fields = ('username', 'avatar_url', 'bio', 'profile')
    
    def update(self, instance, validated_data):
        profile_data = validated_data.pop('profile', None)
        
        # 更新用户基本信息
        for attr, value in validated_data.items():
            setattr(instance, attr, value)
        instance.save()
        
        # 更新用户资料
        if profile_data:
            profile, created = UserProfile.objects.get_or_create(user=instance)
            for attr, value in profile_data.items():
                setattr(profile, attr, value)
            profile.save()
        
        return instance


class UserListSerializer(serializers.ModelSerializer):
    """用户列表序列化器"""
    quota_usage_percentage = serializers.FloatField(read_only=True)
    
    class Meta:
        model = User
        fields = ('id', 'username', 'email', 'role', 'quota_limit', 'quota_used',
                 'quota_usage_percentage', 'is_active', 'created_at', 'last_login')


class UserStatsSerializer(serializers.Serializer):
    """用户统计序列化器"""
    total_tasks = serializers.IntegerField()
    completed_tasks = serializers.IntegerField()
    failed_tasks = serializers.IntegerField()
    total_files = serializers.IntegerField()
    total_file_size = serializers.IntegerField()
    quota_used = serializers.IntegerField()
    quota_limit = serializers.IntegerField()
    quota_usage_percentage = serializers.FloatField()
    last_activity = serializers.DateTimeField()


class QuotaUpdateSerializer(serializers.Serializer):
    """配额更新序列化器"""
    quota_limit = serializers.IntegerField(min_value=0)
    
    def validate_quota_limit(self, value):
        user = self.context.get('user')
        if user and value < user.quota_used:
            raise serializers.ValidationError(
                f'配额限制不能小于已使用配额 ({user.quota_used})'
            )
        return value
