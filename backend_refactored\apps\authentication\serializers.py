"""
认证相关序列化器
"""
from rest_framework import serializers
from django.contrib.auth import authenticate, get_user_model
from django.contrib.auth.password_validation import validate_password
from .models import APIKey, LoginLog
from apps.users.models import UserRole

User = get_user_model()


class UserRegistrationSerializer(serializers.ModelSerializer):
    """用户注册序列化器"""
    password = serializers.CharField(write_only=True, validators=[validate_password])
    password_confirm = serializers.CharField(write_only=True)
    
    class Meta:
        model = User
        fields = ('username', 'email', 'password', 'password_confirm', 'role')
        extra_kwargs = {
            'role': {'default': UserRole.BASIC}
        }
    
    def validate(self, attrs):
        if attrs['password'] != attrs['password_confirm']:
            raise serializers.ValidationError("密码不匹配")
        return attrs
    
    def validate_email(self, value):
        if User.objects.filter(email=value).exists():
            raise serializers.ValidationError("邮箱已被注册")
        return value
    
    def validate_username(self, value):
        if User.objects.filter(username=value).exists():
            raise serializers.ValidationError("用户名已被使用")
        return value
    
    def create(self, validated_data):
        validated_data.pop('password_confirm')
        password = validated_data.pop('password')
        user = User.objects.create_user(password=password, **validated_data)
        return user


class UserLoginSerializer(serializers.Serializer):
    """用户登录序列化器"""
    email = serializers.EmailField()
    password = serializers.CharField(write_only=True)
    
    def validate(self, attrs):
        email = attrs.get('email')
        password = attrs.get('password')
        
        if email and password:
            user = authenticate(
                request=self.context.get('request'),
                username=email,
                password=password
            )
            
            if not user:
                raise serializers.ValidationError('邮箱或密码错误')
            
            if not user.is_active:
                raise serializers.ValidationError('账户已被禁用')
            
            attrs['user'] = user
            return attrs
        else:
            raise serializers.ValidationError('必须提供邮箱和密码')


class PasswordChangeSerializer(serializers.Serializer):
    """密码修改序列化器"""
    old_password = serializers.CharField(write_only=True)
    new_password = serializers.CharField(write_only=True, validators=[validate_password])
    new_password_confirm = serializers.CharField(write_only=True)
    
    def validate_old_password(self, value):
        user = self.context['request'].user
        if not user.check_password(value):
            raise serializers.ValidationError('原密码错误')
        return value
    
    def validate(self, attrs):
        if attrs['new_password'] != attrs['new_password_confirm']:
            raise serializers.ValidationError('新密码不匹配')
        return attrs
    
    def save(self):
        user = self.context['request'].user
        user.set_password(self.validated_data['new_password'])
        user.save()
        return user


class APIKeySerializer(serializers.ModelSerializer):
    """API密钥序列化器"""
    key = serializers.CharField(read_only=True)
    key_display = serializers.SerializerMethodField()
    
    class Meta:
        model = APIKey
        fields = ('id', 'name', 'key', 'key_display', 'permissions', 'expires_at', 
                 'last_used', 'usage_count', 'is_active', 'created_at')
        read_only_fields = ('key', 'last_used', 'usage_count', 'created_at')
    
    def get_key_display(self, obj):
        """显示密钥前缀"""
        return f"{obj.key_prefix}..." if obj.key_prefix else "***"
    
    def create(self, validated_data):
        validated_data['user'] = self.context['request'].user
        api_key = APIKey.objects.create(**validated_data)
        # 返回原始密钥供显示
        if hasattr(api_key, '_raw_key'):
            api_key.key = api_key._raw_key
        return api_key


class APIKeyListSerializer(serializers.ModelSerializer):
    """API密钥列表序列化器"""
    key_display = serializers.SerializerMethodField()
    is_expired = serializers.BooleanField(read_only=True)
    
    class Meta:
        model = APIKey
        fields = ('id', 'name', 'key_display', 'permissions', 'expires_at', 
                 'last_used', 'usage_count', 'is_active', 'is_expired', 'created_at')
    
    def get_key_display(self, obj):
        return f"{obj.key_prefix}..." if obj.key_prefix else "***"


class LoginLogSerializer(serializers.ModelSerializer):
    """登录日志序列化器"""
    user_display = serializers.CharField(source='user.username', read_only=True)
    
    class Meta:
        model = LoginLog
        fields = ('id', 'user_display', 'ip_address', 'user_agent', 'login_type', 
                 'success', 'failure_reason', 'created_at')


class PasswordResetRequestSerializer(serializers.Serializer):
    """密码重置请求序列化器"""
    email = serializers.EmailField()
    
    def validate_email(self, value):
        try:
            User.objects.get(email=value, is_active=True)
        except User.DoesNotExist:
            raise serializers.ValidationError('邮箱不存在或账户已被禁用')
        return value


class PasswordResetConfirmSerializer(serializers.Serializer):
    """密码重置确认序列化器"""
    token = serializers.CharField()
    new_password = serializers.CharField(write_only=True, validators=[validate_password])
    new_password_confirm = serializers.CharField(write_only=True)
    
    def validate(self, attrs):
        if attrs['new_password'] != attrs['new_password_confirm']:
            raise serializers.ValidationError('密码不匹配')
        return attrs
