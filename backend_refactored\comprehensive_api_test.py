#!/usr/bin/env python
"""
全面的API测试脚本 - 测试所有API端点
"""
import requests
import json
import time
import sys

BASE_URL = "http://localhost:8000"

class APITester:
    def __init__(self):
        self.session = requests.Session()
        self.access_token = None
        self.test_results = []
    
    def log_test(self, test_name, success, message="", response_data=None):
        """记录测试结果"""
        status = "✅ 通过" if success else "❌ 失败"
        print(f"{status} {test_name}: {message}")
        
        self.test_results.append({
            'test': test_name,
            'success': success,
            'message': message,
            'response_data': response_data
        })
    
    def test_health_check(self):
        """测试健康检查"""
        print("\n🔍 测试健康检查...")
        try:
            response = self.session.get(f"{BASE_URL}/health/")
            if response.status_code == 200:
                data = response.json()
                self.log_test("健康检查", True, f"系统状态: {data.get('status', 'unknown')}")
                return True
            else:
                self.log_test("健康检查", False, f"状态码: {response.status_code}")
                return False
        except Exception as e:
            self.log_test("健康检查", False, f"异常: {e}")
            return False
    
    def test_api_root(self):
        """测试API根路径"""
        print("\n🏠 测试API根路径...")
        try:
            response = self.session.get(f"{BASE_URL}/api/")
            if response.status_code == 200:
                data = response.json()
                self.log_test("API根路径", True, f"版本: {data.get('version', 'unknown')}")
                return True
            else:
                self.log_test("API根路径", False, f"状态码: {response.status_code}")
                return False
        except Exception as e:
            self.log_test("API根路径", False, f"异常: {e}")
            return False
    
    def test_api_docs(self):
        """测试API文档"""
        print("\n📚 测试API文档...")
        try:
            response = self.session.get(f"{BASE_URL}/api/docs/")
            if response.status_code == 200:
                self.log_test("API文档", True, "Swagger文档可访问")
                return True
            else:
                self.log_test("API文档", False, f"状态码: {response.status_code}")
                return False
        except Exception as e:
            self.log_test("API文档", False, f"异常: {e}")
            return False
    
    def test_admin_interface(self):
        """测试管理界面"""
        print("\n🔧 测试管理界面...")
        try:
            response = self.session.get(f"{BASE_URL}/admin/")
            if response.status_code in [200, 302]:  # 200或重定向到登录页
                self.log_test("管理界面", True, "Django admin可访问")
                return True
            else:
                self.log_test("管理界面", False, f"状态码: {response.status_code}")
                return False
        except Exception as e:
            self.log_test("管理界面", False, f"异常: {e}")
            return False
    
    def test_compatibility_apis(self):
        """测试兼容性API"""
        print("\n🔄 测试兼容性API...")
        
        # 测试所有兼容性接口
        compatibility_apis = [
            "inference_sft",
            "inference_zero_shot", 
            "inference_cross_lingual",
            "inference_instruct",
            "inference_instruct2"
        ]
        
        success_count = 0
        for api in compatibility_apis:
            try:
                # 使用POST请求测试
                response = self.session.post(
                    f"{BASE_URL}/{api}",
                    json={"tts_text": "测试文本", "speaker": "default"},
                    headers={"Content-Type": "application/json"}
                )
                
                # 400错误是正常的，说明接口存在但参数不正确
                if response.status_code in [200, 400, 422]:
                    self.log_test(f"兼容性API - {api}", True, f"接口可访问 (状态码: {response.status_code})")
                    success_count += 1
                else:
                    self.log_test(f"兼容性API - {api}", False, f"状态码: {response.status_code}")
                    
            except Exception as e:
                self.log_test(f"兼容性API - {api}", False, f"异常: {e}")
        
        return success_count == len(compatibility_apis)
    
    def test_new_api_endpoints(self):
        """测试新API端点（需要认证）"""
        print("\n🆕 测试新API端点...")
        
        # 测试需要认证的端点
        auth_required_endpoints = [
            "/api/tasks/",
            "/api/users/",
            "/api/files/",
            "/api/auth/login/",
            "/api/auth/register/"
        ]
        
        success_count = 0
        for endpoint in auth_required_endpoints:
            try:
                response = self.session.get(f"{BASE_URL}{endpoint}")
                
                # 401未授权或405方法不允许都是正常的
                if response.status_code in [401, 405]:
                    self.log_test(f"新API - {endpoint}", True, f"端点存在 (状态码: {response.status_code})")
                    success_count += 1
                elif response.status_code == 200:
                    self.log_test(f"新API - {endpoint}", True, f"端点可访问 (状态码: {response.status_code})")
                    success_count += 1
                else:
                    self.log_test(f"新API - {endpoint}", False, f"状态码: {response.status_code}")
                    
            except Exception as e:
                self.log_test(f"新API - {endpoint}", False, f"异常: {e}")
        
        return success_count >= len(auth_required_endpoints) * 0.8  # 80%通过率
    
    def test_user_registration(self):
        """测试用户注册"""
        print("\n👤 测试用户注册...")
        try:
            user_data = {
                "username": f"testuser_{int(time.time())}",
                "email": f"test_{int(time.time())}@example.com",
                "password": "testpass123",
                "password_confirm": "testpass123"
            }
            
            response = self.session.post(
                f"{BASE_URL}/api/auth/register/",
                json=user_data,
                headers={"Content-Type": "application/json"}
            )
            
            if response.status_code == 201:
                self.log_test("用户注册", True, "用户注册成功")
                return True
            elif response.status_code == 400:
                # 可能是验证错误，但接口正常工作
                self.log_test("用户注册", True, f"接口正常工作 (验证错误: {response.status_code})")
                return True
            else:
                self.log_test("用户注册", False, f"状态码: {response.status_code}, 响应: {response.text[:100]}")
                return False
                
        except Exception as e:
            self.log_test("用户注册", False, f"异常: {e}")
            return False
    
    def test_tts_inference(self):
        """测试TTS推理（兼容性接口）"""
        print("\n🎵 测试TTS推理...")
        try:
            # 测试inference_sft接口
            tts_data = {
                "tts_text": "你好，这是一个测试语音合成。",
                "speaker": "default"
            }
            
            response = self.session.post(
                f"{BASE_URL}/inference_sft",
                json=tts_data,
                headers={"Content-Type": "application/json"}
            )
            
            if response.status_code == 200:
                self.log_test("TTS推理", True, "语音合成成功")
                return True
            elif response.status_code in [400, 422]:
                # 参数错误但接口正常
                self.log_test("TTS推理", True, f"接口正常工作 (参数错误: {response.status_code})")
                return True
            else:
                self.log_test("TTS推理", False, f"状态码: {response.status_code}, 响应: {response.text[:100]}")
                return False
                
        except Exception as e:
            self.log_test("TTS推理", False, f"异常: {e}")
            return False
    
    def test_static_files(self):
        """测试静态文件服务"""
        print("\n📁 测试静态文件服务...")
        try:
            response = self.session.get(f"{BASE_URL}/static/")
            # 404是正常的，说明静态文件路由存在
            if response.status_code in [200, 404]:
                self.log_test("静态文件服务", True, "静态文件路由正常")
                return True
            else:
                self.log_test("静态文件服务", False, f"状态码: {response.status_code}")
                return False
        except Exception as e:
            self.log_test("静态文件服务", False, f"异常: {e}")
            return False
    
    def run_all_tests(self):
        """运行所有测试"""
        print("🚀 开始全面API测试...")
        print("=" * 60)
        
        # 等待服务器完全启动
        print("⏳ 等待服务器启动...")
        time.sleep(3)
        
        # 执行所有测试
        tests = [
            ("健康检查", self.test_health_check),
            ("API根路径", self.test_api_root),
            ("API文档", self.test_api_docs),
            ("管理界面", self.test_admin_interface),
            ("兼容性API", self.test_compatibility_apis),
            ("新API端点", self.test_new_api_endpoints),
            ("用户注册", self.test_user_registration),
            ("TTS推理", self.test_tts_inference),
            ("静态文件服务", self.test_static_files),
        ]

        # 运行测试
        for test_name, test_func in tests:
            try:
                test_func()
            except Exception as e:
                self.log_test(test_name, False, f"测试异常: {e}")
        
        # 输出测试结果
        print("\n" + "=" * 60)
        print("📊 测试结果汇总:")
        print("=" * 60)
        
        passed = sum(1 for result in self.test_results if result['success'])
        total = len(self.test_results)
        
        for result in self.test_results:
            status = "✅" if result['success'] else "❌"
            print(f"{status} {result['test']}: {result['message']}")
        
        print(f"\n总计: {passed}/{total} 个测试通过")
        if total > 0:
            print(f"通过率: {(passed/total)*100:.1f}%")
        else:
            print("通过率: 0.0%")
        
        if total > 0 and passed >= total * 0.8:  # 80%通过率
            print("🎉 系统运行良好！大部分功能正常工作！")
            return True
        elif total > 0:
            print("⚠️ 系统存在一些问题，需要进一步检查")
            return False
        else:
            print("❌ 没有执行任何测试")
            return False

def main():
    """主函数"""
    tester = APITester()
    success = tester.run_all_tests()
    
    print("\n" + "=" * 60)
    print("🔗 可用的服务地址:")
    print("=" * 60)
    print(f"🏠 API根路径: {BASE_URL}/api/")
    print(f"📚 API文档: {BASE_URL}/api/docs/")
    print(f"🔧 管理界面: {BASE_URL}/admin/")
    print(f"🔍 健康检查: {BASE_URL}/health/")
    print(f"🎵 TTS接口: {BASE_URL}/inference_sft")
    
    return success

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
