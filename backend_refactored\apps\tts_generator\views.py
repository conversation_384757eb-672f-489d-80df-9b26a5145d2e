"""
TTS生成器视图
"""
import json
import time
import requests
from django.shortcuts import render, redirect
from django.http import JsonResponse, HttpResponse
from django.contrib.admin.views.decorators import staff_member_required
from django.contrib import messages
from django.conf import settings
from django.views.decorators.csrf import csrf_exempt
from django.views.decorators.http import require_http_methods
from django.core.files.base import ContentFile
from django.utils import timezone
from .models import Speaker, TTSTemplate, QuickGeneration, APITestRecord
from apps.common.logger_config import logger


@staff_member_required
def tts_generator_view(request):
    """TTS生成器主页面"""
    try:
        # 获取说话人列表
        speakers = Speaker.objects.filter(is_active=True).order_by('name')
        
        # 获取模板列表
        templates = TTSTemplate.objects.filter(is_active=True).order_by('name')
        
        # 获取最近的生成记录
        recent_generations = QuickGeneration.objects.filter(
            user=request.user
        ).order_by('-created_at')[:10]
        
        context = {
            'title': 'TTS生成器',
            'speakers': speakers,
            'templates': templates,
            'recent_generations': recent_generations,
        }
        
        return render(request, 'admin/tts_generator/generator.html', context)
        
    except Exception as e:
        logger.error(f"TTS生成器页面加载失败: {e}")
        messages.error(request, f"页面加载失败: {e}")
        return redirect('/admin/')


@staff_member_required
def api_tester_view(request):
    """API测试器页面"""
    try:
        # 获取最近的测试记录
        recent_tests = APITestRecord.objects.filter(
            user=request.user
        ).order_by('-created_at')[:20]
        
        # API端点列表
        api_endpoints = [
            {
                'name': 'SFT语音合成',
                'endpoint': '/inference_sft',
                'method': 'POST',
                'description': '使用预训练说话人进行语音合成',
                'params': [
                    {'name': 'tts_text', 'type': 'text', 'required': True, 'description': '要合成的文本'},
                    {'name': 'spk_id', 'type': 'select', 'required': True, 'description': '说话人ID'},
                ]
            },
            {
                'name': '零样本语音克隆',
                'endpoint': '/inference_zero_shot',
                'method': 'POST',
                'description': '使用参考音频进行语音克隆',
                'params': [
                    {'name': 'tts_text', 'type': 'text', 'required': True, 'description': '要合成的文本'},
                    {'name': 'prompt_text', 'type': 'text', 'required': True, 'description': '参考音频对应的文本'},
                    {'name': 'prompt_wav', 'type': 'file', 'required': True, 'description': '参考音频文件'},
                ]
            },
            {
                'name': '跨语种复刻',
                'endpoint': '/inference_cross_lingual',
                'method': 'POST',
                'description': '跨语种语音复刻',
                'params': [
                    {'name': 'tts_text', 'type': 'text', 'required': True, 'description': '要合成的文本'},
                    {'name': 'prompt_wav', 'type': 'file', 'required': True, 'description': '参考音频文件'},
                ]
            },
            {
                'name': '指令控制',
                'endpoint': '/inference_instruct',
                'method': 'POST',
                'description': '通过自然语言指令控制语音合成',
                'params': [
                    {'name': 'tts_text', 'type': 'text', 'required': True, 'description': '要合成的文本'},
                    {'name': 'spk_id', 'type': 'select', 'required': True, 'description': '说话人ID'},
                    {'name': 'instruct_text', 'type': 'text', 'required': True, 'description': '控制指令'},
                ]
            },
            {
                'name': '获取说话人列表',
                'endpoint': '/list_speakers',
                'method': 'GET',
                'description': '获取可用的说话人列表',
                'params': []
            },
            {
                'name': '系统健康检查',
                'endpoint': '/health/',
                'method': 'GET',
                'description': '检查系统运行状态',
                'params': []
            },
        ]
        
        context = {
            'title': 'API测试器',
            'api_endpoints': api_endpoints,
            'recent_tests': recent_tests,
        }
        
        return render(request, 'admin/tts_generator/api_tester.html', context)
        
    except Exception as e:
        logger.error(f"API测试器页面加载失败: {e}")
        messages.error(request, f"页面加载失败: {e}")
        return redirect('/admin/')


@csrf_exempt
@staff_member_required
@require_http_methods(["POST"])
def test_api(request):
    """测试API接口"""
    try:
        import requests
        import json
        import time

        endpoint = request.POST.get('endpoint', '')
        method = request.POST.get('method', 'GET')
        test_data = request.POST.get('test_data', '{}')

        if not endpoint:
            return JsonResponse({'error': '缺少API端点'}, status=400)

        # 构建完整URL
        base_url = 'http://localhost:8000'
        full_url = f"{base_url}{endpoint}"

        # 解析测试数据
        try:
            data = json.loads(test_data) if test_data.strip() else {}
        except json.JSONDecodeError:
            return JsonResponse({'error': '测试数据格式错误，请使用有效的JSON格式'}, status=400)

        # 记录开始时间
        start_time = time.time()

        # 发送请求
        try:
            if method.upper() == 'GET':
                response = requests.get(full_url, params=data, timeout=30)
            else:
                response = requests.post(full_url, data=data, timeout=30)

            # 计算响应时间
            response_time = (time.time() - start_time) * 1000  # 转换为毫秒

            # 解析响应
            try:
                response_data = response.json()
            except:
                response_data = response.text

            return JsonResponse({
                'success': response.status_code < 400,
                'status_code': response.status_code,
                'response_time': f"{response_time:.2f}ms",
                'response_data': response_data
            })

        except requests.exceptions.Timeout:
            return JsonResponse({
                'success': False,
                'error': '请求超时（30秒）'
            })
        except requests.exceptions.ConnectionError:
            return JsonResponse({
                'success': False,
                'error': '连接失败，请检查服务是否运行'
            })
        except Exception as e:
            return JsonResponse({
                'success': False,
                'error': f'请求失败: {str(e)}'
            })

    except Exception as e:
        return JsonResponse({
            'success': False,
            'error': f'测试失败: {str(e)}'
        }, status=500)


@csrf_exempt
@staff_member_required
@require_http_methods(["POST"])
def quick_generate_tts(request):
    """快速生成TTS"""
    try:
        # 获取参数
        generation_type = request.POST.get('generation_type', 'sft')
        text_content = request.POST.get('text_content', '')
        speaker_id = request.POST.get('speaker_id', '')
        instruct_text = request.POST.get('instruct_text', '')
        reference_text = request.POST.get('reference_text', '')
        reference_audio = request.FILES.get('reference_audio')
        
        if not text_content:
            return JsonResponse({'error': '文本内容不能为空'}, status=400)
        
        # 获取说话人
        speaker = None
        if speaker_id:
            try:
                speaker = Speaker.objects.get(speaker_id=speaker_id, is_active=True)
            except Speaker.DoesNotExist:
                return JsonResponse({'error': f'说话人 {speaker_id} 不存在'}, status=400)
        
        # 创建生成记录
        generation = QuickGeneration.objects.create(
            user=request.user,
            generation_type=generation_type,
            text_content=text_content,
            speaker=speaker,
            instruct_text=instruct_text,
            reference_text=reference_text,
            reference_audio=reference_audio,
            status='pending'
        )
        
        # 准备API请求 - 使用直接推理接口
        if generation_type == 'sft':
            api_url = "http://localhost:8000/direct/inference_sft"
        elif generation_type == 'zero_shot':
            api_url = "http://localhost:8000/direct/inference_zero_shot"
        elif generation_type == 'cross_lingual':
            api_url = "http://localhost:8000/direct/inference_cross_lingual"
        elif generation_type == 'instruct':
            api_url = "http://localhost:8000/direct/inference_instruct"
        else:
            return JsonResponse({'error': f'不支持的生成类型: {generation_type}'}, status=400)
        
        # 更新状态为处理中
        generation.update_status('processing')
        
        try:
            # 发送API请求
            start_time = time.time()
            
            if generation_type == 'sft':
                data = {
                    'tts_text': text_content,
                    'spk_id': speaker.speaker_id if speaker else '中文女'
                }
                response = requests.post(api_url, data=data, timeout=60)
                
            elif generation_type == 'zero_shot':
                if not reference_audio or not reference_text:
                    raise ValueError('零样本克隆需要参考音频和参考文本')
                
                data = {
                    'tts_text': text_content,
                    'prompt_text': reference_text
                }
                files = {'prompt_wav': reference_audio}
                response = requests.post(api_url, data=data, files=files, timeout=120)
                
            elif generation_type == 'cross_lingual':
                if not reference_audio:
                    raise ValueError('跨语种复刻需要参考音频')
                
                data = {'tts_text': text_content}
                files = {'prompt_wav': reference_audio}
                response = requests.post(api_url, data=data, files=files, timeout=120)
                
            elif generation_type == 'instruct':
                if not instruct_text:
                    raise ValueError('指令控制需要指令文本')
                
                data = {
                    'tts_text': text_content,
                    'spk_id': speaker.speaker_id if speaker else '中文女',
                    'instruct_text': instruct_text
                }
                response = requests.post(api_url, data=data, timeout=90)
            
            processing_time = time.time() - start_time
            
            if response.status_code == 200:
                # 解析API响应
                try:
                    api_result = response.json()
                    if api_result.get('success') and 'audio_info' in api_result:
                        audio_info = api_result['audio_info']

                        # 从API响应中获取音频文件路径
                        audio_filename = audio_info.get('filename')
                        audio_url = audio_info.get('download_url') or audio_info.get('url')

                        if audio_filename and audio_url:
                            # 设置result_audio字段为相对路径
                            generation.result_audio = f"generated_audio/{audio_filename}"

                            # 更新状态为完成
                            generation.update_status('completed', processing_time=processing_time)

                            return JsonResponse({
                                'success': True,
                                'message': '生成成功',
                                'generation_id': str(generation.id),
                                'processing_time': f"{processing_time:.2f}秒",
                                'audio_url': audio_url,
                                'audio_info': audio_info
                            })
                        else:
                            raise ValueError("API响应中缺少音频文件信息")
                    else:
                        raise ValueError(f"API返回错误: {api_result.get('error', '未知错误')}")

                except (json.JSONDecodeError, ValueError) as e:
                    # 如果不是JSON响应，可能是旧版本的二进制响应
                    logger.warning(f"API响应解析失败，尝试二进制处理: {e}")

                    # 保存生成的音频（兼容旧版本）
                    audio_content = response.content
                    audio_filename = f"generated_{generation.id}.wav"
                    generation.result_audio.save(
                        audio_filename,
                        ContentFile(audio_content),
                        save=False
                    )

                    # 更新状态为完成
                    generation.update_status('completed', processing_time=processing_time)

                    return JsonResponse({
                        'success': True,
                        'message': '生成成功',
                        'generation_id': str(generation.id),
                        'processing_time': f"{processing_time:.2f}秒",
                        'audio_url': generation.result_audio.url if generation.result_audio else None
                    })
            else:
                # 生成失败
                error_msg = f"API请求失败: {response.status_code} - {response.text}"
                generation.update_status('failed', error_message=error_msg)
                
                return JsonResponse({
                    'error': error_msg
                }, status=500)
                
        except Exception as api_error:
            # API调用异常
            error_msg = f"API调用异常: {str(api_error)}"
            generation.update_status('failed', error_message=error_msg)
            
            return JsonResponse({
                'error': error_msg
            }, status=500)
        
    except Exception as e:
        logger.error(f"快速生成TTS失败: {e}")
        return JsonResponse({'error': str(e)}, status=500)


@csrf_exempt
@staff_member_required
@require_http_methods(["POST"])
def test_api_endpoint(request):
    """测试API端点"""
    try:
        # 获取参数
        endpoint = request.POST.get('endpoint', '')
        method = request.POST.get('method', 'GET')
        test_data = request.POST.get('test_data', '{}')
        
        if not endpoint:
            return JsonResponse({'error': 'API端点不能为空'}, status=400)
        
        # 解析测试数据
        try:
            data = json.loads(test_data) if test_data else {}
        except json.JSONDecodeError:
            return JsonResponse({'error': '测试数据格式错误'}, status=400)
        
        # 构建完整URL
        base_url = "http://localhost:8000"
        full_url = f"{base_url}{endpoint}"
        
        # 记录测试开始时间
        start_time = time.time()
        
        try:
            # 发送请求
            if method.upper() == 'GET':
                response = requests.get(full_url, params=data, timeout=30)
            elif method.upper() == 'POST':
                response = requests.post(full_url, data=data, timeout=60)
            else:
                return JsonResponse({'error': f'不支持的请求方法: {method}'}, status=400)
            
            # 计算响应时间
            response_time = (time.time() - start_time) * 1000  # 转换为毫秒
            
            # 解析响应数据
            try:
                response_data = response.json()
            except:
                response_data = {'raw_response': response.text}
            
            # 判断是否成功
            is_success = 200 <= response.status_code < 300
            
            # 保存测试记录
            test_record = APITestRecord.objects.create(
                user=request.user,
                api_endpoint=endpoint,
                method=method.upper(),
                request_data=data,
                response_status=response.status_code,
                response_data=response_data,
                response_time=response_time,
                is_success=is_success,
                error_message='' if is_success else response.text
            )
            
            return JsonResponse({
                'success': is_success,
                'status_code': response.status_code,
                'response_data': response_data,
                'response_time': f"{response_time:.2f}ms",
                'test_id': str(test_record.id)
            })
            
        except requests.exceptions.RequestException as req_error:
            # 请求异常
            error_msg = f"请求异常: {str(req_error)}"
            
            # 保存失败记录
            APITestRecord.objects.create(
                user=request.user,
                api_endpoint=endpoint,
                method=method.upper(),
                request_data=data,
                response_status=None,
                response_data={},
                response_time=None,
                is_success=False,
                error_message=error_msg
            )
            
            return JsonResponse({
                'error': error_msg
            }, status=500)
        
    except Exception as e:
        logger.error(f"API测试失败: {e}")
        return JsonResponse({'error': str(e)}, status=500)


@staff_member_required
def get_speakers_api(request):
    """获取说话人列表API"""
    try:
        speakers = Speaker.objects.filter(is_active=True).values(
            'speaker_id', 'name', 'language', 'gender'
        )
        return JsonResponse({
            'success': True,
            'speakers': list(speakers)
        })
    except Exception as e:
        return JsonResponse({'error': str(e)}, status=500)


@staff_member_required
def get_generation_status(request, generation_id):
    """获取生成状态"""
    try:
        generation = QuickGeneration.objects.get(id=generation_id, user=request.user)
        
        return JsonResponse({
            'success': True,
            'status': generation.status,
            'processing_time': generation.duration_display,
            'audio_url': generation.result_audio.url if generation.result_audio else None,
            'error_message': generation.error_message
        })
        
    except QuickGeneration.DoesNotExist:
        return JsonResponse({'error': '生成记录不存在'}, status=404)
    except Exception as e:
        return JsonResponse({'error': str(e)}, status=500)
