"""
认证相关模型
"""
import secrets
import hashlib
from django.db import models
from django.contrib.auth import get_user_model
from apps.common.models import BaseModel

User = get_user_model()


class APIKey(BaseModel):
    """API密钥模型"""
    user = models.ForeignKey(User, on_delete=models.CASCADE, related_name='api_keys')
    name = models.CharField('密钥名称', max_length=100)
    key_hash = models.CharField('密钥哈希', max_length=255, unique=True)
    key_prefix = models.CharField('密钥前缀', max_length=10)  # 用于显示
    permissions = models.JSONField('权限列表', default=list, blank=True)
    expires_at = models.DateTimeField('过期时间', blank=True, null=True)
    last_used = models.DateTimeField('最后使用时间', blank=True, null=True)
    usage_count = models.IntegerField('使用次数', default=0)
    
    class Meta:
        db_table = 'api_keys'
        verbose_name = 'API密钥'
        verbose_name_plural = 'API密钥'
        indexes = [
            models.Index(fields=['user']),
            models.Index(fields=['key_hash']),
            models.Index(fields=['is_active']),
            models.Index(fields=['expires_at']),
        ]

    def __str__(self):
        return f"{self.user.username} - {self.name}"

    @classmethod
    def generate_key(cls):
        """生成API密钥"""
        key = secrets.token_urlsafe(32)
        return key

    @classmethod
    def hash_key(cls, key):
        """哈希API密钥"""
        return hashlib.sha256(key.encode()).hexdigest()

    def save(self, *args, **kwargs):
        if not self.key_hash:
            # 生成新密钥
            key = self.generate_key()
            self.key_hash = self.hash_key(key)
            self.key_prefix = key[:8]
            # 返回原始密钥供显示（仅在创建时）
            self._raw_key = key
        super().save(*args, **kwargs)

    def verify_key(self, key):
        """验证API密钥"""
        return self.key_hash == self.hash_key(key)

    @property
    def is_expired(self):
        """检查是否过期"""
        if not self.expires_at:
            return False
        from django.utils import timezone
        return timezone.now() > self.expires_at

    def record_usage(self):
        """记录使用"""
        from django.utils import timezone
        self.last_used = timezone.now()
        self.usage_count += 1
        self.save(update_fields=['last_used', 'usage_count'])


class LoginLog(BaseModel):
    """登录日志"""
    user = models.ForeignKey(User, on_delete=models.CASCADE, related_name='login_logs')
    ip_address = models.GenericIPAddressField('IP地址')
    user_agent = models.TextField('用户代理', blank=True)
    login_type = models.CharField('登录类型', max_length=20, choices=[
        ('password', '密码登录'),
        ('api_key', 'API密钥'),
        ('token', 'Token刷新'),
    ])
    success = models.BooleanField('是否成功', default=True)
    failure_reason = models.CharField('失败原因', max_length=200, blank=True)
    
    class Meta:
        db_table = 'login_logs'
        verbose_name = '登录日志'
        verbose_name_plural = '登录日志'
        indexes = [
            models.Index(fields=['user', 'created_at']),
            models.Index(fields=['ip_address']),
            models.Index(fields=['success']),
        ]

    def __str__(self):
        status = '成功' if self.success else '失败'
        return f"{self.user.username} - {self.login_type} - {status}"


class PasswordResetToken(BaseModel):
    """密码重置令牌"""
    user = models.ForeignKey(User, on_delete=models.CASCADE, related_name='reset_tokens')
    token = models.CharField('令牌', max_length=255, unique=True)
    expires_at = models.DateTimeField('过期时间')
    used = models.BooleanField('是否已使用', default=False)

    class Meta:
        db_table = 'password_reset_tokens'
        verbose_name = '密码重置令牌'
        verbose_name_plural = '密码重置令牌'
        indexes = [
            models.Index(fields=['token']),
            models.Index(fields=['user', 'used']),
            models.Index(fields=['expires_at']),
        ]

    def __str__(self):
        return f"{self.user.username} - 密码重置令牌"

    @classmethod
    def generate_token(cls):
        """生成重置令牌"""
        import secrets
        return secrets.token_urlsafe(32)

    @property
    def is_expired(self):
        """检查是否过期"""
        from django.utils import timezone
        return timezone.now() > self.expires_at

    @property
    def is_valid(self):
        """检查是否有效"""
        return not self.used and not self.is_expired
