#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
CosyVoice推理引擎
直接集成CosyVoice模型到Django系统中
"""

import os
import sys
import numpy as np
import torchaudio
import torch
import io
import wave
import tempfile
from typing import Optional, Dict, Any, List
from django.conf import settings
from pathlib import Path

# 导入loguru日志系统
try:
    from .logger_config import cosyvoice_logger as logger, performance_logger
    from .logger_utils import monitor_performance, log_model_inference
except ImportError:
    # 如果loguru还没初始化，使用临时logger
    import logging
    logger = logging.getLogger(__name__)
    performance_logger = logger
    def monitor_performance(threshold_ms=1000):
        def decorator(func):
            return func
        return decorator
    def log_model_inference(*args, **kwargs):
        pass

# 添加CosyVoice路径
COSYVOICE_ROOT = Path(__file__).parent.parent.parent.parent  # 回到CosyVoice根目录
sys.path.insert(0, str(COSYVOICE_ROOT))  # 添加CosyVoice根目录
sys.path.insert(0, str(COSYVOICE_ROOT / 'third_party/Matcha-TTS'))  # 添加Matcha-TTS路径

try:
    from cosyvoice.cli.cosyvoice import CosyVoice, CosyVoice2
    from cosyvoice.utils.file_utils import load_wav
    COSYVOICE_AVAILABLE = True
except ImportError as e:
    logger.warning(f"CosyVoice模块导入失败: {e}")
    COSYVOICE_AVAILABLE = False


class CosyVoiceEngine:
    """CosyVoice推理引擎"""
    
    def __init__(self):
        self.model = None
        # 使用绝对路径避免ModelScope下载
        cosyvoice_root = Path(__file__).parent.parent.parent.parent
        self.model_dir = str(cosyvoice_root / 'pretrained_models' / 'CosyVoice-300M-SFT')
        # 使用绝对路径确保文件保存到正确位置
        # 如果是相对路径，则相对于BASE_DIR；如果是绝对路径，则直接使用
        output_dir_setting = getattr(settings, 'COSYVOICE_OUTPUT_DIR', 'media/generated_audio')
        if os.path.isabs(output_dir_setting):
            self.output_dir = output_dir_setting
        else:
            self.output_dir = str(settings.BASE_DIR / output_dir_setting)
        self.sample_rate = 22050
        self._ensure_output_dir()
    
    def _ensure_output_dir(self):
        """确保输出目录存在"""
        if not os.path.exists(self.output_dir):
            os.makedirs(self.output_dir, exist_ok=True)
    
    def is_available(self) -> bool:
        """检查CosyVoice是否可用"""
        return COSYVOICE_AVAILABLE and self.model is not None
    
    def load_model(self) -> bool:
        """加载CosyVoice模型"""
        if not COSYVOICE_AVAILABLE:
            logger.error("CosyVoice模块不可用")
            return False
        
        if self.model is not None:
            logger.info("模型已加载")
            return True
        
        try:
            logger.info(f"正在加载CosyVoice模型: {self.model_dir}")
            
            # 尝试加载CosyVoice模型
            try:
                self.model = CosyVoice(self.model_dir)
                logger.info("CosyVoice模型加载成功")
            except Exception as e:
                logger.warning(f"CosyVoice加载失败，尝试CosyVoice2: {e}")
                self.model = CosyVoice2(self.model_dir)
                logger.info("CosyVoice2模型加载成功")
            
            self.sample_rate = getattr(self.model, 'sample_rate', 22050)
            return True
            
        except Exception as e:
            logger.error(f"模型加载失败: {e}")
            self.model = None
            return False
    
    def unload_model(self):
        """卸载模型释放内存"""
        if self.model is not None:
            del self.model
            self.model = None
            # 清理GPU缓存
            if torch.cuda.is_available():
                torch.cuda.empty_cache()
            logger.info("模型已卸载")
    
    def list_speakers(self) -> List[str]:
        """获取可用说话人列表"""
        if not self.is_available():
            return ["中文女", "中文男", "英文女", "英文男"]  # 默认列表
        
        try:
            return self.model.list_available_spks()
        except Exception as e:
            logger.error(f"获取说话人列表失败: {e}")
            return ["中文女", "中文男", "英文女", "英文男"]
    
    def _generate_audio_data(self, model_output) -> bytes:
        """生成WAV格式音频数据"""
        try:
            # 收集所有音频片段
            audio_chunks = []
            for i in model_output:
                audio_chunks.append(i['tts_speech'].numpy())
            
            if not audio_chunks:
                return b''
            
            # 合并所有音频片段
            full_audio = np.concatenate(audio_chunks, axis=-1)
            # 转换为int16格式
            audio_int16 = (full_audio * (2 ** 15)).astype(np.int16)
            
            # 创建WAV文件的字节流
            wav_buffer = io.BytesIO()
            
            # 写入WAV文件头和数据
            with wave.open(wav_buffer, 'wb') as wav_file:
                wav_file.setnchannels(1)  # 单声道
                wav_file.setsampwidth(2)  # 16位
                wav_file.setframerate(self.sample_rate)  # 采样率
                wav_file.writeframes(audio_int16.tobytes())
            
            wav_buffer.seek(0)
            return wav_buffer.getvalue()
            
        except Exception as e:
            logger.error(f"生成音频数据失败: {e}")
            return b''
    
    def _save_audio_file(self, audio_data: bytes, prefix: str = "audio") -> Dict[str, Any]:
        """保存音频文件并返回文件信息"""
        try:
            from datetime import datetime
            
            # 生成唯一文件名
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S_%f")[:-3]
            filename = f"{prefix}_{timestamp}.wav"
            file_path = os.path.join(self.output_dir, filename)
            
            # 保存文件
            with open(file_path, 'wb') as f:
                f.write(audio_data)
            
            # 获取文件信息
            file_size = len(audio_data)
            duration = self._get_audio_duration(audio_data)

            # 生成正确的URL
            # 计算相对于MEDIA_ROOT的路径
            media_root = str(settings.MEDIA_ROOT)
            if file_path.startswith(media_root):
                # 文件在MEDIA_ROOT下，生成相对URL
                relative_path = os.path.relpath(file_path, media_root)
                url = f"{settings.MEDIA_URL}{relative_path.replace(os.sep, '/')}"
            else:
                # 文件不在MEDIA_ROOT下，使用文件名
                url = f"{settings.MEDIA_URL}generated_audio/{filename}"

            return {
                "filename": filename,
                "file_path": file_path,
                "file_size": file_size,
                "duration": duration,
                "sample_rate": self.sample_rate,
                "url": url
            }
            
        except Exception as e:
            logger.error(f"保存音频文件失败: {e}")
            return {}
    
    def _get_audio_duration(self, audio_data: bytes) -> float:
        """获取音频时长（秒）"""
        try:
            with io.BytesIO(audio_data) as audio_buffer:
                with wave.open(audio_buffer, 'rb') as wav_file:
                    frames = wav_file.getnframes()
                    sample_rate = wav_file.getframerate()
                    duration = frames / float(sample_rate)
                    return round(duration, 2)
        except:
            return 0.0
    
    def inference_sft(self, tts_text: str, spk_id: str = "中文女") -> Dict[str, Any]:
        """SFT模式推理"""
        if not self.is_available():
            raise RuntimeError("CosyVoice模型未加载")
        
        try:
            logger.info(f"SFT推理: text='{tts_text}', speaker='{spk_id}'")
            
            # 执行推理
            model_output = self.model.inference_sft(tts_text, spk_id)
            
            # 生成音频数据
            audio_data = self._generate_audio_data(model_output)
            if not audio_data:
                raise RuntimeError("音频生成失败")
            
            # 保存音频文件
            audio_info = self._save_audio_file(audio_data, "sft")
            
            return {
                "success": True,
                "message": "SFT推理成功",
                "audio_info": audio_info,
                "parameters": {
                    "tts_text": tts_text,
                    "spk_id": spk_id,
                    "model_type": "sft"
                }
            }
            
        except Exception as e:
            logger.error(f"SFT推理失败: {e}")
            raise RuntimeError(f"SFT推理失败: {e}")
    
    def inference_zero_shot(self, tts_text: str, prompt_text: str, prompt_wav_path: str) -> Dict[str, Any]:
        """零样本语音克隆"""
        if not self.is_available():
            raise RuntimeError("CosyVoice模型未加载")
        
        try:
            logger.info(f"零样本推理: text='{tts_text}', prompt='{prompt_text}'")
            
            # 加载参考音频
            prompt_speech_16k = load_wav(prompt_wav_path, 16000)
            
            # 执行推理
            model_output = self.model.inference_zero_shot(tts_text, prompt_text, prompt_speech_16k)
            
            # 生成音频数据
            audio_data = self._generate_audio_data(model_output)
            if not audio_data:
                raise RuntimeError("音频生成失败")
            
            # 保存音频文件
            audio_info = self._save_audio_file(audio_data, "zero_shot")
            
            return {
                "success": True,
                "message": "零样本推理成功",
                "audio_info": audio_info,
                "parameters": {
                    "tts_text": tts_text,
                    "prompt_text": prompt_text,
                    "model_type": "zero_shot"
                }
            }
            
        except Exception as e:
            logger.error(f"零样本推理失败: {e}")
            raise RuntimeError(f"零样本推理失败: {e}")
    
    def inference_cross_lingual(self, tts_text: str, prompt_wav_path: str) -> Dict[str, Any]:
        """跨语言语音合成"""
        if not self.is_available():
            raise RuntimeError("CosyVoice模型未加载")
        
        try:
            logger.info(f"跨语言推理: text='{tts_text}'")
            
            # 加载参考音频
            prompt_speech_16k = load_wav(prompt_wav_path, 16000)
            
            # 执行推理
            model_output = self.model.inference_cross_lingual(tts_text, prompt_speech_16k)
            
            # 生成音频数据
            audio_data = self._generate_audio_data(model_output)
            if not audio_data:
                raise RuntimeError("音频生成失败")
            
            # 保存音频文件
            audio_info = self._save_audio_file(audio_data, "cross_lingual")
            
            return {
                "success": True,
                "message": "跨语言推理成功",
                "audio_info": audio_info,
                "parameters": {
                    "tts_text": tts_text,
                    "model_type": "cross_lingual"
                }
            }
            
        except Exception as e:
            logger.error(f"跨语言推理失败: {e}")
            raise RuntimeError(f"跨语言推理失败: {e}")
    
    def inference_instruct(self, tts_text: str, spk_id: str, instruct_text: str) -> Dict[str, Any]:
        """指令式语音合成"""
        if not self.is_available():
            raise RuntimeError("CosyVoice模型未加载")
        
        try:
            logger.info(f"指令式推理: text='{tts_text}', speaker='{spk_id}', instruct='{instruct_text}'")
            
            # 执行推理
            model_output = self.model.inference_instruct(tts_text, spk_id, instruct_text)
            
            # 生成音频数据
            audio_data = self._generate_audio_data(model_output)
            if not audio_data:
                raise RuntimeError("音频生成失败")
            
            # 保存音频文件
            audio_info = self._save_audio_file(audio_data, "instruct")
            
            return {
                "success": True,
                "message": "指令式推理成功",
                "audio_info": audio_info,
                "parameters": {
                    "tts_text": tts_text,
                    "spk_id": spk_id,
                    "instruct_text": instruct_text,
                    "model_type": "instruct"
                }
            }
            
        except Exception as e:
            logger.error(f"指令式推理失败: {e}")
            raise RuntimeError(f"指令式推理失败: {e}")

    def inference_instruct2(self, tts_text: str, instruct_text: str, prompt_wav_path: str) -> Dict[str, Any]:
        """高级指令式语音合成（基于参考音频）"""
        if not self.is_available():
            raise RuntimeError("CosyVoice模型未加载")

        try:
            logger.info(f"高级指令式推理: text='{tts_text}', instruct='{instruct_text}'")

            # 检查模型是否支持inference_instruct2方法
            if not hasattr(self.model, 'inference_instruct2'):
                # 如果不支持instruct2，使用instruct方法作为fallback
                logger.warning("模型不支持inference_instruct2，使用inference_instruct作为替代")
                # 加载参考音频
                prompt_speech_16k = load_wav(prompt_wav_path, 16000)
                # 使用零样本推理作为替代
                model_output = self.model.inference_zero_shot(tts_text, instruct_text, prompt_speech_16k)
            else:
                # 加载参考音频
                prompt_speech_16k = load_wav(prompt_wav_path, 16000)
                # 执行推理
                model_output = self.model.inference_instruct2(tts_text, instruct_text, prompt_speech_16k)

            # 生成音频数据
            audio_data = self._generate_audio_data(model_output)
            if not audio_data:
                raise RuntimeError("音频生成失败")

            # 保存音频文件
            audio_info = self._save_audio_file(audio_data, "instruct2")

            return {
                "success": True,
                "message": "高级指令式推理成功",
                "audio_info": audio_info,
                "parameters": {
                    "tts_text": tts_text,
                    "instruct_text": instruct_text,
                    "model_type": "instruct2"
                }
            }

        except Exception as e:
            logger.error(f"高级指令式推理失败: {e}")
            raise RuntimeError(f"高级指令式推理失败: {e}")


# 全局引擎实例
_cosyvoice_engine = None


def get_cosyvoice_engine() -> CosyVoiceEngine:
    """获取CosyVoice引擎实例（单例模式）"""
    global _cosyvoice_engine
    if _cosyvoice_engine is None:
        _cosyvoice_engine = CosyVoiceEngine()
    return _cosyvoice_engine


def initialize_cosyvoice_engine() -> bool:
    """初始化CosyVoice引擎"""
    try:
        engine = get_cosyvoice_engine()
        return engine.load_model()
    except Exception as e:
        logger.error(f"初始化CosyVoice引擎失败: {e}")
        return False
