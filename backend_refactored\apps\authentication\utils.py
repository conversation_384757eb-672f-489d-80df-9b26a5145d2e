"""
认证相关工具函数
"""
import secrets
from django.core.mail import send_mail
from django.conf import settings
from django.template.loader import render_to_string
from loguru import logger


def get_client_ip(request):
    """获取客户端IP地址"""
    x_forwarded_for = request.META.get('HTTP_X_FORWARDED_FOR')
    if x_forwarded_for:
        ip = x_forwarded_for.split(',')[0].strip()
    else:
        ip = request.META.get('REMOTE_ADDR')
    return ip


def generate_api_key():
    """生成API密钥"""
    return secrets.token_urlsafe(32)


def send_password_reset_email(user, token):
    """发送密码重置邮件"""
    try:
        subject = 'CosyVoice - 密码重置'
        
        # 构建重置链接
        reset_url = f"{settings.FRONTEND_URL}/reset-password?token={token}"
        
        # 渲染邮件模板
        html_message = render_to_string('emails/password_reset.html', {
            'user': user,
            'reset_url': reset_url,
            'site_name': 'CosyVoice API任务管理系统',
        })
        
        plain_message = f"""
        您好 {user.username}，

        您请求重置CosyVoice账户密码。请点击以下链接重置密码：
        {reset_url}

        此链接将在1小时后过期。

        如果您没有请求重置密码，请忽略此邮件。

        CosyVoice团队
        """
        
        send_mail(
            subject=subject,
            message=plain_message,
            from_email=settings.DEFAULT_FROM_EMAIL,
            recipient_list=[user.email],
            html_message=html_message,
            fail_silently=False,
        )
        
        logger.info(f"密码重置邮件发送成功: {user.email}")
        return True
        
    except Exception as e:
        logger.error(f"密码重置邮件发送失败: {e}")
        return False


def send_welcome_email(user):
    """发送欢迎邮件"""
    try:
        subject = '欢迎使用CosyVoice API任务管理系统'
        
        html_message = render_to_string('emails/welcome.html', {
            'user': user,
            'site_name': 'CosyVoice API任务管理系统',
            'login_url': f"{settings.FRONTEND_URL}/login",
            'docs_url': f"{settings.BACKEND_URL}/docs/",
        })
        
        plain_message = f"""
        欢迎 {user.username}！

        感谢您注册CosyVoice API任务管理系统。

        您的账户信息：
        - 用户名: {user.username}
        - 邮箱: {user.email}
        - 角色: {user.get_role_display()}
        - 配额限制: {user.quota_limit}

        您可以通过以下链接登录系统：
        {settings.FRONTEND_URL}/login

        API文档地址：
        {settings.BACKEND_URL}/docs/

        如有任何问题，请联系我们的支持团队。

        CosyVoice团队
        """
        
        send_mail(
            subject=subject,
            message=plain_message,
            from_email=settings.DEFAULT_FROM_EMAIL,
            recipient_list=[user.email],
            html_message=html_message,
            fail_silently=True,  # 欢迎邮件失败不影响注册
        )
        
        logger.info(f"欢迎邮件发送成功: {user.email}")
        return True
        
    except Exception as e:
        logger.error(f"欢迎邮件发送失败: {e}")
        return False
