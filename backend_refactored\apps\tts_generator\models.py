"""
TTS生成器模型
"""
from django.db import models
from django.contrib.auth import get_user_model
from django.utils import timezone
import uuid

User = get_user_model()


class Speaker(models.Model):
    """说话人模型"""
    id = models.UUIDField(primary_key=True, default=uuid.uuid4, editable=False)
    name = models.CharField('说话人名称', max_length=100)
    speaker_id = models.CharField('说话人ID', max_length=50, unique=True)
    language = models.CharField('语言', max_length=20, default='zh')
    gender = models.CharField('性别', max_length=10, choices=[
        ('male', '男性'),
        ('female', '女性'),
        ('neutral', '中性')
    ], default='female')
    description = models.TextField('描述', blank=True)
    is_active = models.BooleanField('是否启用', default=True)
    created_at = models.DateTimeField('创建时间', auto_now_add=True)
    updated_at = models.DateTimeField('更新时间', auto_now=True)

    class Meta:
        verbose_name = '说话人'
        verbose_name_plural = '说话人管理'
        ordering = ['name']

    def __str__(self):
        return f"{self.name} ({self.speaker_id})"


class TTSTemplate(models.Model):
    """TTS模板"""
    id = models.UUIDField(primary_key=True, default=uuid.uuid4, editable=False)
    name = models.CharField('模板名称', max_length=100)
    template_type = models.CharField('模板类型', max_length=20, choices=[
        ('sft', 'SFT模式'),
        ('zero_shot', '零样本克隆'),
        ('cross_lingual', '跨语种复刻'),
        ('instruct', '指令控制')
    ])
    text_template = models.TextField('文本模板', help_text='支持变量替换，如 {name}, {content}')
    speaker = models.ForeignKey(Speaker, on_delete=models.CASCADE, verbose_name='默认说话人', null=True, blank=True)
    instruct_text = models.TextField('指令文本', blank=True, help_text='仅指令控制模式使用')
    is_active = models.BooleanField('是否启用', default=True)
    created_by = models.ForeignKey(User, on_delete=models.CASCADE, verbose_name='创建者')
    created_at = models.DateTimeField('创建时间', auto_now_add=True)
    updated_at = models.DateTimeField('更新时间', auto_now=True)

    class Meta:
        verbose_name = 'TTS模板'
        verbose_name_plural = 'TTS模板管理'
        ordering = ['-created_at']

    def __str__(self):
        return f"{self.name} ({self.get_template_type_display()})"


class QuickGeneration(models.Model):
    """快速生成记录"""
    id = models.UUIDField(primary_key=True, default=uuid.uuid4, editable=False)
    user = models.ForeignKey(User, on_delete=models.CASCADE, verbose_name='用户')
    generation_type = models.CharField('生成类型', max_length=20, choices=[
        ('sft', 'SFT模式'),
        ('zero_shot', '零样本克隆'),
        ('cross_lingual', '跨语种复刻'),
        ('instruct', '指令控制')
    ])
    text_content = models.TextField('文本内容')
    speaker = models.ForeignKey(Speaker, on_delete=models.SET_NULL, null=True, blank=True, verbose_name='说话人')
    instruct_text = models.TextField('指令文本', blank=True)
    reference_audio = models.FileField('参考音频', upload_to='reference_audio/', null=True, blank=True)
    reference_text = models.TextField('参考文本', blank=True)
    
    # 生成结果
    task_id = models.CharField('任务ID', max_length=100, blank=True)
    status = models.CharField('状态', max_length=20, choices=[
        ('pending', '等待中'),
        ('processing', '处理中'),
        ('completed', '已完成'),
        ('failed', '失败')
    ], default='pending')
    result_audio = models.FileField('生成音频', upload_to='generated_audio/', null=True, blank=True)
    error_message = models.TextField('错误信息', blank=True)
    
    # 时间记录
    created_at = models.DateTimeField('创建时间', auto_now_add=True)
    started_at = models.DateTimeField('开始时间', null=True, blank=True)
    completed_at = models.DateTimeField('完成时间', null=True, blank=True)
    
    # 性能指标
    processing_time = models.FloatField('处理时长(秒)', null=True, blank=True)
    audio_duration = models.FloatField('音频时长(秒)', null=True, blank=True)

    class Meta:
        verbose_name = '快速生成'
        verbose_name_plural = '快速生成记录'
        ordering = ['-created_at']

    def __str__(self):
        return f"{self.get_generation_type_display()} - {self.text_content[:30]}..."

    @property
    def duration_display(self):
        """显示处理时长"""
        if self.processing_time:
            return f"{self.processing_time:.2f}秒"
        return "未知"

    def update_status(self, status, **kwargs):
        """更新状态"""
        self.status = status
        if status == 'processing':
            self.started_at = timezone.now()
        elif status in ['completed', 'failed']:
            self.completed_at = timezone.now()
            if self.started_at:
                self.processing_time = (self.completed_at - self.started_at).total_seconds()
        
        for key, value in kwargs.items():
            if hasattr(self, key):
                setattr(self, key, value)
        
        self.save()


class APITestRecord(models.Model):
    """API测试记录"""
    id = models.UUIDField(primary_key=True, default=uuid.uuid4, editable=False)
    user = models.ForeignKey(User, on_delete=models.CASCADE, verbose_name='测试用户')
    api_endpoint = models.CharField('API端点', max_length=200)
    method = models.CharField('请求方法', max_length=10, choices=[
        ('GET', 'GET'),
        ('POST', 'POST'),
        ('PUT', 'PUT'),
        ('DELETE', 'DELETE')
    ])
    request_data = models.JSONField('请求数据', default=dict)
    response_status = models.IntegerField('响应状态码', null=True, blank=True)
    response_data = models.JSONField('响应数据', default=dict)
    response_time = models.FloatField('响应时间(毫秒)', null=True, blank=True)
    is_success = models.BooleanField('是否成功', default=False)
    error_message = models.TextField('错误信息', blank=True)
    created_at = models.DateTimeField('测试时间', auto_now_add=True)

    class Meta:
        verbose_name = 'API测试记录'
        verbose_name_plural = 'API测试记录'
        ordering = ['-created_at']

    def __str__(self):
        status = "成功" if self.is_success else "失败"
        return f"{self.method} {self.api_endpoint} - {status}"
