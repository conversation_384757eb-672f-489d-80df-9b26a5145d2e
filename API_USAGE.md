# CosyVoice API 使用说明

## 快速开始

### 1. 启动API服务器

```bash
# 方法1: 使用启动脚本
chmod +x start_api_server.sh
./start_api_server.sh 8000

# 方法2: 直接运行
conda activate cosyvoice
python api_server.py --port 8000 --model_dir pretrained_models/CosyVoice-300M
```

### 2. 访问API文档

- Swagger UI: http://localhost:8000/docs
- ReDoc: http://localhost:8000/redoc

### 3. 运行测试

```bash
python test_api.py
```

## API接口说明

### 基础接口

#### GET /health
健康检查接口

#### GET /list_speakers
获取可用说话人列表

#### GET /reference_audios
获取参考音频列表

### 推理接口

#### POST /inference_sft
**SFT模式推理（预训练音色）**

参数：
- `tts_text`: 要合成的文本
- `spk_id`: 说话人ID（可通过/list_speakers获取）

示例：
```bash
curl -X POST "http://localhost:8000/inference_sft" \
  -F "tts_text=你好，我是CosyVoice语音合成系统" \
  -F "spk_id=中文女" \
  --output sft_output.wav
```

#### POST /inference_zero_shot
**3秒极速复刻（零样本语音克隆）**

参数：
- `tts_text`: 要合成的文本
- `prompt_text`: 参考音频对应的文本
- `prompt_wav`: 参考音频文件（3-30秒）

示例：
```bash
curl -X POST "http://localhost:8000/inference_zero_shot" \
  -F "tts_text=收到好友从远方寄来的生日礼物，那份意外的惊喜让我心中充满了快乐" \
  -F "prompt_text=希望你以后能够做的比我还好呦" \
  -F "prompt_wav=@asset/zero_shot_prompt.wav" \
  --output zero_shot_output.wav
```

#### POST /inference_cross_lingual
**跨语种复刻**

参数：
- `tts_text`: 要合成的文本（可包含语言标记）
- `prompt_wav`: 参考音频文件

语言标记：
- `<|zh|>`: 中文
- `<|en|>`: 英文
- `<|jp|>`: 日文
- `<|yue|>`: 粤语
- `<|ko|>`: 韩语

示例：
```bash
curl -X POST "http://localhost:8000/inference_cross_lingual" \
  -F "tts_text=<|en|>Hello, this is a cross-lingual speech synthesis test." \
  -F "prompt_wav=@asset/cross_lingual_prompt.wav" \
  --output cross_lingual_output.wav
```

#### POST /inference_instruct
**自然语言控制**

参数：
- `tts_text`: 要合成的文本
- `spk_id`: 基础说话人ID
- `instruct_text`: 控制指令

示例：
```bash
curl -X POST "http://localhost:8000/inference_instruct" \
  -F "tts_text=今天天气真不错，阳光明媚" \
  -F "spk_id=中文女" \
  -F "instruct_text=用温柔甜美的语气说" \
  --output instruct_output.wav
```

## Python客户端示例

```python
import requests

# 3秒极速复刻示例
def zero_shot_tts(text, prompt_text, audio_file_path):
    url = "http://localhost:8000/inference_zero_shot"
    
    data = {
        'tts_text': text,
        'prompt_text': prompt_text
    }
    
    with open(audio_file_path, 'rb') as f:
        files = {'prompt_wav': f}
        response = requests.post(url, data=data, files=files)
    
    if response.status_code == 200:
        with open('output.wav', 'wb') as f:
            f.write(response.content)
        print("语音合成成功！")
    else:
        print(f"合成失败: {response.text}")

# 使用示例
zero_shot_tts(
    text="这是一个语音克隆测试",
    prompt_text="参考音频的文本内容", 
    audio_file_path="reference_audio.wav"
)
```

## 注意事项

1. **参考音频要求**：
   - 格式：wav/mp3/flac
   - 时长：建议3-30秒
   - 质量：清晰，无噪音

2. **文本要求**：
   - 支持中文、英文、日文等多语言
   - 避免过长文本（建议单次不超过200字）

3. **性能优化**：
   - 首次推理可能较慢（模型加载）
   - 建议使用GPU加速
   - 可调整并发数量

4. **错误处理**：
   - 检查返回状态码
   - 查看错误信息进行调试

## 部署建议

### Docker部署
```bash
# 构建镜像
docker build -t cosyvoice-api .

# 运行容器
docker run -d -p 8000:8000 \
  -v $(pwd)/pretrained_models:/app/pretrained_models \
  -v $(pwd)/asset:/app/asset \
  cosyvoice-api
```

### 生产环境
- 使用nginx反向代理
- 配置HTTPS证书
- 设置适当的并发限制
- 监控资源使用情况