"""
用户管理模型
"""
import uuid
from django.contrib.auth.models import AbstractUser
from django.db import models
from django.utils import timezone
from apps.common.models import BaseModel


class UserRole(models.TextChoices):
    """用户角色"""
    BASIC = 'basic', '基础用户'
    PREMIUM = 'premium', '高级用户'
    ADMIN = 'admin', '管理员'


class User(AbstractUser):
    """用户模型"""
    id = models.UUIDField(primary_key=True, default=uuid.uuid4, editable=False)
    email = models.EmailField('邮箱', unique=True)
    role = models.CharField('角色', max_length=20, choices=UserRole.choices, default=UserRole.BASIC)
    avatar_url = models.URLField('头像URL', max_length=500, blank=True, null=True)
    bio = models.TextField('个人简介', blank=True, null=True)
    quota_limit = models.IntegerField('配额限制', default=1000)
    quota_used = models.IntegerField('已使用配额', default=0)
    is_active = models.BooleanField('是否激活', default=True)
    created_at = models.DateTimeField('创建时间', auto_now_add=True)
    updated_at = models.DateTimeField('更新时间', auto_now=True)
    last_login = models.DateTimeField('最后登录', blank=True, null=True)
    deleted_at = models.DateTimeField('删除时间', blank=True, null=True)

    USERNAME_FIELD = 'email'
    REQUIRED_FIELDS = ['username']

    class Meta:
        db_table = 'users'
        verbose_name = '用户'
        verbose_name_plural = '用户'
        indexes = [
            models.Index(fields=['email']),
            models.Index(fields=['username']),
            models.Index(fields=['created_at']),
            models.Index(fields=['role']),
        ]

    def __str__(self):
        return f"{self.username} ({self.email})"

    def soft_delete(self):
        """软删除"""
        self.deleted_at = timezone.now()
        self.is_active = False
        self.save()

    def restore(self):
        """恢复删除"""
        self.deleted_at = None
        self.is_active = True
        self.save()

    @property
    def quota_remaining(self):
        """剩余配额"""
        return max(0, self.quota_limit - self.quota_used)

    @property
    def quota_usage_percentage(self):
        """配额使用百分比"""
        if self.quota_limit == 0:
            return 0
        return min(100, (self.quota_used / self.quota_limit) * 100)

    def can_use_quota(self, amount=1):
        """检查是否可以使用配额"""
        return self.quota_used + amount <= self.quota_limit

    def use_quota(self, amount=1):
        """使用配额"""
        if self.can_use_quota(amount):
            self.quota_used += amount
            self.save(update_fields=['quota_used'])
            return True
        return False


class UserProfile(BaseModel):
    """用户资料扩展"""
    user = models.OneToOneField(User, on_delete=models.CASCADE, related_name='profile')
    phone = models.CharField('手机号', max_length=20, blank=True, null=True)
    address = models.TextField('地址', blank=True, null=True)
    birth_date = models.DateField('生日', blank=True, null=True)
    company = models.CharField('公司', max_length=100, blank=True, null=True)
    website = models.URLField('网站', blank=True, null=True)
    
    # 偏好设置
    language = models.CharField('语言偏好', max_length=10, default='zh-hans')
    timezone = models.CharField('时区', max_length=50, default='Asia/Shanghai')
    email_notifications = models.BooleanField('邮件通知', default=True)
    
    class Meta:
        db_table = 'user_profiles'
        verbose_name = '用户资料'
        verbose_name_plural = '用户资料'

    def __str__(self):
        return f"{self.user.username}的资料"
