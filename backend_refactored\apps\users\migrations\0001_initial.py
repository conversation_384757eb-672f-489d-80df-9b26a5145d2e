# Generated by Django 4.2.7 on 2025-07-24 03:28

from django.conf import settings
import django.contrib.auth.models
import django.contrib.auth.validators
from django.db import migrations, models
import django.db.models.deletion
import django.utils.timezone
import uuid


class Migration(migrations.Migration):
    initial = True

    dependencies = [
        ("auth", "0012_alter_user_first_name_max_length"),
    ]

    operations = [
        migrations.CreateModel(
            name="User",
            fields=[
                ("password", models.CharField(max_length=128, verbose_name="password")),
                (
                    "is_superuser",
                    models.BooleanField(
                        default=False,
                        help_text="Designates that this user has all permissions without explicitly assigning them.",
                        verbose_name="superuser status",
                    ),
                ),
                (
                    "username",
                    models.CharField(
                        error_messages={
                            "unique": "A user with that username already exists."
                        },
                        help_text="Required. 150 characters or fewer. Letters, digits and @/./+/-/_ only.",
                        max_length=150,
                        unique=True,
                        validators=[
                            django.contrib.auth.validators.UnicodeUsernameValidator()
                        ],
                        verbose_name="username",
                    ),
                ),
                (
                    "first_name",
                    models.CharField(
                        blank=True, max_length=150, verbose_name="first name"
                    ),
                ),
                (
                    "last_name",
                    models.CharField(
                        blank=True, max_length=150, verbose_name="last name"
                    ),
                ),
                (
                    "is_staff",
                    models.BooleanField(
                        default=False,
                        help_text="Designates whether the user can log into this admin site.",
                        verbose_name="staff status",
                    ),
                ),
                (
                    "date_joined",
                    models.DateTimeField(
                        default=django.utils.timezone.now, verbose_name="date joined"
                    ),
                ),
                (
                    "id",
                    models.UUIDField(
                        default=uuid.uuid4,
                        editable=False,
                        primary_key=True,
                        serialize=False,
                    ),
                ),
                (
                    "email",
                    models.EmailField(max_length=254, unique=True, verbose_name="邮箱"),
                ),
                (
                    "role",
                    models.CharField(
                        choices=[
                            ("basic", "基础用户"),
                            ("premium", "高级用户"),
                            ("admin", "管理员"),
                        ],
                        default="basic",
                        max_length=20,
                        verbose_name="角色",
                    ),
                ),
                (
                    "avatar_url",
                    models.URLField(
                        blank=True, max_length=500, null=True, verbose_name="头像URL"
                    ),
                ),
                ("bio", models.TextField(blank=True, null=True, verbose_name="个人简介")),
                ("quota_limit", models.IntegerField(default=1000, verbose_name="配额限制")),
                ("quota_used", models.IntegerField(default=0, verbose_name="已使用配额")),
                ("is_active", models.BooleanField(default=True, verbose_name="是否激活")),
                (
                    "created_at",
                    models.DateTimeField(auto_now_add=True, verbose_name="创建时间"),
                ),
                (
                    "updated_at",
                    models.DateTimeField(auto_now=True, verbose_name="更新时间"),
                ),
                (
                    "last_login",
                    models.DateTimeField(blank=True, null=True, verbose_name="最后登录"),
                ),
                (
                    "deleted_at",
                    models.DateTimeField(blank=True, null=True, verbose_name="删除时间"),
                ),
                (
                    "groups",
                    models.ManyToManyField(
                        blank=True,
                        help_text="The groups this user belongs to. A user will get all permissions granted to each of their groups.",
                        related_name="user_set",
                        related_query_name="user",
                        to="auth.group",
                        verbose_name="groups",
                    ),
                ),
                (
                    "user_permissions",
                    models.ManyToManyField(
                        blank=True,
                        help_text="Specific permissions for this user.",
                        related_name="user_set",
                        related_query_name="user",
                        to="auth.permission",
                        verbose_name="user permissions",
                    ),
                ),
            ],
            options={
                "verbose_name": "用户",
                "verbose_name_plural": "用户",
                "db_table": "users",
            },
            managers=[
                ("objects", django.contrib.auth.models.UserManager()),
            ],
        ),
        migrations.CreateModel(
            name="UserProfile",
            fields=[
                (
                    "id",
                    models.UUIDField(
                        default=uuid.uuid4,
                        editable=False,
                        primary_key=True,
                        serialize=False,
                    ),
                ),
                (
                    "created_at",
                    models.DateTimeField(auto_now_add=True, verbose_name="创建时间"),
                ),
                (
                    "updated_at",
                    models.DateTimeField(auto_now=True, verbose_name="更新时间"),
                ),
                (
                    "deleted_at",
                    models.DateTimeField(blank=True, null=True, verbose_name="删除时间"),
                ),
                ("is_active", models.BooleanField(default=True, verbose_name="是否激活")),
                (
                    "phone",
                    models.CharField(
                        blank=True, max_length=20, null=True, verbose_name="手机号"
                    ),
                ),
                ("address", models.TextField(blank=True, null=True, verbose_name="地址")),
                (
                    "birth_date",
                    models.DateField(blank=True, null=True, verbose_name="生日"),
                ),
                (
                    "company",
                    models.CharField(
                        blank=True, max_length=100, null=True, verbose_name="公司"
                    ),
                ),
                ("website", models.URLField(blank=True, null=True, verbose_name="网站")),
                (
                    "language",
                    models.CharField(
                        default="zh-hans", max_length=10, verbose_name="语言偏好"
                    ),
                ),
                (
                    "timezone",
                    models.CharField(
                        default="Asia/Shanghai", max_length=50, verbose_name="时区"
                    ),
                ),
                (
                    "email_notifications",
                    models.BooleanField(default=True, verbose_name="邮件通知"),
                ),
                (
                    "user",
                    models.OneToOneField(
                        on_delete=django.db.models.deletion.CASCADE,
                        related_name="profile",
                        to=settings.AUTH_USER_MODEL,
                    ),
                ),
            ],
            options={
                "verbose_name": "用户资料",
                "verbose_name_plural": "用户资料",
                "db_table": "user_profiles",
            },
        ),
        migrations.AddIndex(
            model_name="user",
            index=models.Index(fields=["email"], name="users_email_4b85f2_idx"),
        ),
        migrations.AddIndex(
            model_name="user",
            index=models.Index(fields=["username"], name="users_usernam_baeb4b_idx"),
        ),
        migrations.AddIndex(
            model_name="user",
            index=models.Index(fields=["created_at"], name="users_created_6541e9_idx"),
        ),
        migrations.AddIndex(
            model_name="user",
            index=models.Index(fields=["role"], name="users_role_0ace22_idx"),
        ),
    ]
