/* CosyVoice Design System CSS v1.0 */
/* 完整的设计系统样式文件，可直接用于项目实施 */

/* ========================================
   1. CSS变量定义
======================================== */
:root {
  /* 主品牌色 - 音频蓝 */
  --brand-primary: #1890ff;
  --brand-primary-light: #69c0ff;
  --brand-primary-dark: #0050b3;
  --brand-primary-hover: #40a9ff;
  --brand-primary-active: #096dd9;
  
  /* 辅助品牌色 - 声波紫 */
  --brand-secondary: #722ed1;
  --brand-secondary-light: #b37feb;
  --brand-secondary-dark: #391085;
  
  /* 功能色彩 */
  --success: #52c41a;
  --warning: #faad14;
  --error: #ff4d4f;
  --info: #1890ff;
  
  /* 音频状态色彩 */
  --audio-playing: #722ed1;
  --audio-paused: #8c8c8c;
  --audio-loading: #faad14;
  --audio-error: #ff4d4f;
  
  /* 中性色彩 */
  --text-primary: #262626;
  --text-secondary: #595959;
  --text-tertiary: #8c8c8c;
  --text-disabled: #bfbfbf;
  
  /* 背景色彩 */
  --bg-primary: #ffffff;
  --bg-secondary: #fafafa;
  --bg-tertiary: #f5f5f5;
  --bg-disabled: #f5f5f5;
  
  /* 边框色彩 */
  --border-primary: #d9d9d9;
  --border-secondary: #f0f0f0;
  --border-focus: #40a9ff;
  
  /* 间距系统 */
  --spacing-xs: 4px;
  --spacing-sm: 8px;
  --spacing-md: 16px;
  --spacing-lg: 24px;
  --spacing-xl: 32px;
  --spacing-xxl: 48px;
  
  /* 字体系统 */
  --font-family-base: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, 'Helvetica Neue', Arial, sans-serif;
  --font-size-xs: 12px;
  --font-size-sm: 14px;
  --font-size-base: 16px;
  --font-size-lg: 18px;
  --font-size-xl: 20px;
  --font-size-xxl: 24px;
  --font-size-xxxl: 32px;
  
  /* 行高 */
  --line-height-tight: 1.25;
  --line-height-base: 1.5;
  --line-height-relaxed: 1.75;
  
  /* 字重 */
  --font-weight-normal: 400;
  --font-weight-medium: 500;
  --font-weight-semibold: 600;
  --font-weight-bold: 700;
  
  /* 圆角 */
  --border-radius-sm: 4px;
  --border-radius-base: 6px;
  --border-radius-lg: 8px;
  --border-radius-xl: 12px;
  
  /* 阴影 */
  --shadow-sm: 0 1px 2px rgba(0, 0, 0, 0.05);
  --shadow-base: 0 2px 8px rgba(0, 0, 0, 0.1);
  --shadow-lg: 0 4px 12px rgba(0, 0, 0, 0.15);
  
  /* 过渡 */
  --transition-fast: all 0.15s ease;
  --transition-base: all 0.3s ease;
  --transition-slow: all 0.5s ease;
}

/* ========================================
   2. 基础样式重置
======================================== */
* {
  box-sizing: border-box;
}

body {
  font-family: var(--font-family-base);
  font-size: var(--font-size-base);
  line-height: var(--line-height-base);
  color: var(--text-primary);
  background: var(--bg-secondary);
  margin: 0;
  padding: 0;
}

/* ========================================
   3. 布局系统
======================================== */
.container {
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 var(--spacing-md);
}

.row {
  display: flex;
  flex-wrap: wrap;
  margin: 0 calc(var(--spacing-sm) * -1);
}

.col {
  padding: 0 var(--spacing-sm);
  flex: 1;
}

/* 响应式列 */
.col-1 { flex: 0 0 8.333333%; }
.col-2 { flex: 0 0 16.666667%; }
.col-3 { flex: 0 0 25%; }
.col-4 { flex: 0 0 33.333333%; }
.col-6 { flex: 0 0 50%; }
.col-8 { flex: 0 0 66.666667%; }
.col-9 { flex: 0 0 75%; }
.col-12 { flex: 0 0 100%; }

/* ========================================
   4. 按钮组件
======================================== */
.btn {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  padding: var(--spacing-sm) var(--spacing-md);
  border: 1px solid transparent;
  border-radius: var(--border-radius-base);
  font-size: var(--font-size-sm);
  font-weight: var(--font-weight-medium);
  line-height: var(--line-height-base);
  cursor: pointer;
  transition: var(--transition-base);
  user-select: none;
  text-decoration: none;
  outline: none;
  background: transparent;
}

.btn:disabled {
  background: var(--bg-disabled);
  border-color: var(--border-secondary);
  color: var(--text-disabled);
  cursor: not-allowed;
}

/* 按钮变体 */
.btn-primary {
  background: var(--brand-primary);
  border-color: var(--brand-primary);
  color: #ffffff;
}

.btn-primary:hover:not(:disabled) {
  background: var(--brand-primary-hover);
  border-color: var(--brand-primary-hover);
}

.btn-primary:active {
  background: var(--brand-primary-active);
  border-color: var(--brand-primary-active);
}

.btn-secondary {
  background: var(--bg-primary);
  border-color: var(--border-primary);
  color: var(--text-primary);
}

.btn-secondary:hover:not(:disabled) {
  border-color: var(--brand-primary);
  color: var(--brand-primary);
}

.btn-audio {
  background: var(--brand-secondary);
  border-color: var(--brand-secondary);
  color: #ffffff;
  min-width: 120px;
}

.btn-audio:hover:not(:disabled) {
  background: var(--brand-secondary-light);
  border-color: var(--brand-secondary-light);
}

/* 按钮尺寸 */
.btn-sm {
  padding: var(--spacing-xs) var(--spacing-sm);
  font-size: var(--font-size-xs);
  border-radius: var(--border-radius-sm);
}

.btn-lg {
  padding: var(--spacing-md) var(--spacing-lg);
  font-size: var(--font-size-lg);
  border-radius: var(--border-radius-lg);
}

.btn-icon {
  width: 32px;
  height: 32px;
  padding: 0;
  border-radius: 50%;
}

/* ========================================
   5. 表单组件
======================================== */
.form-item {
  margin-bottom: var(--spacing-lg);
}

.form-label {
  display: block;
  margin-bottom: var(--spacing-sm);
  color: var(--text-primary);
  font-size: var(--font-size-sm);
  font-weight: var(--font-weight-medium);
  line-height: var(--line-height-base);
}

.form-label.required::after {
  content: '*';
  color: var(--error);
  margin-left: var(--spacing-xs);
}

.form-input,
.form-textarea,
.form-select {
  width: 100%;
  padding: var(--spacing-sm) var(--spacing-md);
  border: 1px solid var(--border-primary);
  border-radius: var(--border-radius-base);
  font-size: var(--font-size-sm);
  line-height: var(--line-height-base);
  color: var(--text-primary);
  background: var(--bg-primary);
  transition: var(--transition-base);
}

.form-input:focus,
.form-textarea:focus,
.form-select:focus {
  border-color: var(--brand-primary);
  box-shadow: 0 0 0 2px rgba(24, 144, 255, 0.2);
  outline: none;
}

.form-input:disabled,
.form-textarea:disabled,
.form-select:disabled {
  background: var(--bg-disabled);
  color: var(--text-disabled);
  cursor: not-allowed;
}

.form-textarea {
  min-height: 80px;
  resize: vertical;
}

/* ========================================
   6. 音频组件
======================================== */
.audio-player {
  background: var(--bg-primary);
  border: 1px solid var(--border-primary);
  border-radius: var(--border-radius-lg);
  padding: var(--spacing-md);
  margin: var(--spacing-md) 0;
  box-shadow: var(--shadow-sm);
}

.audio-controls {
  display: flex;
  align-items: center;
  gap: var(--spacing-md);
  margin-bottom: var(--spacing-md);
}

.audio-play-btn {
  width: 40px;
  height: 40px;
  border-radius: 50%;
  background: var(--brand-secondary);
  border: none;
  color: white;
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: var(--transition-base);
  font-size: 16px;
}

.audio-play-btn:hover {
  background: var(--brand-secondary-light);
  transform: scale(1.05);
}

.audio-progress {
  flex: 1;
  height: 4px;
  background: var(--bg-tertiary);
  border-radius: 2px;
  overflow: hidden;
  cursor: pointer;
}

.audio-progress-bar {
  height: 100%;
  background: var(--brand-secondary);
  transition: width 0.1s;
  width: 0%;
}

.audio-time {
  font-size: var(--font-size-xs);
  color: var(--text-secondary);
  min-width: 40px;
  text-align: center;
}

.audio-info {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-top: var(--spacing-sm);
  font-size: var(--font-size-xs);
  color: var(--text-tertiary);
}

/* 音频状态 */
.audio-status {
  display: inline-flex;
  align-items: center;
  gap: var(--spacing-xs);
  padding: 2px var(--spacing-sm);
  border-radius: 12px;
  font-size: var(--font-size-xs);
  font-weight: var(--font-weight-medium);
}

.audio-status.playing {
  background: rgba(114, 46, 209, 0.1);
  color: var(--audio-playing);
}

.audio-status.paused {
  background: rgba(140, 140, 140, 0.1);
  color: var(--audio-paused);
}

.audio-status.loading {
  background: rgba(250, 173, 20, 0.1);
  color: var(--audio-loading);
}

.audio-status.error {
  background: rgba(255, 77, 79, 0.1);
  color: var(--audio-error);
}

/* ========================================
   7. 卡片组件
======================================== */
.card {
  background: var(--bg-primary);
  border-radius: var(--border-radius-lg);
  box-shadow: var(--shadow-base);
  padding: var(--spacing-md);
  margin-bottom: var(--spacing-md);
  border: 1px solid var(--border-secondary);
}

.card-header {
  margin-bottom: var(--spacing-md);
  padding-bottom: var(--spacing-md);
  border-bottom: 1px solid var(--border-secondary);
}

.card-title {
  font-size: var(--font-size-lg);
  font-weight: var(--font-weight-semibold);
  color: var(--text-primary);
  margin: 0;
}

/* ========================================
   8. 工具类
======================================== */
/* 间距 */
.m-0 { margin: 0; }
.m-xs { margin: var(--spacing-xs); }
.m-sm { margin: var(--spacing-sm); }
.m-md { margin: var(--spacing-md); }
.m-lg { margin: var(--spacing-lg); }
.m-xl { margin: var(--spacing-xl); }

.mt-0 { margin-top: 0; }
.mt-xs { margin-top: var(--spacing-xs); }
.mt-sm { margin-top: var(--spacing-sm); }
.mt-md { margin-top: var(--spacing-md); }
.mt-lg { margin-top: var(--spacing-lg); }
.mt-xl { margin-top: var(--spacing-xl); }

.p-0 { padding: 0; }
.p-xs { padding: var(--spacing-xs); }
.p-sm { padding: var(--spacing-sm); }
.p-md { padding: var(--spacing-md); }
.p-lg { padding: var(--spacing-lg); }
.p-xl { padding: var(--spacing-xl); }

/* 文本 */
.text-primary { color: var(--text-primary); }
.text-secondary { color: var(--text-secondary); }
.text-tertiary { color: var(--text-tertiary); }
.text-disabled { color: var(--text-disabled); }

.text-center { text-align: center; }
.text-left { text-align: left; }
.text-right { text-align: right; }

.font-normal { font-weight: var(--font-weight-normal); }
.font-medium { font-weight: var(--font-weight-medium); }
.font-semibold { font-weight: var(--font-weight-semibold); }
.font-bold { font-weight: var(--font-weight-bold); }

/* 显示 */
.d-none { display: none; }
.d-block { display: block; }
.d-flex { display: flex; }
.d-inline-flex { display: inline-flex; }

.flex-center {
  display: flex;
  align-items: center;
  justify-content: center;
}

.flex-between {
  display: flex;
  align-items: center;
  justify-content: space-between;
}

/* ========================================
   9. 响应式设计
======================================== */
@media (max-width: 767px) {
  .container {
    padding: 0 var(--spacing-sm);
  }
  
  .btn {
    min-height: 44px;
  }
  
  .form-input,
  .form-textarea,
  .form-select {
    font-size: var(--font-size-base); /* 防止iOS缩放 */
  }
  
  .col-sm-12 { flex: 0 0 100%; }
  .col-sm-6 { flex: 0 0 50%; }
}

@media (max-width: 576px) {
  .col-xs-12 { flex: 0 0 100%; }
}

/* ========================================
   10. 动画
======================================== */
@keyframes spin {
  from { transform: rotate(0deg); }
  to { transform: rotate(360deg); }
}

@keyframes pulse {
  0%, 100% { opacity: 1; }
  50% { opacity: 0.5; }
}

@keyframes wave {
  0%, 100% { transform: scaleY(1); }
  50% { transform: scaleY(1.5); }
}

.loading-spinner {
  animation: spin 1s linear infinite;
}

.audio-loading {
  animation: pulse 1.5s ease-in-out infinite;
}

.waveform-active {
  animation: wave 0.8s ease-in-out infinite;
}

/* ========================================
   11. 可访问性
======================================== */
.sr-only {
  position: absolute;
  width: 1px;
  height: 1px;
  padding: 0;
  margin: -1px;
  overflow: hidden;
  clip: rect(0, 0, 0, 0);
  white-space: nowrap;
  border: 0;
}

.focusable:focus {
  outline: 2px solid var(--brand-primary);
  outline-offset: 2px;
}

/* 高对比度模式支持 */
@media (prefers-contrast: high) {
  :root {
    --border-primary: #000000;
    --text-primary: #000000;
    --bg-primary: #ffffff;
  }
}
