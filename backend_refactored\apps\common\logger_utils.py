#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
基于loguru的日志工具函数
参考：日志系统快速开始指南.md
"""

import time
import json
from functools import wraps
from typing import Any, Dict, Optional, Callable
from django.http import HttpRequest, HttpResponse
from django.utils import timezone
from .logger_config import logger, api_logger, performance_logger, security_logger, tasks_logger

def get_client_ip(request: HttpRequest) -> str:
    """获取客户端IP地址"""
    x_forwarded_for = request.META.get('HTTP_X_FORWARDED_FOR')
    if x_forwarded_for:
        ip = x_forwarded_for.split(',')[0].strip()
    else:
        ip = request.META.get('REMOTE_ADDR', 'unknown')
    return ip

def get_user_info(request: HttpRequest) -> Dict[str, Any]:
    """获取用户信息"""
    if hasattr(request, 'user') and request.user.is_authenticated:
        return {
            "user_id": request.user.id,
            "username": getattr(request.user, 'username', 'unknown'),
            "email": getattr(request.user, 'email', 'unknown')
        }
    return {"user": "Anonymous"}

def monitor_performance(threshold_ms: float = 1000):
    """
    性能监控装饰器
    参考指南中的性能监控装饰器
    """
    def decorator(func: Callable) -> Callable:
        @wraps(func)
        def wrapper(*args, **kwargs):
            start_time = time.time()
            function_name = f"{func.__module__}.{func.__name__}"
            
            try:
                result = func(*args, **kwargs)
                duration_ms = (time.time() - start_time) * 1000
                
                # 记录性能日志
                log_data = {
                    "function": function_name,
                    "duration_ms": round(duration_ms, 2),
                    "status": "success",
                    "args_count": len(args),
                    "kwargs_count": len(kwargs)
                }
                
                if duration_ms > threshold_ms:
                    performance_logger.warning(
                        f"慢函数执行: {function_name} 耗时 {duration_ms:.2f}ms",
                        **log_data
                    )
                else:
                    performance_logger.debug(
                        f"函数执行完成: {function_name}",
                        **log_data
                    )
                
                return result
                
            except Exception as e:
                duration_ms = (time.time() - start_time) * 1000
                
                # 记录错误日志
                log_data = {
                    "function": function_name,
                    "duration_ms": round(duration_ms, 2),
                    "status": "error",
                    "error_type": type(e).__name__,
                    "error_message": str(e)
                }
                
                performance_logger.error(
                    f"函数执行失败: {function_name}",
                    **log_data
                )
                
                raise
        
        return wrapper
    return decorator

def log_api_call(request: HttpRequest, response: HttpResponse = None, duration: float = None):
    """
    记录API调用日志
    参考指南中的API请求日志
    """
    # 基础请求信息
    log_data = {
        "method": request.method,
        "endpoint": request.path,
        "query_params": dict(request.GET) if request.GET else {},
        "client_ip": get_client_ip(request),
        "user_agent": request.META.get('HTTP_USER_AGENT', 'unknown'),
        "timestamp": timezone.now().isoformat(),
    }
    
    # 用户信息
    log_data.update(get_user_info(request))
    
    # 响应信息
    if response:
        log_data.update({
            "status_code": response.status_code,
            "duration_ms": round(duration * 1000, 2) if duration else None
        })
        
        # 根据状态码选择日志级别
        if response.status_code >= 500:
            level = "error"
        elif response.status_code >= 400:
            level = "warning"
        else:
            level = "info"
        
        message = f"API请求完成: {request.method} {request.path} -> {response.status_code}"
    else:
        level = "info"
        message = f"API请求开始: {request.method} {request.path}"
    
    # 记录日志
    getattr(api_logger, level)(message, **log_data)

def log_user_action(request: HttpRequest, action: str, **extra_data):
    """
    记录用户操作日志
    参考指南中的关键业务操作日志
    """
    log_data = {
        "action": action,
        "client_ip": get_client_ip(request),
        "user_agent": request.META.get('HTTP_USER_AGENT', 'unknown'),
        "timestamp": timezone.now().isoformat(),
    }
    
    # 用户信息
    log_data.update(get_user_info(request))
    
    # 额外数据
    if extra_data:
        log_data.update(extra_data)
    
    api_logger.info(f"用户操作: {action}", **log_data)

def log_security_event(request: HttpRequest, event_type: str, severity: str = "info", **extra_data):
    """
    记录安全事件日志
    """
    log_data = {
        "event_type": event_type,
        "severity": severity,
        "client_ip": get_client_ip(request),
        "user_agent": request.META.get('HTTP_USER_AGENT', 'unknown'),
        "timestamp": timezone.now().isoformat(),
    }
    
    # 用户信息
    log_data.update(get_user_info(request))
    
    # 额外数据
    if extra_data:
        log_data.update(extra_data)
    
    # 根据严重程度选择日志级别
    level_map = {
        "low": "info",
        "medium": "warning", 
        "high": "error",
        "critical": "critical"
    }
    level = level_map.get(severity, "info")
    
    getattr(security_logger, level)(f"安全事件: {event_type}", **log_data)

def log_task_event(task_id: str, event_type: str, **extra_data):
    """
    记录任务事件日志
    """
    log_data = {
        "task_id": task_id,
        "event_type": event_type,
        "timestamp": timezone.now().isoformat(),
    }
    
    # 额外数据
    if extra_data:
        log_data.update(extra_data)
    
    tasks_logger.info(f"任务事件: {event_type} - {task_id}", **log_data)

def log_model_inference(model_type: str, text: str, duration: float, audio_duration: float = None, **extra_data):
    """
    记录模型推理日志
    """
    rtf = duration / audio_duration if audio_duration and audio_duration > 0 else None
    
    log_data = {
        "model_type": model_type,
        "text_length": len(text),
        "text_preview": text[:50] + "..." if len(text) > 50 else text,
        "inference_duration_s": round(duration, 3),
        "audio_duration_s": round(audio_duration, 3) if audio_duration else None,
        "rtf": round(rtf, 3) if rtf else None,
        "timestamp": timezone.now().isoformat(),
    }
    
    # 额外数据
    if extra_data:
        log_data.update(extra_data)
    
    # 根据RTF判断性能
    if rtf and rtf > 1.0:
        level = "warning"
        message = f"慢推理: {model_type} RTF={rtf:.3f}"
    else:
        level = "info"
        message = f"推理完成: {model_type}"
    
    getattr(performance_logger, level)(message, **log_data)

# 导出
__all__ = [
    'get_client_ip',
    'get_user_info',
    'monitor_performance',
    'log_api_call',
    'log_user_action',
    'log_security_event',
    'log_task_event',
    'log_model_inference',
]
