"""
自定义认证类
"""
from django.contrib.auth import get_user_model
from rest_framework.authentication import BaseAuthentication
from rest_framework.exceptions import AuthenticationFailed
from .models import APIKey
from loguru import logger

User = get_user_model()


class APIKeyAuthentication(BaseAuthentication):
    """API密钥认证"""
    
    def authenticate(self, request):
        api_key = self.get_api_key_from_request(request)
        if not api_key:
            return None
        
        try:
            # 查找API密钥
            api_key_obj = APIKey.objects.select_related('user').get(
                key_hash=APIKey.hash_key(api_key),
                is_active=True
            )
            
            # 检查是否过期
            if api_key_obj.is_expired:
                raise AuthenticationFailed('API密钥已过期')
            
            # 检查用户是否激活
            if not api_key_obj.user.is_active:
                raise AuthenticationFailed('用户账户已被禁用')
            
            # 记录使用
            api_key_obj.record_usage()
            
            logger.info(f"API密钥认证成功: {api_key_obj.user.username}")
            return (api_key_obj.user, api_key_obj)
            
        except APIKey.DoesNotExist:
            raise AuthenticationFailed('无效的API密钥')
        except Exception as e:
            logger.error(f"API密钥认证失败: {e}")
            raise AuthenticationFailed('认证失败')
    
    def get_api_key_from_request(self, request):
        """从请求中获取API密钥"""
        # 从Header中获取
        api_key = request.META.get('HTTP_X_API_KEY')
        if api_key:
            return api_key
        
        # 从Authorization Header中获取
        auth_header = request.META.get('HTTP_AUTHORIZATION', '')
        if auth_header.startswith('ApiKey '):
            return auth_header[7:]  # 移除 'ApiKey ' 前缀
        
        # 从查询参数中获取
        return request.GET.get('api_key')
    
    def authenticate_header(self, request):
        return 'ApiKey'
