"""
任务相关工具函数
"""
import json
from channels.layers import get_channel_layer
from asgiref.sync import async_to_sync
from django.utils import timezone
from loguru import logger


def notify_task_status_update(task, message=None):
    """通知任务状态更新"""
    if not task.user:
        return
    
    channel_layer = get_channel_layer()
    if not channel_layer:
        return
    
    # 发送到用户组
    user_group = f"user_{task.user.id}_tasks"
    
    # 发送到任务组
    task_group = f"task_{task.id}"
    
    event_data = {
        'type': 'task_status_update',
        'task_id': str(task.id),
        'status': task.status,
        'message': message or f'任务状态更新为: {task.get_status_display()}',
        'data': {
            'task_type': task.task_type,
            'created_at': task.created_at.isoformat(),
            'updated_at': task.updated_at.isoformat(),
        }
    }
    
    try:
        # 发送到用户组
        async_to_sync(channel_layer.group_send)(user_group, event_data)
        
        # 发送到任务组
        async_to_sync(channel_layer.group_send)(task_group, event_data)
        
        logger.debug(f"任务状态通知已发送: {task.id} -> {task.status}")
        
    except Exception as e:
        logger.error(f"发送任务状态通知失败: {e}")


def notify_task_completed(task, result=None):
    """通知任务完成"""
    if not task.user:
        return
    
    channel_layer = get_channel_layer()
    if not channel_layer:
        return
    
    user_group = f"user_{task.user.id}_tasks"
    task_group = f"task_{task.id}"
    
    event_data = {
        'type': 'task_completed',
        'task_id': str(task.id),
        'result': result or task.response_data or {}
    }
    
    try:
        async_to_sync(channel_layer.group_send)(user_group, event_data)
        async_to_sync(channel_layer.group_send)(task_group, event_data)
        
        logger.info(f"任务完成通知已发送: {task.id}")
        
    except Exception as e:
        logger.error(f"发送任务完成通知失败: {e}")


def notify_task_failed(task, error=None):
    """通知任务失败"""
    if not task.user:
        return
    
    channel_layer = get_channel_layer()
    if not channel_layer:
        return
    
    user_group = f"user_{task.user.id}_tasks"
    task_group = f"task_{task.id}"
    
    event_data = {
        'type': 'task_failed',
        'task_id': str(task.id),
        'error': error or task.error_message or '任务处理失败'
    }
    
    try:
        async_to_sync(channel_layer.group_send)(user_group, event_data)
        async_to_sync(channel_layer.group_send)(task_group, event_data)
        
        logger.info(f"任务失败通知已发送: {task.id}")
        
    except Exception as e:
        logger.error(f"发送任务失败通知失败: {e}")


def send_user_notification(user, title, message, level='info'):
    """发送用户通知"""
    channel_layer = get_channel_layer()
    if not channel_layer:
        return
    
    user_group = f"user_{user.id}_notifications"
    
    event_data = {
        'type': 'notification',
        'title': title,
        'message': message,
        'level': level,
        'timestamp': timezone.now().isoformat()
    }
    
    try:
        async_to_sync(channel_layer.group_send)(user_group, event_data)
        logger.debug(f"用户通知已发送: {user.username} - {title}")
        
    except Exception as e:
        logger.error(f"发送用户通知失败: {e}")


def broadcast_system_message(message):
    """广播系统消息"""
    channel_layer = get_channel_layer()
    if not channel_layer:
        return
    
    # 广播到所有连接的用户
    event_data = {
        'type': 'system_message',
        'message': message,
        'timestamp': timezone.now().isoformat()
    }
    
    try:
        # 这里需要实现广播逻辑，可以通过Redis或其他方式
        # 暂时记录日志
        logger.info(f"系统消息广播: {message}")
        
    except Exception as e:
        logger.error(f"广播系统消息失败: {e}")


def get_task_queue_status():
    """获取任务队列状态"""
    try:
        from celery import current_app
        
        # 获取活跃任务
        active_tasks = current_app.control.inspect().active()
        
        # 获取等待任务
        reserved_tasks = current_app.control.inspect().reserved()
        
        # 获取工作节点状态
        stats = current_app.control.inspect().stats()
        
        return {
            'active_tasks': active_tasks,
            'reserved_tasks': reserved_tasks,
            'worker_stats': stats,
            'timestamp': timezone.now().isoformat()
        }
        
    except Exception as e:
        logger.error(f"获取任务队列状态失败: {e}")
        return None


def estimate_task_wait_time(task_type=None, priority=None):
    """估算任务等待时间"""
    from .models import Task, TaskStatus, TaskPriority
    from django.db.models import Avg, Count
    
    try:
        # 获取当前队列中的任务数量
        pending_count = Task.objects.filter(status=TaskStatus.PENDING).count()
        processing_count = Task.objects.filter(status=TaskStatus.PROCESSING).count()
        
        # 获取平均处理时间
        avg_processing_time = Task.objects.filter(
            status=TaskStatus.COMPLETED,
            processing_time__isnull=False
        ).aggregate(avg_time=Avg('processing_time'))['avg_time'] or 30.0
        
        # 根据优先级调整
        priority_multiplier = 1.0
        if priority == TaskPriority.HIGH:
            priority_multiplier = 0.5
        elif priority == TaskPriority.URGENT:
            priority_multiplier = 0.2
        elif priority == TaskPriority.LOW:
            priority_multiplier = 2.0
        
        # 估算等待时间（秒）
        estimated_wait = (pending_count * avg_processing_time * priority_multiplier) / max(1, processing_count)
        
        return max(0, estimated_wait)
        
    except Exception as e:
        logger.error(f"估算任务等待时间失败: {e}")
        return 0
