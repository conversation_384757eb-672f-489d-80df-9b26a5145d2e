"""
文件清理管理命令
"""
from django.core.management.base import BaseCommand
from django.utils import timezone
from datetime import timedelta
from apps.files.services import FileService
from apps.files.models import AudioFile
from loguru import logger


class Command(BaseCommand):
    help = '清理文件系统中的过期和孤儿文件'
    
    def add_arguments(self, parser):
        parser.add_argument(
            '--type',
            choices=['expired', 'orphaned', 'temp', 'old'],
            default='expired',
            help='清理类型: expired(过期文件), orphaned(孤儿文件), temp(临时文件), old(旧文件)'
        )
        
        parser.add_argument(
            '--days',
            type=int,
            default=30,
            help='清理多少天前的文件(仅对old类型有效)'
        )
        
        parser.add_argument(
            '--dry-run',
            action='store_true',
            help='只显示将要删除的文件，不实际删除'
        )
        
        parser.add_argument(
            '--force',
            action='store_true',
            help='强制删除，不询问确认'
        )
    
    def handle(self, *args, **options):
        cleanup_type = options['type']
        days = options['days']
        dry_run = options['dry_run']
        force = options['force']
        
        self.stdout.write(f"开始清理{cleanup_type}文件...")
        
        file_service = FileService()
        
        if cleanup_type == 'expired':
            deleted_count = self.cleanup_expired_files(file_service, dry_run, force)
        elif cleanup_type == 'orphaned':
            deleted_count = self.cleanup_orphaned_files(file_service, dry_run, force)
        elif cleanup_type == 'temp':
            deleted_count = self.cleanup_temp_files(file_service, dry_run, force)
        elif cleanup_type == 'old':
            deleted_count = self.cleanup_old_files(file_service, days, dry_run, force)
        
        if dry_run:
            self.stdout.write(
                self.style.WARNING(f"[模拟运行] 将删除 {deleted_count} 个文件")
            )
        else:
            self.stdout.write(
                self.style.SUCCESS(f"成功删除 {deleted_count} 个文件")
            )
    
    def cleanup_expired_files(self, file_service, dry_run, force):
        """清理过期文件"""
        expired_files = AudioFile.objects.filter(
            expires_at__lt=timezone.now(),
            auto_delete=True,
            is_active=True
        )
        
        count = expired_files.count()
        if count == 0:
            self.stdout.write("没有找到过期文件")
            return 0
        
        self.stdout.write(f"找到 {count} 个过期文件")
        
        if dry_run:
            for audio_file in expired_files[:10]:  # 只显示前10个
                self.stdout.write(f"  - {audio_file.filename} (过期时间: {audio_file.expires_at})")
            if count > 10:
                self.stdout.write(f"  ... 还有 {count - 10} 个文件")
            return count
        
        if not force:
            confirm = input(f"确定要删除这 {count} 个过期文件吗? (y/N): ")
            if confirm.lower() != 'y':
                self.stdout.write("操作已取消")
                return 0
        
        return file_service.cleanup_expired_files()
    
    def cleanup_orphaned_files(self, file_service, dry_run, force):
        """清理孤儿文件"""
        if dry_run:
            self.stdout.write("孤儿文件清理需要扫描文件系统，暂不支持模拟运行")
            return 0
        
        if not force:
            confirm = input("确定要清理孤儿文件吗? 这将扫描整个文件系统 (y/N): ")
            if confirm.lower() != 'y':
                self.stdout.write("操作已取消")
                return 0
        
        return file_service.cleanup_orphaned_files()
    
    def cleanup_temp_files(self, file_service, dry_run, force):
        """清理临时文件"""
        import os
        import glob
        from django.conf import settings
        
        temp_dir = os.path.join(settings.MEDIA_ROOT, 'temp')
        if not os.path.exists(temp_dir):
            self.stdout.write("临时文件目录不存在")
            return 0
        
        # 查找1小时前的临时文件
        import time
        cutoff_time = time.time() - 3600
        temp_files = []
        
        for file_path in glob.glob(os.path.join(temp_dir, '*')):
            if os.path.isfile(file_path) and os.path.getmtime(file_path) < cutoff_time:
                temp_files.append(file_path)
        
        count = len(temp_files)
        if count == 0:
            self.stdout.write("没有找到需要清理的临时文件")
            return 0
        
        self.stdout.write(f"找到 {count} 个临时文件")
        
        if dry_run:
            for file_path in temp_files[:10]:
                self.stdout.write(f"  - {os.path.basename(file_path)}")
            if count > 10:
                self.stdout.write(f"  ... 还有 {count - 10} 个文件")
            return count
        
        if not force:
            confirm = input(f"确定要删除这 {count} 个临时文件吗? (y/N): ")
            if confirm.lower() != 'y':
                self.stdout.write("操作已取消")
                return 0
        
        deleted_count = 0
        for file_path in temp_files:
            try:
                os.remove(file_path)
                deleted_count += 1
            except Exception as e:
                self.stdout.write(
                    self.style.ERROR(f"删除文件失败: {file_path}, 错误: {e}")
                )
        
        return deleted_count
    
    def cleanup_old_files(self, file_service, days, dry_run, force):
        """清理旧文件"""
        cutoff_date = timezone.now() - timedelta(days=days)
        old_files = AudioFile.objects.filter(
            created_at__lt=cutoff_date,
            is_active=True
        )
        
        count = old_files.count()
        if count == 0:
            self.stdout.write(f"没有找到 {days} 天前的文件")
            return 0
        
        self.stdout.write(f"找到 {count} 个 {days} 天前的文件")
        
        if dry_run:
            for audio_file in old_files[:10]:
                self.stdout.write(f"  - {audio_file.filename} (创建时间: {audio_file.created_at})")
            if count > 10:
                self.stdout.write(f"  ... 还有 {count - 10} 个文件")
            return count
        
        if not force:
            confirm = input(f"确定要删除这 {count} 个 {days} 天前的文件吗? (y/N): ")
            if confirm.lower() != 'y':
                self.stdout.write("操作已取消")
                return 0
        
        deleted_count = 0
        for audio_file in old_files:
            if file_service.delete_file(audio_file):
                deleted_count += 1
        
        return deleted_count
