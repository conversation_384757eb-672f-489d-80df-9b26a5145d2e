# 日志系统快速开始指南 (Logging System Quick Start Guide)

## 🎯 5分钟快速上手

本指南帮助您在5分钟内快速上手团队统一开发方法论中的日志系统功能，实现类似Python Loguru的简单易用体验。

## 🚀 第一步：选择您的场景 (1分钟)

根据您的项目情况选择对应的场景：

### 🔧 维护开发场景
**适用情况**: 
- 生产环境出现问题需要快速定位
- 系统故障需要紧急修复
- 日常维护和小幅调整

**核心需求**: 快速问题定位和故障分析

**推荐文档**: [日志分析和问题定位模板](./06_工具模板库/维护开发模板/日志分析和问题定位模板.md)

### 🚀 迭代开发场景
**适用情况**:
- 新功能开发和迭代
- 性能监控和优化
- 用户行为分析

**核心需求**: 性能监控和用户行为分析

**推荐文档**: [日志监控和性能分析模板](./06_工具模板库/迭代开发模板/日志监控和性能分析模板.md)

### 🏗️ 重构开发场景
**适用情况**:
- 系统架构重构
- 日志系统升级改造
- 技术栈迁移

**核心需求**: 日志系统现状评估和改进

**推荐文档**: [日志系统现状分析模板](./06_工具模板库/重构开发模板/日志系统现状分析模板.md)

## ⚡ 第二步：快速配置 (2分钟)

### Python项目 (推荐Loguru)
```bash
# 安装Loguru
pip install loguru
```

```python
# app.py - 基础配置
from loguru import logger
import sys
import os

# 移除默认处理器
logger.remove()

# 开发环境：彩色控制台输出
if os.getenv("ENV") == "development":
    logger.add(sys.stdout, level="DEBUG", 
               format="<green>{time:HH:mm:ss}</green> | <level>{level: <8}</level> | <cyan>{name}</cyan>:<cyan>{line}</cyan> | <level>{message}</level>",
               colorize=True)

# 生产环境：文件输出
logger.add("logs/app_{time:YYYY-MM-DD}.log", 
           level="INFO",
           rotation="1 day", 
           retention="30 days",
           compression="zip")

# 错误日志单独文件
logger.add("logs/error_{time:YYYY-MM-DD}.log", 
           level="ERROR",
           rotation="1 day", 
           retention="90 days")

# 使用示例
logger.info("应用启动成功")
logger.info("用户登录", extra={"user_id": 123, "ip": "***********"})
```

### Node.js项目 (推荐Winston)
```bash
# 安装Winston
npm install winston winston-daily-rotate-file
```

```javascript
// logger.js - 基础配置
const winston = require('winston');
require('winston-daily-rotate-file');

const logger = winston.createLogger({
    level: 'info',
    format: winston.format.combine(
        winston.format.timestamp(),
        winston.format.errors({ stack: true }),
        winston.format.json()
    ),
    transports: [
        new winston.transports.DailyRotateFile({
            filename: 'logs/app-%DATE%.log',
            datePattern: 'YYYY-MM-DD',
            maxSize: '20m',
            maxFiles: '30d'
        }),
        new winston.transports.DailyRotateFile({
            filename: 'logs/error-%DATE%.log',
            datePattern: 'YYYY-MM-DD',
            level: 'error',
            maxSize: '20m',
            maxFiles: '90d'
        })
    ]
});

// 开发环境控制台输出
if (process.env.NODE_ENV !== 'production') {
    logger.add(new winston.transports.Console({
        format: winston.format.combine(
            winston.format.colorize(),
            winston.format.simple()
        )
    }));
}

// 使用示例
logger.info('应用启动成功');
logger.info('用户登录', { user_id: 123, ip: '***********' });

module.exports = logger;
```

### Java项目 (推荐Logback)
```xml
<!-- pom.xml -->
<dependency>
    <groupId>ch.qos.logback</groupId>
    <artifactId>logback-classic</artifactId>
    <version>1.4.14</version>
</dependency>
```

```xml
<!-- src/main/resources/logback-spring.xml -->
<configuration>
    <appender name="CONSOLE" class="ch.qos.logback.core.ConsoleAppender">
        <encoder>
            <pattern>%d{HH:mm:ss.SSS} [%thread] %-5level %logger{36} - %msg%n</pattern>
        </encoder>
    </appender>
    
    <appender name="FILE" class="ch.qos.logback.core.rolling.RollingFileAppender">
        <file>logs/app.log</file>
        <rollingPolicy class="ch.qos.logback.core.rolling.TimeBasedRollingPolicy">
            <fileNamePattern>logs/app.%d{yyyy-MM-dd}.log</fileNamePattern>
            <maxHistory>30</maxHistory>
        </rollingPolicy>
        <encoder>
            <pattern>%d{yyyy-MM-dd HH:mm:ss.SSS} [%thread] %-5level %logger{36} - %msg%n</pattern>
        </encoder>
    </appender>
    
    <root level="INFO">
        <appender-ref ref="CONSOLE" />
        <appender-ref ref="FILE" />
    </root>
</configuration>
```

```java
// 使用示例
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

public class Application {
    private static final Logger logger = LoggerFactory.getLogger(Application.class);
    
    public void login(String userId, String ip) {
        logger.info("用户登录成功 - 用户ID: {}, IP: {}", userId, ip);
    }
}
```

## 🔍 第三步：开始记录日志 (1分钟)

### 关键业务操作日志
```python
# 用户操作
logger.info("用户登录成功", extra={
    "user_id": user.id,
    "username": user.username,
    "ip": request.remote_addr,
    "user_agent": request.headers.get('User-Agent')
})

# 业务操作
logger.info("订单创建", extra={
    "user_id": user.id,
    "order_id": order.id,
    "amount": order.amount,
    "payment_method": order.payment_method
})

# 错误处理
try:
    result = process_payment(order)
except PaymentException as e:
    logger.error("支付处理失败", extra={
        "order_id": order.id,
        "user_id": order.user_id,
        "error_code": e.code,
        "error_message": str(e)
    })
```

### API请求日志
```python
# 请求开始
logger.info("API请求开始", extra={
    "request_id": request_id,
    "method": request.method,
    "endpoint": request.path,
    "user_id": current_user.id if current_user else None,
    "ip": request.remote_addr
})

# 请求结束
logger.info("API请求完成", extra={
    "request_id": request_id,
    "method": request.method,
    "endpoint": request.path,
    "status_code": response.status_code,
    "duration_ms": duration
})
```

## 📊 第四步：快速分析 (1分钟)

### 基础查询命令
```bash
# 查看今天的错误日志
grep "ERROR\|CRITICAL" logs/app_$(date +%Y-%m-%d).log | tail -20

# 查看特定用户的操作
grep "user_id.*12345" logs/app_*.log | head -10

# 查看慢请求 (>1000ms)
grep "duration_ms" logs/app_*.log | awk -F'"duration_ms":' '{print $2}' | awk '{if($1>1000) print $0}'
```

### 快速分析脚本
```bash
#!/bin/bash
# quick_analysis.sh
DATE=${1:-$(date +%Y-%m-%d)}

echo "=== 日志快速分析 ($DATE) ==="
echo "错误数量: $(grep -c "ERROR\|CRITICAL" logs/app_$DATE.log)"
echo "用户登录: $(grep -c "用户登录成功" logs/app_$DATE.log)"
echo "慢请求: $(grep "duration_ms" logs/app_$DATE.log | awk '$NF > 1000' | wc -l)"
```

## 🎯 场景化快速指导

### 🔧 维护场景：快速问题定位
```bash
# 1. 查看最近错误
tail -50 logs/error_$(date +%Y-%m-%d).log

# 2. 查找特定错误
grep "NullPointerException" logs/error_*.log

# 3. 追踪请求链路
REQUEST_ID="req_12345"
grep -r "$REQUEST_ID" logs/ | sort
```

### 🚀 迭代场景：性能监控
```python
# 性能监控装饰器
import time
from functools import wraps

def monitor_performance(func):
    @wraps(func)
    def wrapper(*args, **kwargs):
        start_time = time.time()
        try:
            result = func(*args, **kwargs)
            duration = (time.time() - start_time) * 1000
            logger.info(f"函数执行完成: {func.__name__}", extra={
                "function": func.__name__,
                "duration_ms": round(duration, 2),
                "status": "success"
            })
            return result
        except Exception as e:
            duration = (time.time() - start_time) * 1000
            logger.error(f"函数执行失败: {func.__name__}", extra={
                "function": func.__name__,
                "duration_ms": round(duration, 2),
                "status": "error",
                "error": str(e)
            })
            raise
    return wrapper

# 使用示例
@monitor_performance
def process_order(order_id):
    # 业务逻辑
    pass
```

### 🏗️ 重构场景：系统评估
```bash
# 日志质量评估脚本
#!/bin/bash
echo "=== 日志系统评估 ==="
echo "日志文件数量: $(find logs/ -name "*.log" | wc -l)"
echo "总日志大小: $(du -sh logs/)"
echo "最近7天错误数: $(find logs/ -name "*.log" -mtime -7 -exec grep -c "ERROR" {} \; | awk '{sum+=$1} END {print sum}')"
echo "结构化日志比例: $(grep -c "user_id\|request_id" logs/app_*.log | head -1)%"
```

## 📋 快速检查清单

### 基础配置检查 ✅
- [ ] 日志框架已安装和配置
- [ ] 日志级别设置合理 (开发DEBUG，生产INFO)
- [ ] 日志文件轮转配置 (按天轮转，保留30天)
- [ ] 错误日志单独文件
- [ ] 日志目录权限正确

### 日志记录检查 ✅
- [ ] 关键业务操作有日志记录
- [ ] API请求/响应有日志记录
- [ ] 错误和异常有详细日志
- [ ] 日志包含必要的上下文信息 (user_id, request_id等)
- [ ] 敏感信息已脱敏处理

### 分析工具检查 ✅
- [ ] 基础查询命令可以正常使用
- [ ] 快速分析脚本可以运行
- [ ] 日志格式便于解析和分析
- [ ] 可以快速定位特定用户或请求的日志

## 🔗 进阶学习路径

### 深入学习
1. **完整设计**: [日志系统设计模板](./06_工具模板库/通用协作模板/日志系统设计模板.md)
2. **实施指导**: [日志系统实施指南](./06_工具模板库/通用协作模板/日志系统实施指南.md)
3. **场景应用**: 根据您的场景选择对应的专门模板

### 高级功能
1. **监控告警**: 配置Prometheus + Grafana监控
2. **自动化分析**: 使用Python脚本进行自动化分析
3. **可视化展示**: 建立日志分析仪表板
4. **智能告警**: 基于日志的异常检测

## 💡 最佳实践提醒

1. **保持简单**: 像Loguru一样，优先考虑易用性
2. **结构化**: 使用结构化日志格式，便于分析
3. **上下文**: 记录足够的上下文信息
4. **性能**: 注意日志对系统性能的影响
5. **安全**: 注意敏感信息的脱敏处理

## 🆘 常见问题

**Q: 日志文件太大怎么办？**
A: 配置日志轮转，按天分割，定期清理旧文件。

**Q: 如何快速找到特定用户的日志？**
A: 使用grep命令：`grep "user_id.*12345" logs/*.log`

**Q: 日志影响性能怎么办？**
A: 调整日志级别，异步写入，使用高性能日志框架。

**Q: 如何分析日志趋势？**
A: 使用提供的Python分析脚本，或配置监控系统。

---

**快速开始理念**: 让日志系统像Loguru一样简单易用，5分钟上手，立即见效。从基础配置开始，逐步深入，让日志成为开发和运维的得力助手。
