"""
告警系统
"""
import smtplib
from email.mime.text import MIMEText
from email.mime.multipart import MI<PERSON><PERSON>ultipart
from datetime import datetime, timedelta
from django.conf import settings
from django.core.cache import cache
from django.template.loader import render_to_string
from .logging import system_logger


class AlertManager:
    """告警管理器"""
    
    def __init__(self):
        self.alert_rules = {
            'system.cpu.usage': {'threshold': 80, 'operator': '>', 'level': 'warning'},
            'system.memory.usage': {'threshold': 85, 'operator': '>', 'level': 'warning'},
            'system.disk.usage': {'threshold': 90, 'operator': '>', 'level': 'critical'},
            'database.response_time': {'threshold': 1000, 'operator': '>', 'level': 'warning'},
            'redis.response_time': {'threshold': 500, 'operator': '>', 'level': 'warning'},
            'app.tasks.pending': {'threshold': 100, 'operator': '>', 'level': 'warning'},
            'app.tasks.failed': {'threshold': 50, 'operator': '>', 'level': 'warning'},
        }
        
        self.notification_channels = [
            EmailNotifier(),
            WebhookNotifier(),
        ]
    
    def check_metric_alerts(self, metric_name, value):
        """检查指标告警"""
        if metric_name not in self.alert_rules:
            return
        
        rule = self.alert_rules[metric_name]
        threshold = rule['threshold']
        operator = rule['operator']
        level = rule['level']
        
        # 检查告警条件
        triggered = False
        if operator == '>':
            triggered = value > threshold
        elif operator == '<':
            triggered = value < threshold
        elif operator == '>=':
            triggered = value >= threshold
        elif operator == '<=':
            triggered = value <= threshold
        elif operator == '==':
            triggered = value == threshold
        elif operator == '!=':
            triggered = value != threshold
        
        if triggered:
            self.trigger_alert(metric_name, value, rule)
        else:
            self.resolve_alert(metric_name)
    
    def trigger_alert(self, metric_name, value, rule):
        """触发告警"""
        alert_key = f"alert:{metric_name}"
        
        # 检查是否已经告警（避免重复告警）
        if cache.get(alert_key):
            return
        
        alert = {
            'id': f"{metric_name}_{int(datetime.now().timestamp())}",
            'metric': metric_name,
            'value': value,
            'threshold': rule['threshold'],
            'level': rule['level'],
            'message': f"{metric_name} 超过阈值: {value} > {rule['threshold']}",
            'timestamp': datetime.now(),
            'resolved': False
        }
        
        # 设置告警缓存（5分钟内不重复告警）
        cache.set(alert_key, alert, 300)
        
        # 发送通知
        self.send_notifications(alert)
        
        # 记录告警日志
        system_logger.warning(f"告警触发: {alert}")
    
    def resolve_alert(self, metric_name):
        """解决告警"""
        alert_key = f"alert:{metric_name}"
        alert = cache.get(alert_key)
        
        if alert and not alert['resolved']:
            alert['resolved'] = True
            alert['resolved_at'] = datetime.now()
            cache.set(alert_key, alert, 3600)  # 保留1小时
            
            # 发送解决通知
            self.send_resolution_notifications(alert)
            
            # 记录日志
            system_logger.info(f"告警解决: {alert}")
    
    def send_notifications(self, alert):
        """发送告警通知"""
        for notifier in self.notification_channels:
            try:
                notifier.send_alert(alert)
            except Exception as e:
                system_logger.error(f"发送告警通知失败: {notifier.__class__.__name__}, 错误: {e}")
    
    def send_resolution_notifications(self, alert):
        """发送告警解决通知"""
        for notifier in self.notification_channels:
            try:
                notifier.send_resolution(alert)
            except Exception as e:
                system_logger.error(f"发送告警解决通知失败: {notifier.__class__.__name__}, 错误: {e}")
    
    def get_active_alerts(self):
        """获取活跃告警"""
        alerts = []
        for metric_name in self.alert_rules.keys():
            alert_key = f"alert:{metric_name}"
            alert = cache.get(alert_key)
            if alert and not alert['resolved']:
                alerts.append(alert)
        return alerts
    
    def get_alert_history(self, hours=24):
        """获取告警历史"""
        # 这里可以从数据库或日志文件中获取历史告警
        # 暂时返回缓存中的告警
        alerts = []
        for metric_name in self.alert_rules.keys():
            alert_key = f"alert:{metric_name}"
            alert = cache.get(alert_key)
            if alert:
                alerts.append(alert)
        return alerts


class BaseNotifier:
    """通知器基类"""
    
    def send_alert(self, alert):
        """发送告警通知"""
        raise NotImplementedError
    
    def send_resolution(self, alert):
        """发送告警解决通知"""
        raise NotImplementedError


class EmailNotifier(BaseNotifier):
    """邮件通知器"""
    
    def __init__(self):
        self.enabled = all([
            getattr(settings, 'EMAIL_HOST', None),
            getattr(settings, 'EMAIL_HOST_USER', None),
            getattr(settings, 'EMAIL_HOST_PASSWORD', None),
        ])
        
        self.recipients = getattr(settings, 'ALERT_EMAIL_RECIPIENTS', [])
    
    def send_alert(self, alert):
        """发送告警邮件"""
        if not self.enabled or not self.recipients:
            return
        
        subject = f"[CosyVoice] {alert['level'].upper()} 告警: {alert['metric']}"
        
        # 渲染邮件模板
        html_content = render_to_string('emails/alert.html', {
            'alert': alert,
            'site_name': 'CosyVoice API任务管理系统'
        })
        
        text_content = f"""
        告警详情:
        - 指标: {alert['metric']}
        - 当前值: {alert['value']}
        - 阈值: {alert['threshold']}
        - 级别: {alert['level']}
        - 时间: {alert['timestamp']}
        - 消息: {alert['message']}
        """
        
        self._send_email(subject, text_content, html_content)
    
    def send_resolution(self, alert):
        """发送告警解决邮件"""
        if not self.enabled or not self.recipients:
            return
        
        subject = f"[CosyVoice] 告警解决: {alert['metric']}"
        
        text_content = f"""
        告警已解决:
        - 指标: {alert['metric']}
        - 解决时间: {alert.get('resolved_at', '未知')}
        - 原始告警时间: {alert['timestamp']}
        """
        
        self._send_email(subject, text_content)
    
    def _send_email(self, subject, text_content, html_content=None):
        """发送邮件"""
        try:
            msg = MIMEMultipart('alternative')
            msg['Subject'] = subject
            msg['From'] = settings.EMAIL_HOST_USER
            msg['To'] = ', '.join(self.recipients)
            
            # 添加文本内容
            text_part = MIMEText(text_content, 'plain', 'utf-8')
            msg.attach(text_part)
            
            # 添加HTML内容
            if html_content:
                html_part = MIMEText(html_content, 'html', 'utf-8')
                msg.attach(html_part)
            
            # 发送邮件
            with smtplib.SMTP(settings.EMAIL_HOST, settings.EMAIL_PORT) as server:
                if settings.EMAIL_USE_TLS:
                    server.starttls()
                server.login(settings.EMAIL_HOST_USER, settings.EMAIL_HOST_PASSWORD)
                server.send_message(msg)
            
            system_logger.info(f"告警邮件发送成功: {subject}")
            
        except Exception as e:
            system_logger.error(f"发送告警邮件失败: {e}")


class WebhookNotifier(BaseNotifier):
    """Webhook通知器"""
    
    def __init__(self):
        self.webhook_url = getattr(settings, 'ALERT_WEBHOOK_URL', None)
        self.enabled = bool(self.webhook_url)
    
    def send_alert(self, alert):
        """发送告警Webhook"""
        if not self.enabled:
            return
        
        payload = {
            'type': 'alert',
            'alert': {
                'id': alert['id'],
                'metric': alert['metric'],
                'value': alert['value'],
                'threshold': alert['threshold'],
                'level': alert['level'],
                'message': alert['message'],
                'timestamp': alert['timestamp'].isoformat(),
            }
        }
        
        self._send_webhook(payload)
    
    def send_resolution(self, alert):
        """发送告警解决Webhook"""
        if not self.enabled:
            return
        
        payload = {
            'type': 'resolution',
            'alert': {
                'id': alert['id'],
                'metric': alert['metric'],
                'resolved_at': alert.get('resolved_at', datetime.now()).isoformat(),
            }
        }
        
        self._send_webhook(payload)
    
    def _send_webhook(self, payload):
        """发送Webhook"""
        try:
            import requests
            
            response = requests.post(
                self.webhook_url,
                json=payload,
                timeout=10,
                headers={'Content-Type': 'application/json'}
            )
            
            if response.status_code == 200:
                system_logger.info(f"Webhook发送成功: {payload['type']}")
            else:
                system_logger.error(f"Webhook发送失败: {response.status_code}")
                
        except Exception as e:
            system_logger.error(f"发送Webhook失败: {e}")


# 全局告警管理器实例
alert_manager = AlertManager()
