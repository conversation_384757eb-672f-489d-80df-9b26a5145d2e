"""
最小化配置 - 用于快速启动和测试
"""
import os
from pathlib import Path
from dotenv import load_dotenv

# 加载.env文件
load_dotenv()

# 构建路径
BASE_DIR = Path(__file__).resolve().parent.parent.parent

# 安全配置
SECRET_KEY = 'django-insecure-minimal-config-for-testing-only'
DEBUG = True
ALLOWED_HOSTS = ['*']

# 应用配置
DJANGO_APPS = [
    'simpleui',  # 必须放在django.contrib.admin之前
    'django.contrib.admin',
    'django.contrib.auth',
    'django.contrib.contenttypes',
    'django.contrib.sessions',
    'django.contrib.messages',
    'django.contrib.staticfiles',
]

THIRD_PARTY_APPS = [
    'rest_framework',
    'rest_framework_simplejwt',
    'corsheaders',
    'drf_spectacular',
]

LOCAL_APPS = [
    'apps.authentication',
    'apps.users',
    'apps.tasks',
    'apps.files',
    'apps.common',
    'apps.tts_generator',  # TTS生成器
]

INSTALLED_APPS = DJANGO_APPS + THIRD_PARTY_APPS + LOCAL_APPS

# 中间件
MIDDLEWARE = [
    'corsheaders.middleware.CorsMiddleware',
    'django.middleware.security.SecurityMiddleware',
    'django.contrib.sessions.middleware.SessionMiddleware',
    'django.middleware.common.CommonMiddleware',
    'django.middleware.csrf.CsrfViewMiddleware',
    'django.contrib.auth.middleware.AuthenticationMiddleware',
    'django.contrib.messages.middleware.MessageMiddleware',
    'django.middleware.clickjacking.XFrameOptionsMiddleware',
]

ROOT_URLCONF = 'config.urls'

# 模板配置
TEMPLATES = [
    {
        'BACKEND': 'django.template.backends.django.DjangoTemplates',
        'DIRS': [BASE_DIR / 'templates'],
        'APP_DIRS': True,
        'OPTIONS': {
            'context_processors': [
                'django.template.context_processors.debug',
                'django.template.context_processors.request',
                'django.contrib.auth.context_processors.auth',
                'django.contrib.messages.context_processors.messages',
            ],
        },
    },
]

WSGI_APPLICATION = 'config.wsgi.application'

# 数据库配置 - 使用MySQL
DATABASES = {
    'default': {
        'ENGINE': 'django.db.backends.mysql',
        'NAME': os.environ.get('DB_NAME', 'cvapi'),
        'USER': os.environ.get('DB_USER', 'cvapi'),
        'PASSWORD': os.environ.get('DB_PASSWORD', 'NsF3wEe75HNPDeYj'),
        'HOST': os.environ.get('DB_HOST', '*************'),
        'PORT': os.environ.get('DB_PORT', '3306'),
        'OPTIONS': {
            'charset': 'utf8mb4',
            'init_command': "SET sql_mode='STRICT_TRANS_TABLES'",
        },
    }
}

# 国际化
LANGUAGE_CODE = 'zh-hans'
TIME_ZONE = 'Asia/Shanghai'
USE_I18N = True
USE_TZ = True

# 静态文件
STATIC_URL = '/static/'
STATIC_ROOT = BASE_DIR / 'staticfiles'
STATICFILES_DIRS = [BASE_DIR / 'static']

# 媒体文件
MEDIA_URL = '/media/'
MEDIA_ROOT = BASE_DIR / 'media'

# 默认主键字段类型
DEFAULT_AUTO_FIELD = 'django.db.models.BigAutoField'

# 自定义用户模型
AUTH_USER_MODEL = 'users.User'

# REST Framework配置
REST_FRAMEWORK = {
    'DEFAULT_AUTHENTICATION_CLASSES': [
        'rest_framework_simplejwt.authentication.JWTAuthentication',
    ],
    'DEFAULT_PERMISSION_CLASSES': [
        'rest_framework.permissions.IsAuthenticated',
    ],
    'DEFAULT_SCHEMA_CLASS': 'drf_spectacular.openapi.AutoSchema',
}

# JWT配置
from datetime import timedelta
SIMPLE_JWT = {
    'ACCESS_TOKEN_LIFETIME': timedelta(hours=1),
    'REFRESH_TOKEN_LIFETIME': timedelta(days=7),
    'ROTATE_REFRESH_TOKENS': True,
}

# CORS配置
CORS_ALLOW_ALL_ORIGINS = True

# API文档配置
SPECTACULAR_SETTINGS = {
    'TITLE': 'CosyVoice API系统',
    'DESCRIPTION': 'CosyVoice文本转语音API服务',
    'VERSION': '2.0.0',
    'SERVE_INCLUDE_SCHEMA': False,
}

# CosyVoice配置
COSYVOICE_MODEL_DIR = os.environ.get('COSYVOICE_MODEL_DIR', 'pretrained_models/CosyVoice-300M')
COSYVOICE_OUTPUT_DIR = os.environ.get('COSYVOICE_OUTPUT_DIR', 'media/generated_audio')
COSYVOICE_ASSET_DIR = os.environ.get('COSYVOICE_ASSET_DIR', 'asset')
COSYVOICE_SERVICE_PORT = int(os.environ.get('COSYVOICE_SERVICE_PORT', '8001'))
COSYVOICE_CONDA_ENV = os.environ.get('COSYVOICE_CONDA_ENV', 'cosyvoice')
COSYVOICE_AUTO_START = os.environ.get('COSYVOICE_AUTO_START', 'False').lower() == 'true'

# 使用loguru替代Django内置日志系统
# 参考：日志系统快速开始指南.md

# 禁用Django默认日志配置，使用loguru
LOGGING_CONFIG = None

# 在应用启动时初始化loguru
def setup_logging():
    """初始化loguru日志系统"""
    from apps.common.logger_config import setup_loguru
    setup_loguru()

# 确保日志目录存在
import os
LOGS_DIR = os.path.join(BASE_DIR, 'logs')
os.makedirs(LOGS_DIR, exist_ok=True)

# ================================
# SimpleUI 配置
# ================================

# 隐藏右侧SimpleUI广告链接和使用分析
SIMPLEUI_HOME_INFO = False
SIMPLEUI_ANALYSIS = False

# 使用中文
SIMPLEUI_DEFAULT_LANGUAGE = 'zh-hans'

# 设置主题 - 使用默认主题
SIMPLEUI_DEFAULT_THEME = 'default.css'

# 自定义Logo和标题
SIMPLEUI_LOGO = 'https://avatars2.githubusercontent.com/u/13655483?s=60&v=4'
SIMPLEUI_HOME_TITLE = 'CosyVoice管理后台'
SIMPLEUI_HOME_ICON = 'fas fa-microphone'

# 自定义首页
SIMPLEUI_INDEX = '/admin/dashboard/'  # 使用自定义仪表板页面

# 自定义菜单 - 使用最简单配置
SIMPLEUI_CONFIG = {
    'system_keep': False,  # 不保留系统菜单，避免重复
    'dynamic': False,  # 关闭动态菜单
    'menus': [
        {
            'name': '系统概览',
            'icon': 'fas fa-tachometer-alt',
            'url': '/admin/dashboard/',
        },
        {
            'name': 'TTS生成器',
            'icon': 'fas fa-microphone',
            'models': [
                {
                    'name': 'TTS生成器',
                    'icon': 'fas fa-magic',
                    'url': '/admin/tts-generator/'
                },
                {
                    'name': 'API测试器',
                    'icon': 'fas fa-flask',
                    'url': '/admin/tts-generator/api-tester/'
                },
                {
                    'name': '说话人管理',
                    'icon': 'fas fa-user-friends',
                    'url': '/admin/tts_generator/speaker/'
                },
                {
                    'name': '生成记录',
                    'icon': 'fas fa-history',
                    'url': '/admin/tts_generator/quickgeneration/'
                }
            ]
        },
        {
            'app': 'tasks',
            'name': '任务管理',
            'icon': 'fas fa-tasks',
            'models': [
                {
                    'name': '任务列表',
                    'icon': 'fas fa-list',
                    'url': '/admin/tasks/task/'
                },
                {
                    'name': '任务日志',
                    'icon': 'fas fa-file-alt',
                    'url': '/admin/tasks/tasklog/'
                }
            ]
        },
        {
            'app': 'files',
            'name': '文件管理',
            'icon': 'fas fa-folder',
            'models': [
                {
                    'name': '音频文件',
                    'icon': 'fas fa-music',
                    'url': '/admin/files/audiofile/'
                },
                {
                    'name': '文件分享',
                    'icon': 'fas fa-share-alt',
                    'url': '/admin/files/fileshare/'
                },
                {
                    'name': '操作记录',
                    'icon': 'fas fa-history',
                    'url': '/admin/files/fileoperation/'
                }
            ]
        },
        {
            'app': 'users',
            'name': '用户管理',
            'icon': 'fas fa-users',
            'models': [
                {
                    'name': '用户列表',
                    'icon': 'fas fa-user',
                    'url': '/admin/users/user/'
                },
                {
                    'name': '用户资料',
                    'icon': 'fas fa-user-edit',
                    'url': '/admin/users/userprofile/'
                }
            ]
        },
        {
            'app': 'auth',
            'name': '权限管理',
            'icon': 'fas fa-shield-alt',
            'models': [
                {
                    'name': '用户组',
                    'icon': 'fas fa-users-cog',
                    'url': '/admin/auth/group/'
                }
            ]
        }
    ]
}
