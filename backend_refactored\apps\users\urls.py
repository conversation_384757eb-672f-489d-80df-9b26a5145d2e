"""
用户相关URL配置
"""
from django.urls import path
from . import views

urlpatterns = [
    # 用户资料
    path('profile/', views.UserProfileView.as_view(), name='user_profile'),
    path('stats/', views.user_stats, name='user_stats'),

    # 用户管理（管理员）
    path('', views.UserListView.as_view(), name='user_list'),
    path('<uuid:pk>/', views.UserDetailView.as_view(), name='user_detail'),
    path('<uuid:user_id>/quota/', views.update_user_quota, name='update_user_quota'),
    path('<uuid:user_id>/quota/reset/', views.reset_user_quota, name='reset_user_quota'),
    path('<uuid:user_id>/toggle/', views.toggle_user_status, name='toggle_user_status'),
]
