"""
认证相关视图
"""
from rest_framework import status, generics, permissions
from rest_framework.decorators import api_view, permission_classes
from rest_framework.response import Response
from rest_framework.views import APIView
from rest_framework_simplejwt.tokens import RefreshToken
from django.contrib.auth import get_user_model
from django.utils import timezone
from drf_spectacular.utils import extend_schema, OpenApiParameter
from .serializers import (
    UserRegistrationSerializer, UserLoginSerializer, PasswordChangeSerializer,
    APIKeySerializer, APIKeyListSerializer, LoginLogSerializer,
    PasswordResetRequestSerializer, PasswordResetConfirmSerializer
)
from .models import APIKey, LoginLog, PasswordResetToken
from .utils import get_client_ip, send_password_reset_email
from loguru import logger

User = get_user_model()


class UserRegistrationView(generics.CreateAPIView):
    """用户注册"""
    serializer_class = UserRegistrationSerializer
    permission_classes = [permissions.AllowAny]
    
    @extend_schema(
        summary="用户注册",
        description="创建新用户账户",
        tags=["认证"]
    )
    def post(self, request, *args, **kwargs):
        serializer = self.get_serializer(data=request.data)
        if serializer.is_valid():
            user = serializer.save()
            
            # 记录注册日志
            LoginLog.objects.create(
                user=user,
                ip_address=get_client_ip(request),
                user_agent=request.META.get('HTTP_USER_AGENT', ''),
                login_type='password',
                success=True
            )
            
            # 生成JWT Token
            refresh = RefreshToken.for_user(user)
            
            logger.info(f"用户注册成功: {user.username}")
            
            return Response({
                'message': '注册成功',
                'user': {
                    'id': str(user.id),
                    'username': user.username,
                    'email': user.email,
                    'role': user.role,
                },
                'tokens': {
                    'access': str(refresh.access_token),
                    'refresh': str(refresh),
                }
            }, status=status.HTTP_201_CREATED)
        
        return Response(serializer.errors, status=status.HTTP_400_BAD_REQUEST)


class UserLoginView(APIView):
    """用户登录"""
    permission_classes = [permissions.AllowAny]
    
    @extend_schema(
        summary="用户登录",
        description="用户邮箱密码登录",
        request=UserLoginSerializer,
        tags=["认证"]
    )
    def post(self, request):
        serializer = UserLoginSerializer(data=request.data, context={'request': request})
        
        if serializer.is_valid():
            user = serializer.validated_data['user']
            
            # 更新最后登录时间
            user.last_login = timezone.now()
            user.save(update_fields=['last_login'])
            
            # 记录登录日志
            LoginLog.objects.create(
                user=user,
                ip_address=get_client_ip(request),
                user_agent=request.META.get('HTTP_USER_AGENT', ''),
                login_type='password',
                success=True
            )
            
            # 生成JWT Token
            refresh = RefreshToken.for_user(user)
            
            logger.info(f"用户登录成功: {user.username}")
            
            return Response({
                'message': '登录成功',
                'user': {
                    'id': str(user.id),
                    'username': user.username,
                    'email': user.email,
                    'role': user.role,
                    'quota_limit': user.quota_limit,
                    'quota_used': user.quota_used,
                    'quota_remaining': user.quota_remaining,
                },
                'tokens': {
                    'access': str(refresh.access_token),
                    'refresh': str(refresh),
                }
            })
        
        # 记录登录失败日志
        email = request.data.get('email')
        if email:
            try:
                user = User.objects.get(email=email)
                LoginLog.objects.create(
                    user=user,
                    ip_address=get_client_ip(request),
                    user_agent=request.META.get('HTTP_USER_AGENT', ''),
                    login_type='password',
                    success=False,
                    failure_reason='密码错误'
                )
            except User.DoesNotExist:
                pass
        
        return Response(serializer.errors, status=status.HTTP_400_BAD_REQUEST)


class PasswordChangeView(APIView):
    """修改密码"""
    permission_classes = [permissions.IsAuthenticated]
    
    @extend_schema(
        summary="修改密码",
        description="修改当前用户密码",
        request=PasswordChangeSerializer,
        tags=["认证"]
    )
    def post(self, request):
        serializer = PasswordChangeSerializer(data=request.data, context={'request': request})
        
        if serializer.is_valid():
            serializer.save()
            logger.info(f"用户修改密码: {request.user.username}")
            return Response({'message': '密码修改成功'})
        
        return Response(serializer.errors, status=status.HTTP_400_BAD_REQUEST)


class APIKeyListCreateView(generics.ListCreateAPIView):
    """API密钥列表和创建"""
    permission_classes = [permissions.IsAuthenticated]
    
    def get_serializer_class(self):
        if self.request.method == 'POST':
            return APIKeySerializer
        return APIKeyListSerializer
    
    def get_queryset(self):
        return APIKey.objects.filter(user=self.request.user, is_active=True)
    
    @extend_schema(
        summary="获取API密钥列表",
        description="获取当前用户的所有API密钥",
        tags=["API密钥"]
    )
    def get(self, request, *args, **kwargs):
        return super().get(request, *args, **kwargs)
    
    @extend_schema(
        summary="创建API密钥",
        description="为当前用户创建新的API密钥",
        tags=["API密钥"]
    )
    def post(self, request, *args, **kwargs):
        response = super().post(request, *args, **kwargs)
        if response.status_code == status.HTTP_201_CREATED:
            logger.info(f"用户创建API密钥: {request.user.username}")
        return response


class APIKeyDetailView(generics.RetrieveUpdateDestroyAPIView):
    """API密钥详情"""
    serializer_class = APIKeyListSerializer
    permission_classes = [permissions.IsAuthenticated]
    
    def get_queryset(self):
        return APIKey.objects.filter(user=self.request.user)
    
    @extend_schema(
        summary="获取API密钥详情",
        tags=["API密钥"]
    )
    def get(self, request, *args, **kwargs):
        return super().get(request, *args, **kwargs)
    
    @extend_schema(
        summary="更新API密钥",
        tags=["API密钥"]
    )
    def put(self, request, *args, **kwargs):
        return super().put(request, *args, **kwargs)
    
    @extend_schema(
        summary="删除API密钥",
        tags=["API密钥"]
    )
    def delete(self, request, *args, **kwargs):
        api_key = self.get_object()
        api_key.soft_delete()
        logger.info(f"用户删除API密钥: {request.user.username} - {api_key.name}")
        return Response({'message': 'API密钥已删除'})


class LoginLogListView(generics.ListAPIView):
    """登录日志列表"""
    serializer_class = LoginLogSerializer
    permission_classes = [permissions.IsAuthenticated]
    
    def get_queryset(self):
        return LoginLog.objects.filter(user=self.request.user).order_by('-created_at')
    
    @extend_schema(
        summary="获取登录日志",
        description="获取当前用户的登录日志",
        tags=["认证"]
    )
    def get(self, request, *args, **kwargs):
        return super().get(request, *args, **kwargs)


@extend_schema(
    summary="密码重置请求",
    description="发送密码重置邮件",
    request=PasswordResetRequestSerializer,
    tags=["认证"]
)
@api_view(['POST'])
@permission_classes([permissions.AllowAny])
def password_reset_request(request):
    """密码重置请求"""
    serializer = PasswordResetRequestSerializer(data=request.data)
    
    if serializer.is_valid():
        email = serializer.validated_data['email']
        user = User.objects.get(email=email)
        
        # 创建重置令牌
        reset_token = PasswordResetToken.objects.create(
            user=user,
            token=PasswordResetToken.generate_token(),
            expires_at=timezone.now() + timezone.timedelta(hours=1)
        )
        
        # 发送重置邮件
        send_password_reset_email(user, reset_token.token)
        
        logger.info(f"发送密码重置邮件: {email}")
        return Response({'message': '密码重置邮件已发送'})
    
    return Response(serializer.errors, status=status.HTTP_400_BAD_REQUEST)


@extend_schema(
    summary="密码重置确认",
    description="使用重置令牌重置密码",
    request=PasswordResetConfirmSerializer,
    tags=["认证"]
)
@api_view(['POST'])
@permission_classes([permissions.AllowAny])
def password_reset_confirm(request):
    """密码重置确认"""
    serializer = PasswordResetConfirmSerializer(data=request.data)
    
    if serializer.is_valid():
        token = serializer.validated_data['token']
        new_password = serializer.validated_data['new_password']
        
        try:
            reset_token = PasswordResetToken.objects.get(token=token)
            
            if not reset_token.is_valid:
                return Response({'error': '重置令牌无效或已过期'}, 
                              status=status.HTTP_400_BAD_REQUEST)
            
            # 重置密码
            user = reset_token.user
            user.set_password(new_password)
            user.save()
            
            # 标记令牌为已使用
            reset_token.used = True
            reset_token.save()
            
            logger.info(f"密码重置成功: {user.username}")
            return Response({'message': '密码重置成功'})
            
        except PasswordResetToken.DoesNotExist:
            return Response({'error': '重置令牌无效'}, 
                          status=status.HTTP_400_BAD_REQUEST)
    
    return Response(serializer.errors, status=status.HTTP_400_BAD_REQUEST)
