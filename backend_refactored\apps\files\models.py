"""
文件管理模型
"""
import os
from django.db import models
from django.contrib.auth import get_user_model
from apps.common.models import BaseModel

User = get_user_model()


class FileType(models.TextChoices):
    """文件类型"""
    AUDIO_INPUT = 'audio_input', '输入音频'
    AUDIO_OUTPUT = 'audio_output', '输出音频'
    REFERENCE = 'reference', '参考音频'
    TEMP = 'temp', '临时文件'


class StorageType(models.TextChoices):
    """存储类型"""
    LOCAL = 'local', '本地存储'
    OSS = 'oss', '阿里云OSS'
    S3 = 's3', 'Amazon S3'


class AudioFile(BaseModel):
    """音频文件模型"""
    user = models.ForeignKey(User, on_delete=models.CASCADE, related_name='audio_files', null=True, blank=True)
    filename = models.CharField('文件名', max_length=255)
    original_filename = models.CharField('原始文件名', max_length=255, blank=True)
    file_type = models.CharField('文件类型', max_length=20, choices=FileType.choices)
    storage_type = models.CharField('存储类型', max_length=10, choices=StorageType.choices, default=StorageType.LOCAL)
    
    # 文件路径和URL
    file_path = models.CharField('文件路径', max_length=500)
    file_url = models.URLField('文件URL', max_length=500, blank=True)
    
    # 文件信息
    file_size = models.BigIntegerField('文件大小(字节)')
    mime_type = models.CharField('MIME类型', max_length=100, blank=True)
    
    # 音频信息
    duration = models.FloatField('时长(秒)', null=True, blank=True)
    sample_rate = models.IntegerField('采样率', null=True, blank=True)
    channels = models.IntegerField('声道数', default=1)
    bit_rate = models.IntegerField('比特率', null=True, blank=True)
    
    # 访问控制
    is_public = models.BooleanField('是否公开', default=False)
    access_count = models.IntegerField('访问次数', default=0)
    last_accessed = models.DateTimeField('最后访问时间', null=True, blank=True)
    
    # 生命周期管理
    expires_at = models.DateTimeField('过期时间', null=True, blank=True)
    auto_delete = models.BooleanField('自动删除', default=True)
    
    # 元数据
    metadata = models.JSONField('元数据', default=dict, blank=True)
    tags = models.JSONField('标签', default=list, blank=True)
    
    class Meta:
        db_table = 'audio_files'
        verbose_name = '音频文件'
        verbose_name_plural = '音频文件'
        indexes = [
            models.Index(fields=['user', 'created_at']),
            models.Index(fields=['file_type']),
            models.Index(fields=['expires_at']),
            models.Index(fields=['created_at']),
            models.Index(fields=['filename']),
        ]
        ordering = ['-created_at']

    def __str__(self):
        user_info = f"{self.user.username}" if self.user else "匿名用户"
        return f"{user_info} - {self.filename}"

    @property
    def is_expired(self):
        """检查是否过期"""
        if not self.expires_at:
            return False
        from django.utils import timezone
        return timezone.now() > self.expires_at

    @property
    def file_size_human(self):
        """人类可读的文件大小"""
        size = self.file_size
        for unit in ['B', 'KB', 'MB', 'GB']:
            if size < 1024.0:
                return f"{size:.1f} {unit}"
            size /= 1024.0
        return f"{size:.1f} TB"

    @property
    def duration_human(self):
        """人类可读的时长"""
        if not self.duration:
            return "未知"
        
        minutes = int(self.duration // 60)
        seconds = int(self.duration % 60)
        if minutes > 0:
            return f"{minutes}分{seconds}秒"
        else:
            return f"{seconds}秒"

    def record_access(self):
        """记录访问"""
        from django.utils import timezone
        self.access_count += 1
        self.last_accessed = timezone.now()
        self.save(update_fields=['access_count', 'last_accessed'])

    def get_download_url(self):
        """获取下载URL"""
        if self.storage_type == StorageType.OSS:
            # 生成OSS临时访问URL
            from apps.files.services import OSSService
            oss_service = OSSService()
            return oss_service.get_download_url(self.file_path)
        else:
            # 本地文件URL
            from django.conf import settings
            return f"{settings.MEDIA_URL}{self.file_path}"

    def delete_file(self):
        """删除物理文件"""
        if self.storage_type == StorageType.OSS:
            from apps.files.services import OSSService
            oss_service = OSSService()
            oss_service.delete_file(self.file_path)
        else:
            # 删除本地文件
            full_path = os.path.join(settings.MEDIA_ROOT, self.file_path)
            if os.path.exists(full_path):
                os.remove(full_path)


class FileShare(BaseModel):
    """文件分享"""
    audio_file = models.ForeignKey(AudioFile, on_delete=models.CASCADE, related_name='shares')
    share_token = models.CharField('分享令牌', max_length=255, unique=True)
    password = models.CharField('访问密码', max_length=100, blank=True)
    expires_at = models.DateTimeField('过期时间', null=True, blank=True)
    max_downloads = models.IntegerField('最大下载次数', null=True, blank=True)
    download_count = models.IntegerField('下载次数', default=0)
    
    class Meta:
        db_table = 'file_shares'
        verbose_name = '文件分享'
        verbose_name_plural = '文件分享'
        indexes = [
            models.Index(fields=['share_token']),
            models.Index(fields=['expires_at']),
        ]

    def __str__(self):
        return f"{self.audio_file.filename} - 分享"

    @property
    def is_expired(self):
        """检查是否过期"""
        if self.expires_at:
            from django.utils import timezone
            return timezone.now() > self.expires_at
        return False

    @property
    def is_download_limit_reached(self):
        """检查是否达到下载限制"""
        if self.max_downloads:
            return self.download_count >= self.max_downloads
        return False

    @property
    def is_valid(self):
        """检查分享是否有效"""
        return not self.is_expired and not self.is_download_limit_reached

    def record_download(self):
        """记录下载"""
        self.download_count += 1
        self.save(update_fields=['download_count'])


class FileOperation(BaseModel):
    """文件操作记录"""
    user = models.ForeignKey(User, on_delete=models.CASCADE, related_name='file_operations', null=True, blank=True)
    audio_file = models.ForeignKey(AudioFile, on_delete=models.CASCADE, related_name='operations')
    operation = models.CharField('操作类型', max_length=20, choices=[
        ('upload', '上传'),
        ('download', '下载'),
        ('delete', '删除'),
        ('share', '分享'),
        ('access', '访问'),
    ])
    ip_address = models.GenericIPAddressField('IP地址', null=True, blank=True)
    user_agent = models.TextField('用户代理', blank=True)
    extra_data = models.JSONField('额外数据', null=True, blank=True)
    
    class Meta:
        db_table = 'file_operations'
        verbose_name = '文件操作记录'
        verbose_name_plural = '文件操作记录'
        indexes = [
            models.Index(fields=['user', 'created_at']),
            models.Index(fields=['audio_file', 'operation']),
            models.Index(fields=['created_at']),
        ]
        ordering = ['-created_at']

    def __str__(self):
        user_info = f"{self.user.username}" if self.user else "匿名用户"
        return f"{user_info} - {self.get_operation_display()} - {self.audio_file.filename}"
