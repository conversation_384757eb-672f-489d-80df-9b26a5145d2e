<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>CosyVoice 管理后台 - 仪表板</title>
    <style>
        body {
            margin: 0;
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, 'Helvetica Neue', Arial, sans-serif;
            background: #f5f5f5;
            color: #333;
        }
        
        .dashboard-container {
            padding: 20px;
            max-width: 1400px;
            margin: 0 auto;
        }
        
        .dashboard-welcome {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 30px;
            border-radius: 12px;
            margin-bottom: 30px;
            text-align: center;
        }
        
        .dashboard-welcome h1 {
            margin: 0 0 10px 0;
            font-size: 28px;
            font-weight: 600;
        }
        
        .dashboard-welcome p {
            margin: 0;
            opacity: 0.9;
            font-size: 16px;
        }
        
        .stats-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 20px;
            margin-bottom: 30px;
        }
        
        .stat-card {
            background: white;
            padding: 24px;
            border-radius: 12px;
            box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
            border: 1px solid #f0f0f0;
            transition: transform 0.3s ease, box-shadow 0.3s ease;
        }
        
        .stat-card:hover {
            transform: translateY(-2px);
            box-shadow: 0 4px 16px rgba(0, 0, 0, 0.15);
        }
        
        .stat-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 16px;
        }
        
        .stat-title {
            font-size: 14px;
            color: #8c8c8c;
            margin: 0;
            font-weight: 500;
        }
        
        .stat-icon {
            width: 40px;
            height: 40px;
            border-radius: 8px;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 20px;
        }
        
        .stat-number {
            font-size: 32px;
            font-weight: bold;
            margin-bottom: 8px;
            line-height: 1;
        }
        
        .stat-trend {
            font-size: 12px;
            display: flex;
            align-items: center;
            gap: 4px;
        }
        
        .trend-up {
            color: #52c41a;
        }
        
        .trend-down {
            color: #ff4d4f;
        }
        
        .dashboard-grid {
            display: grid;
            grid-template-columns: 2fr 1fr;
            gap: 24px;
            margin-top: 20px;
        }
        
        .chart-card {
            background: white;
            padding: 24px;
            border-radius: 12px;
            box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
            border: 1px solid #f0f0f0;
        }
        
        .chart-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 24px;
        }
        
        .chart-title {
            font-size: 16px;
            font-weight: 600;
            margin: 0;
            color: #262626;
        }
        
        .chart-placeholder {
            height: 300px;
            background: #fafafa;
            border-radius: 8px;
            display: flex;
            flex-direction: column;
            align-items: center;
            justify-content: center;
            color: #8c8c8c;
            border: 2px dashed #d9d9d9;
        }
        
        .activity-list {
            list-style: none;
            padding: 0;
            margin: 0;
        }
        
        .activity-item {
            display: flex;
            align-items: center;
            padding: 12px 0;
            border-bottom: 1px solid #f0f0f0;
        }
        
        .activity-item:last-child {
            border-bottom: none;
        }
        
        .activity-icon {
            width: 32px;
            height: 32px;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            margin-right: 12px;
            font-size: 14px;
        }
        
        .activity-content {
            flex: 1;
        }
        
        .activity-title {
            font-size: 14px;
            margin-bottom: 4px;
            color: #262626;
        }
        
        .activity-time {
            font-size: 12px;
            color: #8c8c8c;
        }
        
        .quick-actions {
            display: grid;
            grid-template-columns: repeat(2, 1fr);
            gap: 12px;
            margin-top: 24px;
        }
        
        .quick-action-btn {
            padding: 16px;
            background: white;
            border: 1px solid #f0f0f0;
            border-radius: 8px;
            text-align: center;
            text-decoration: none;
            color: #595959;
            transition: all 0.3s;
            display: block;
        }
        
        .quick-action-btn:hover {
            border-color: #1890ff;
            color: #1890ff;
            transform: translateY(-2px);
            box-shadow: 0 4px 12px rgba(24, 144, 255, 0.15);
            text-decoration: none;
        }
        
        .quick-action-icon {
            font-size: 24px;
            margin-bottom: 8px;
        }
        
        .back-to-admin {
            position: fixed;
            top: 20px;
            right: 20px;
            background: #1890ff;
            color: white;
            padding: 10px 20px;
            border-radius: 6px;
            text-decoration: none;
            font-size: 14px;
            box-shadow: 0 2px 8px rgba(24, 144, 255, 0.3);
            transition: all 0.3s;
        }
        
        .back-to-admin:hover {
            background: #40a9ff;
            color: white;
            text-decoration: none;
            transform: translateY(-1px);
            box-shadow: 0 4px 12px rgba(24, 144, 255, 0.4);
        }
        
        /* 响应式设计 */
        @media (max-width: 768px) {
            .stats-grid {
                grid-template-columns: 1fr;
            }
            
            .dashboard-grid {
                grid-template-columns: 1fr;
            }
            
            .quick-actions {
                grid-template-columns: 1fr;
            }
            
            .dashboard-container {
                padding: 10px;
            }
        }
        
        @media (max-width: 1200px) {
            .stats-grid {
                grid-template-columns: repeat(2, 1fr);
            }
        }
    </style>
</head>
<body>
    <!-- 返回管理后台按钮 -->
    <a href="/admin/" class="back-to-admin">← 返回管理后台</a>
    
    <div class="dashboard-container">
        <!-- 欢迎区域 -->
        <div class="dashboard-welcome">
            <h1>🎵 欢迎使用 CosyVoice 管理后台</h1>
            <p>智能语音合成系统 - 让声音更有温度</p>
        </div>
        
        <!-- 统计卡片 -->
        <div class="stats-grid">
            <div class="stat-card">
                <div class="stat-header">
                    <h3 class="stat-title">今日生成</h3>
                    <div class="stat-icon" style="background: #e6f7ff; color: #1890ff;">🎵</div>
                </div>
                <div class="stat-number" style="color: #1890ff;" id="today-generated">{{ stats.today_generated|default:0 }}</div>
                <div class="stat-trend trend-up">
                    <span>↗</span>
                    <span>+{{ stats.today_growth|default:15 }}% 较昨日</span>
                </div>
            </div>

            <div class="stat-card">
                <div class="stat-header">
                    <h3 class="stat-title">总用户数</h3>
                    <div class="stat-icon" style="background: #f6ffed; color: #52c41a;">👥</div>
                </div>
                <div class="stat-number" style="color: #52c41a;">{{ stats.total_users|default:0 }}</div>
                <div class="stat-trend">
                    <span style="color: #8c8c8c;">活跃用户: {{ stats.active_users|default:0 }}</span>
                </div>
            </div>

            <div class="stat-card">
                <div class="stat-header">
                    <h3 class="stat-title">成功率</h3>
                    <div class="stat-icon" style="background: #fff2e8; color: #fa8c16;">✅</div>
                </div>
                <div class="stat-number" style="color: #fa8c16;">{{ stats.success_rate|default:"98.5" }}%</div>
                <div class="stat-trend trend-up">
                    <span>↗</span>
                    <span>+{{ stats.success_growth|default:2.1 }}% 较昨日</span>
                </div>
            </div>

            <div class="stat-card">
                <div class="stat-header">
                    <h3 class="stat-title">音频文件</h3>
                    <div class="stat-icon" style="background: #f9f0ff; color: #722ed1;">📁</div>
                </div>
                <div class="stat-number" style="color: #722ed1;">{{ stats.total_files|default:0 }}</div>
                <div class="stat-trend trend-up">
                    <span>↗</span>
                    <span>+{{ stats.files_growth|default:8 }}% 较昨日</span>
                </div>
            </div>
        </div>

        <!-- 图表和活动区域 -->
        <div class="dashboard-grid">
            <!-- 使用趋势图表 -->
            <div class="chart-card">
                <div class="chart-header">
                    <h3 class="chart-title">使用趋势</h3>
                    <select style="border: 1px solid #d9d9d9; border-radius: 4px; padding: 4px 8px;">
                        <option>最近7天</option>
                        <option>最近30天</option>
                        <option>最近90天</option>
                    </select>
                </div>
                <div class="chart-placeholder">
                    <div style="font-size: 48px; margin-bottom: 16px;">📈</div>
                    <div style="font-size: 16px; margin-bottom: 8px;">使用趋势图表</div>
                    <small style="color: #bfbfbf;">（实际项目中这里会是真实的图表）</small>
                </div>
            </div>

            <!-- 最近活动 -->
            <div class="chart-card">
                <div class="chart-header">
                    <h3 class="chart-title">最近活动</h3>
                    <a href="/admin/tasks/task/" style="color: #1890ff; text-decoration: none; font-size: 14px;">查看全部</a>
                </div>
                <ul class="activity-list">
                    {% for activity in recent_activities %}
                    <li class="activity-item">
                        <div class="activity-icon" style="background: {{ activity.bg_color }}; color: {{ activity.color }};">{{ activity.icon }}</div>
                        <div class="activity-content">
                            <div class="activity-title">{{ activity.title }}</div>
                            <div class="activity-time">{{ activity.time }}</div>
                        </div>
                    </li>
                    {% empty %}
                    <li class="activity-item">
                        <div class="activity-icon" style="background: #e6f7ff; color: #1890ff;">🎵</div>
                        <div class="activity-content">
                            <div class="activity-title">生成了新的音频文件</div>
                            <div class="activity-time">2分钟前</div>
                        </div>
                    </li>
                    <li class="activity-item">
                        <div class="activity-icon" style="background: #f6ffed; color: #52c41a;">📁</div>
                        <div class="activity-content">
                            <div class="activity-title">上传了参考音频</div>
                            <div class="activity-time">5分钟前</div>
                        </div>
                    </li>
                    <li class="activity-item">
                        <div class="activity-icon" style="background: #fff2e8; color: #fa8c16;">👤</div>
                        <div class="activity-content">
                            <div class="activity-title">新用户注册</div>
                            <div class="activity-time">10分钟前</div>
                        </div>
                    </li>
                    <li class="activity-item">
                        <div class="activity-icon" style="background: #f9f0ff; color: #722ed1;">⚙️</div>
                        <div class="activity-content">
                            <div class="activity-title">更新了系统设置</div>
                            <div class="activity-time">1小时前</div>
                        </div>
                    </li>
                    {% endfor %}
                </ul>

                <!-- 快速操作 -->
                <div class="quick-actions">
                    <a href="/direct/" class="quick-action-btn" target="_blank">
                        <div class="quick-action-icon">🎵</div>
                        <div>快速生成</div>
                    </a>
                    <a href="/admin/files/audiofile/" class="quick-action-btn">
                        <div class="quick-action-icon">📁</div>
                        <div>音频文件</div>
                    </a>
                    <a href="/admin/tasks/task/" class="quick-action-btn">
                        <div class="quick-action-icon">📋</div>
                        <div>查看任务</div>
                    </a>
                    <a href="/admin/users/user/" class="quick-action-btn">
                        <div class="quick-action-icon">👥</div>
                        <div>用户管理</div>
                    </a>
                </div>
            </div>
        </div>
    </div>

    <script>
        // 模拟实时数据更新
        setInterval(() => {
            const todayGenerated = document.getElementById('today-generated');
            if (todayGenerated) {
                const current = parseInt(todayGenerated.textContent);
                const change = Math.random() > 0.7 ? 1 : 0; // 30% 概率增加
                if (change) {
                    todayGenerated.textContent = current + change;
                }
            }
        }, 30000); // 每30秒检查一次

        // 添加卡片点击效果
        document.querySelectorAll('.stat-card').forEach(card => {
            card.addEventListener('click', function() {
                this.style.transform = 'scale(0.98)';
                setTimeout(() => {
                    this.style.transform = '';
                }, 150);
            });
        });
    </script>
</body>
</html>
