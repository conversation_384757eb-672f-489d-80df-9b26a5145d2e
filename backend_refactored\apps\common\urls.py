"""
兼容性API路由 - 保持原有接口不变
"""
from django.urls import path
from . import views, health_views, monitoring_views, direct_inference_views

urlpatterns = [
    # 根路径
    path('', views.root_view, name='root'),
    
    # 原有API接口 - 保持完全兼容 (使用任务队列)
    path('inference_sft', views.inference_sft, name='inference_sft'),
    path('inference_zero_shot', views.inference_zero_shot, name='inference_zero_shot'),
    path('inference_cross_lingual', views.inference_cross_lingual, name='inference_cross_lingual'),
    path('inference_instruct', views.inference_instruct, name='inference_instruct'),
    path('inference_instruct2', views.inference_instruct2, name='inference_instruct2'),

    # 直接推理API - 使用集成的CosyVoice引擎 (立即返回结果)
    path('direct/inference_sft', direct_inference_views.direct_inference_sft, name='direct_inference_sft'),
    path('direct/inference_zero_shot', direct_inference_views.direct_inference_zero_shot, name='direct_inference_zero_shot'),
    path('direct/inference_cross_lingual', direct_inference_views.direct_inference_cross_lingual, name='direct_inference_cross_lingual'),
    path('direct/inference_instruct', direct_inference_views.direct_inference_instruct, name='direct_inference_instruct'),
    path('direct/inference_instruct2', direct_inference_views.direct_inference_instruct2, name='direct_inference_instruct2'),
    path('direct/list_speakers', direct_inference_views.list_speakers, name='direct_list_speakers'),
    path('direct/engine_status', direct_inference_views.engine_status, name='direct_engine_status'),
    
    # 辅助接口
    path('list_speakers', views.list_speakers, name='list_speakers'),
    path('reference_audios', views.reference_audios, name='reference_audios'),
    path('list_generated_audio', views.list_generated_audio, name='list_generated_audio'),
    
    # 文件操作
    path('download/<str:filename>', views.download_audio, name='download_audio'),
    path('delete_audio/<str:filename>', views.delete_audio, name='delete_audio'),
    path('clear_generated_audio', views.clear_generated_audio, name='clear_generated_audio'),

    # 任务状态查询
    path('task_status/<uuid:task_id>', views.task_status, name='task_status'),
    path('task_result/<uuid:task_id>', views.task_result, name='task_result'),

    # 健康检查
    path('health/', health_views.health_check, name='health_check'),
    path('health/db/', health_views.db_health_check, name='db_health_check'),
    path('health/redis/', health_views.redis_health_check, name='redis_health_check'),
    path('health/celery/', health_views.celery_health_check, name='celery_health_check'),
    path('health/model/', health_views.model_health_check, name='model_health_check'),

    # 监控API
    path('monitoring/metrics/', monitoring_views.system_metrics, name='system_metrics'),
    path('monitoring/alerts/', monitoring_views.system_alerts, name='system_alerts'),
    path('monitoring/performance/', monitoring_views.performance_stats, name='performance_stats'),
    path('monitoring/overview/', monitoring_views.system_overview, name='system_overview'),
]
