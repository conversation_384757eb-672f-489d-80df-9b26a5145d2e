"""
Celery配置文件
"""
import os
from celery import Celery

# 设置Django设置模块
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'config.settings.development')

app = Celery('cosyvoice')

# 使用Django设置配置Celery
app.config_from_object('django.conf:settings', namespace='CELERY')

# 导入定时任务配置
from apps.tasks.celery_config import CELERY_BEAT_SCHEDULE, CELERY_TIMEZONE
app.conf.beat_schedule = CELERY_BEAT_SCHEDULE
app.conf.timezone = CELERY_TIMEZONE

# 自动发现任务
app.autodiscover_tasks()

@app.task(bind=True)
def debug_task(self):
    print(f'Request: {self.request!r}')
