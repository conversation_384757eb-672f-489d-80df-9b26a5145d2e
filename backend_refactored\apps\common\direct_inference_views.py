"""
直接推理API视图 - 使用集成的CosyVoice引擎
"""
import os
import json
import tempfile
from django.http import JsonResponse, FileResponse, Http404
from django.views.decorators.csrf import csrf_exempt
from django.views.decorators.http import require_http_methods
from django.conf import settings
from django.utils.decorators import method_decorator
from rest_framework.decorators import api_view, permission_classes
from rest_framework.permissions import AllowAny
from rest_framework.response import Response
from drf_spectacular.utils import extend_schema, OpenApiParameter
from apps.tasks.models import Task, TaskType, TaskStatus
from apps.files.models import AudioFile, FileType
from apps.users.models import User
from .decorators import enhanced_api_compatibility, rate_limit, require_quota, monitor_performance
from .cosyvoice_engine import get_cosyvoice_engine
from .logger_config import api_logger as logger, performance_logger
from .logger_utils import log_api_call, log_model_inference, monitor_performance as perf_monitor


def cleanup_temp_file(file_path, delay=0.1, max_retries=3):
    """
    清理临时文件的辅助函数 - 异步清理，不影响主流程

    Args:
        file_path: 要清理的文件路径
        delay: 每次重试前的等待时间（秒）
        max_retries: 最大重试次数
    """
    import time
    import gc
    import threading

    def async_cleanup():
        """异步清理函数"""
        for attempt in range(max_retries):
            try:
                # 强制垃圾回收，释放可能的文件句柄
                gc.collect()

                # 等待指定时间
                if delay > 0:
                    time.sleep(delay)

                # 尝试删除文件
                if os.path.exists(file_path):
                    os.unlink(file_path)
                    logger.info(f"成功清理临时文件: {file_path}")
                    return True
                else:
                    logger.info(f"临时文件已不存在: {file_path}")
                    return True

            except Exception as e:
                if attempt < max_retries - 1:
                    logger.warning(f"清理临时文件失败 (尝试 {attempt + 1}/{max_retries}): {e}")
                    time.sleep(delay * (2 ** attempt))  # 指数退避
                else:
                    logger.warning(f"临时文件清理失败，将在后台继续尝试: {e}")
                    # 启动后台清理任务
                    schedule_background_cleanup(file_path)
                    return False

        return False

    # 启动异步清理线程
    cleanup_thread = threading.Thread(target=async_cleanup, daemon=True)
    cleanup_thread.start()

    return True  # 立即返回，不等待清理完成


def schedule_background_cleanup(file_path):
    """安排后台清理任务"""
    import time
    import threading

    def background_cleanup():
        """后台清理函数，延迟更长时间后尝试清理"""
        time.sleep(30)  # 等待30秒
        for attempt in range(10):  # 尝试10次
            try:
                if os.path.exists(file_path):
                    os.unlink(file_path)
                    logger.info(f"后台成功清理临时文件: {file_path}")
                    return
                else:
                    return
            except Exception as e:
                if attempt < 9:
                    time.sleep(60)  # 每次等待1分钟
                else:
                    logger.error(f"后台清理最终失败，文件可能需要手动清理: {file_path}")

    # 启动后台清理线程
    bg_thread = threading.Thread(target=background_cleanup, daemon=True)
    bg_thread.start()


def get_client_ip(request):
    """获取客户端IP地址"""
    x_forwarded_for = request.META.get('HTTP_X_FORWARDED_FOR')
    if x_forwarded_for:
        ip = x_forwarded_for.split(',')[0]
    else:
        ip = request.META.get('REMOTE_ADDR')
    return ip


def save_audio_to_file_model(audio_info: dict, task: Task = None, user: User = None) -> AudioFile:
    """将音频信息保存到AudioFile模型"""
    try:
        # 创建AudioFile记录
        audio_file = AudioFile.objects.create(
            user=user,
            filename=audio_info['filename'],
            file_path=audio_info['file_path'],
            file_size=audio_info['file_size'],
            duration=audio_info['duration'],
            sample_rate=audio_info['sample_rate'],
            file_type=FileType.AUDIO_OUTPUT,
            metadata={
                'engine': 'cosyvoice_integrated',
                'task_id': str(task.id) if task else None,
                'parameters': audio_info.get('parameters', {})
            }
        )
        
        # 更新任务关联
        if task:
            task.response_data = {
                'audio_file_id': str(audio_file.id),
                'filename': audio_file.filename,
                'duration': audio_file.duration,
                'file_size': audio_file.file_size,
                'download_url': f"/media/generated_audio/{audio_file.filename}"
            }
            task.status = TaskStatus.COMPLETED
            task.save()
        
        return audio_file
        
    except Exception as e:
        logger.error(f"保存音频文件模型失败: {e}")
        raise


@extend_schema(
    summary="SFT模式推理 (直接)",
    description="SFT模式文本转语音推理，直接返回结果",
    tags=["直接推理API"],
    parameters=[
        OpenApiParameter(name='tts_text', description='要转换的文本', required=True, type=str),
        OpenApiParameter(name='spk_id', description='说话人ID', required=True, type=str),
    ]
)
@enhanced_api_compatibility
@rate_limit(max_requests=30, window_seconds=60)
@require_quota(quota_cost=1)
@monitor_performance(threshold_seconds=10.0)
@csrf_exempt
@require_http_methods(["POST"])
def direct_inference_sft(request):
    """SFT模式推理 - 直接返回结果"""
    try:
        # 获取CosyVoice引擎
        engine = get_cosyvoice_engine()
        
        # 检查引擎是否可用
        if not engine.is_available():
            # 尝试加载模型
            if not engine.load_model():
                return JsonResponse({
                    'error': 'CosyVoice模型未加载，请检查模型文件是否存在',
                    'model_dir': str(engine.model_dir)
                }, status=503)
        
        # 获取参数
        tts_text = request.POST.get('tts_text', '')
        spk_id = request.POST.get('spk_id', '中文女')
        
        if not tts_text:
            return JsonResponse({'error': '缺少必需参数: tts_text'}, status=400)
        
        # 检查用户配额
        user = request.user if request.user.is_authenticated else None
        if user and not user.can_use_quota():
            return JsonResponse({
                'error': '配额不足',
                'quota_used': user.quota_used,
                'quota_limit': user.quota_limit
            }, status=429)
        
        # 创建任务记录
        task = Task.objects.create(
            user=user,
            task_type=TaskType.SFT,
            request_data={
                'tts_text': tts_text,
                'spk_id': spk_id,
            },
            status=TaskStatus.PROCESSING,
            client_ip=get_client_ip(request),
            user_agent=request.META.get('HTTP_USER_AGENT', ''),
        )
        
        try:
            # 执行推理
            result = engine.inference_sft(tts_text, spk_id)
            
            # 保存音频文件
            audio_file = save_audio_to_file_model(result['audio_info'], task, user)
            
            # 使用配额
            if user:
                user.use_quota()
            
            logger.info(f"SFT推理成功: {task.id}, 文本: '{tts_text}', 说话人: '{spk_id}'")
            
            # 返回兼容格式
            return JsonResponse({
                'success': True,
                'message': 'SFT推理成功',
                'task_id': str(task.id),
                'audio_info': {
                    'filename': audio_file.filename,
                    'duration': audio_file.duration,
                    'file_size': audio_file.file_size,
                    'sample_rate': audio_file.sample_rate,
                    'download_url': f"/media/generated_audio/{audio_file.filename}",
                    'file_id': str(audio_file.id)
                },
                'text': tts_text,
                'speaker': spk_id
            })
            
        except Exception as e:
            # 更新任务状态为失败
            task.status = TaskStatus.FAILED
            task.error_message = str(e)
            task.save()
            raise
            
    except Exception as e:
        logger.error(f"SFT推理失败: {e}")
        return JsonResponse({'error': str(e)}, status=500)


@extend_schema(
    summary="零样本语音克隆 (直接)",
    description="零样本语音克隆推理，直接返回结果",
    tags=["直接推理API"]
)
@enhanced_api_compatibility
@rate_limit(max_requests=20, window_seconds=60)
@require_quota(quota_cost=2)
@monitor_performance(threshold_seconds=15.0)
@csrf_exempt
@require_http_methods(["POST"])
def direct_inference_zero_shot(request):
    """零样本语音克隆 - 直接返回结果"""
    try:
        # 获取CosyVoice引擎
        engine = get_cosyvoice_engine()
        
        if not engine.is_available():
            if not engine.load_model():
                return JsonResponse({
                    'error': 'CosyVoice模型未加载，请检查模型文件是否存在'
                }, status=503)
        
        # 获取参数
        tts_text = request.POST.get('tts_text', '')
        prompt_text = request.POST.get('prompt_text', '')
        prompt_wav = request.FILES.get('prompt_wav')
        
        if not all([tts_text, prompt_text, prompt_wav]):
            return JsonResponse({
                'error': '缺少必需参数: tts_text, prompt_text, prompt_wav'
            }, status=400)
        
        # 检查用户配额
        user = request.user if request.user.is_authenticated else None
        if user and not user.can_use_quota():
            return JsonResponse({
                'error': '配额不足',
                'quota_used': user.quota_used,
                'quota_limit': user.quota_limit
            }, status=429)
        
        # 创建任务记录
        task = Task.objects.create(
            user=user,
            task_type=TaskType.ZERO_SHOT,
            request_data={
                'tts_text': tts_text,
                'prompt_text': prompt_text,
                'prompt_wav_filename': prompt_wav.name,
            },
            status=TaskStatus.PROCESSING,
            client_ip=get_client_ip(request),
            user_agent=request.META.get('HTTP_USER_AGENT', ''),
        )
        
        temp_wav_path = None
        try:
            # 保存临时音频文件到指定目录，避免Windows文件锁定问题
            import os
            import time
            import uuid
            from django.conf import settings

            file_extension = os.path.splitext(prompt_wav.name)[1].lower()
            if file_extension not in ['.wav', '.mp3', '.flac']:
                file_extension = '.wav'  # 默认使用wav格式

            # 使用generated_audio目录而不是系统临时目录
            # 确保使用正确的输出目录路径
            output_dir_setting = getattr(settings, 'COSYVOICE_OUTPUT_DIR', 'media/generated_audio')
            if os.path.isabs(output_dir_setting):
                temp_dir = os.path.join(output_dir_setting, 'temp')
            else:
                temp_dir = os.path.join(settings.BASE_DIR, output_dir_setting, 'temp')
            os.makedirs(temp_dir, exist_ok=True)

            # 生成唯一的临时文件名
            temp_filename = f"temp_zero_shot_{uuid.uuid4().hex[:8]}{file_extension}"
            temp_wav_path = os.path.join(temp_dir, temp_filename)

            # 直接写入文件，避免使用NamedTemporaryFile
            with open(temp_wav_path, 'wb') as temp_file:
                for chunk in prompt_wav.chunks():
                    temp_file.write(chunk)

            # 等待一小段时间确保文件完全写入
            time.sleep(0.1)

            try:
                # 执行推理
                result = engine.inference_zero_shot(tts_text, prompt_text, temp_wav_path)
                
                # 保存音频文件
                audio_file = save_audio_to_file_model(result['audio_info'], task, user)
                
                # 使用配额
                if user:
                    user.use_quota(2)  # 零样本推理消耗更多配额

                logger.info(f"零样本推理成功: {task.id}, 文本: '{tts_text}'")

                # 更新任务状态为成功
                task.status = TaskStatus.COMPLETED
                task.response_data = json.dumps({
                    'audio_file_id': str(audio_file.id),
                    'filename': audio_file.filename,
                    'duration': audio_file.duration,
                    'file_size': audio_file.file_size,
                    'download_url': f"/media/generated_audio/{audio_file.filename}"
                })
                task.save()

                # 异步清理临时文件，不影响响应
                if temp_wav_path and os.path.exists(temp_wav_path):
                    cleanup_temp_file(temp_wav_path)

                return JsonResponse({
                    'success': True,
                    'message': '零样本推理成功',
                    'task_id': str(task.id),
                    'audio_info': {
                        'filename': audio_file.filename,
                        'duration': audio_file.duration,
                        'file_size': audio_file.file_size,
                        'sample_rate': audio_file.sample_rate,
                        'download_url': f"/media/generated_audio/{audio_file.filename}",
                        'file_id': str(audio_file.id)
                    },
                    'text': tts_text,
                    'prompt_text': prompt_text,
                    'reference_audio': prompt_wav.name
                })

            except Exception as inference_error:
                # 推理过程中的错误
                logger.error(f"零样本推理失败: {inference_error}")

                # 更新任务状态为失败
                task.status = TaskStatus.FAILED
                task.error_message = str(inference_error)
                task.save()

                # 异步清理临时文件
                if temp_wav_path and os.path.exists(temp_wav_path):
                    cleanup_temp_file(temp_wav_path, delay=0.5)

                raise inference_error

        except Exception as e:
            # 文件处理或其他错误
            logger.error(f"零样本推理失败: {e}")

            # 更新任务状态为失败
            task.status = TaskStatus.FAILED
            task.error_message = str(e)
            task.save()

            # 异步清理临时文件
            if temp_wav_path and os.path.exists(temp_wav_path):
                cleanup_temp_file(temp_wav_path, delay=0.5)

            raise
            
    except Exception as e:
        logger.error(f"零样本推理失败: {e}")
        return JsonResponse({'error': str(e)}, status=500)


@extend_schema(
    summary="跨语言语音合成 (直接)",
    description="跨语言语音合成推理，直接返回结果",
    tags=["直接推理API"]
)
@enhanced_api_compatibility
@rate_limit(max_requests=15, window_seconds=60)
@require_quota(quota_cost=3)
@monitor_performance(threshold_seconds=20.0)
@csrf_exempt
@require_http_methods(["POST"])
def direct_inference_cross_lingual(request):
    """跨语言语音合成 - 直接返回结果"""
    try:
        # 获取CosyVoice引擎
        engine = get_cosyvoice_engine()

        if not engine.is_available():
            if not engine.load_model():
                return JsonResponse({
                    'error': 'CosyVoice模型未加载，请检查模型文件是否存在'
                }, status=503)

        # 获取参数
        tts_text = request.POST.get('tts_text', '')
        prompt_wav = request.FILES.get('prompt_wav')

        if not all([tts_text, prompt_wav]):
            return JsonResponse({
                'error': '缺少必需参数: tts_text, prompt_wav'
            }, status=400)

        # 检查用户配额
        user = request.user if request.user.is_authenticated else None
        if user and not user.can_use_quota():
            return JsonResponse({
                'error': '配额不足',
                'quota_used': user.quota_used,
                'quota_limit': user.quota_limit
            }, status=429)

        # 创建任务记录
        task = Task.objects.create(
            user=user,
            task_type=TaskType.CROSS_LINGUAL,
            request_data={
                'tts_text': tts_text,
                'prompt_wav_filename': prompt_wav.name,
            },
            status=TaskStatus.PROCESSING,
            client_ip=get_client_ip(request),
            user_agent=request.META.get('HTTP_USER_AGENT', ''),
        )

        temp_wav_path = None
        try:
            # 保存临时音频文件到指定目录，避免Windows文件锁定问题
            import os
            import time
            import uuid
            from django.conf import settings

            file_extension = os.path.splitext(prompt_wav.name)[1].lower()
            if file_extension not in ['.wav', '.mp3', '.flac']:
                file_extension = '.wav'  # 默认使用wav格式

            # 使用generated_audio目录而不是系统临时目录
            # 确保使用正确的输出目录路径
            output_dir_setting = getattr(settings, 'COSYVOICE_OUTPUT_DIR', 'media/generated_audio')
            if os.path.isabs(output_dir_setting):
                temp_dir = os.path.join(output_dir_setting, 'temp')
            else:
                temp_dir = os.path.join(settings.BASE_DIR, output_dir_setting, 'temp')
            os.makedirs(temp_dir, exist_ok=True)

            # 生成唯一的临时文件名
            temp_filename = f"temp_cross_lingual_{uuid.uuid4().hex[:8]}{file_extension}"
            temp_wav_path = os.path.join(temp_dir, temp_filename)

            # 直接写入文件，避免使用NamedTemporaryFile
            with open(temp_wav_path, 'wb') as temp_file:
                for chunk in prompt_wav.chunks():
                    temp_file.write(chunk)

            # 等待一小段时间确保文件完全写入
            time.sleep(0.1)

            try:
                # 执行推理
                result = engine.inference_cross_lingual(tts_text, temp_wav_path)

                # 保存音频文件
                audio_file = save_audio_to_file_model(result['audio_info'], task, user)

                # 使用配额
                if user:
                    user.use_quota(3)  # 跨语言推理消耗更多配额

                logger.info(f"跨语言推理成功: {task.id}, 文本: '{tts_text}'")

                # 更新任务状态为成功
                task.status = TaskStatus.COMPLETED
                task.response_data = json.dumps({
                    'audio_file_id': str(audio_file.id),
                    'filename': audio_file.filename,
                    'duration': audio_file.duration,
                    'file_size': audio_file.file_size,
                    'download_url': f"/media/generated_audio/{audio_file.filename}"
                })
                task.save()

                # 异步清理临时文件，不影响响应
                if temp_wav_path and os.path.exists(temp_wav_path):
                    cleanup_temp_file(temp_wav_path)

                return JsonResponse({
                    'success': True,
                    'message': '跨语言推理成功',
                    'task_id': str(task.id),
                    'audio_info': {
                        'filename': audio_file.filename,
                        'duration': audio_file.duration,
                        'file_size': audio_file.file_size,
                        'sample_rate': audio_file.sample_rate,
                        'download_url': f"/media/generated_audio/{audio_file.filename}",
                        'file_id': str(audio_file.id)
                    },
                    'text': tts_text,
                    'reference_audio': prompt_wav.name
                })

            except Exception as inference_error:
                # 推理过程中的错误
                logger.error(f"跨语言推理失败: {inference_error}")

                # 更新任务状态为失败
                task.status = TaskStatus.FAILED
                task.error_message = str(inference_error)
                task.save()

                # 异步清理临时文件
                if temp_wav_path and os.path.exists(temp_wav_path):
                    cleanup_temp_file(temp_wav_path, delay=0.5)

                raise inference_error

        except Exception as e:
            # 文件处理或其他错误
            logger.error(f"跨语言推理失败: {e}")

            # 更新任务状态为失败
            task.status = TaskStatus.FAILED
            task.error_message = str(e)
            task.save()

            # 异步清理临时文件
            if temp_wav_path and os.path.exists(temp_wav_path):
                cleanup_temp_file(temp_wav_path, delay=0.5)

            raise

    except Exception as e:
        logger.error(f"跨语言推理失败: {e}")
        return JsonResponse({'error': str(e)}, status=500)


@extend_schema(
    summary="指令式语音合成 (直接)",
    description="指令式语音合成推理，直接返回结果",
    tags=["直接推理API"]
)
@enhanced_api_compatibility
@rate_limit(max_requests=20, window_seconds=60)
@require_quota(quota_cost=2)
@monitor_performance(threshold_seconds=15.0)
@csrf_exempt
@require_http_methods(["POST"])
def direct_inference_instruct(request):
    """指令式语音合成 - 直接返回结果"""
    try:
        # 获取CosyVoice引擎
        engine = get_cosyvoice_engine()

        if not engine.is_available():
            if not engine.load_model():
                return JsonResponse({
                    'error': 'CosyVoice模型未加载，请检查模型文件是否存在'
                }, status=503)

        # 获取参数
        tts_text = request.POST.get('tts_text', '')
        spk_id = request.POST.get('spk_id', '中文女')
        instruct_text = request.POST.get('instruct_text', '')

        if not all([tts_text, instruct_text]):
            return JsonResponse({
                'error': '缺少必需参数: tts_text, instruct_text'
            }, status=400)

        # 检查用户配额
        user = request.user if request.user.is_authenticated else None
        if user and not user.can_use_quota():
            return JsonResponse({
                'error': '配额不足',
                'quota_used': user.quota_used,
                'quota_limit': user.quota_limit
            }, status=429)

        # 创建任务记录
        task = Task.objects.create(
            user=user,
            task_type=TaskType.INSTRUCT,
            request_data={
                'tts_text': tts_text,
                'spk_id': spk_id,
                'instruct_text': instruct_text,
            },
            status=TaskStatus.PROCESSING,
            client_ip=get_client_ip(request),
            user_agent=request.META.get('HTTP_USER_AGENT', ''),
        )

        try:
            # 执行推理
            result = engine.inference_instruct(tts_text, spk_id, instruct_text)

            # 保存音频文件
            audio_file = save_audio_to_file_model(result['audio_info'], task, user)

            # 使用配额
            if user:
                user.use_quota(2)  # 指令式推理消耗更多配额

            logger.info(f"指令式推理成功: {task.id}, 文本: '{tts_text}', 指令: '{instruct_text}'")

            return JsonResponse({
                'success': True,
                'message': '指令式推理成功',
                'task_id': str(task.id),
                'audio_info': {
                    'filename': audio_file.filename,
                    'duration': audio_file.duration,
                    'file_size': audio_file.file_size,
                    'sample_rate': audio_file.sample_rate,
                    'download_url': f"/media/generated_audio/{audio_file.filename}",
                    'file_id': str(audio_file.id)
                },
                'text': tts_text,
                'speaker': spk_id,
                'instruction': instruct_text
            })

        except Exception as e:
            # 更新任务状态为失败
            task.status = TaskStatus.FAILED
            task.error_message = str(e)
            task.save()
            raise

    except Exception as e:
        logger.error(f"指令式推理失败: {e}")
        return JsonResponse({'error': str(e)}, status=500)


@extend_schema(
    summary="获取可用说话人列表",
    description="获取CosyVoice支持的说话人列表",
    tags=["直接推理API"]
)
@api_view(['GET'])
@permission_classes([AllowAny])
def list_speakers(request):
    """获取可用说话人列表"""
    try:
        engine = get_cosyvoice_engine()
        
        if not engine.is_available():
            # 返回默认说话人列表
            speakers = ["中文女", "中文男", "英文女", "英文男"]
        else:
            speakers = engine.list_speakers()
        
        return JsonResponse({
            'speakers': speakers,
            'model_loaded': engine.is_available()
        })
        
    except Exception as e:
        logger.error(f"获取说话人列表失败: {e}")
        return JsonResponse({'error': str(e)}, status=500)


@extend_schema(
    summary="高级指令式语音合成 (直接)",
    description="基于参考音频的高级指令式语音合成，直接返回结果",
    tags=["直接推理API"]
)
@enhanced_api_compatibility
@rate_limit(max_requests=15, window_seconds=60)
@require_quota(quota_cost=3)
@monitor_performance(threshold_seconds=20.0)
@csrf_exempt
@require_http_methods(["POST"])
def direct_inference_instruct2(request):
    """高级指令式语音合成 - 直接返回结果"""
    try:
        # 获取CosyVoice引擎
        engine = get_cosyvoice_engine()

        if not engine.is_available():
            if not engine.load_model():
                return JsonResponse({
                    'error': 'CosyVoice模型未加载，请检查模型文件是否存在'
                }, status=503)

        # 获取参数
        tts_text = request.POST.get('tts_text', '')
        instruct_text = request.POST.get('instruct_text', '')
        prompt_wav = request.FILES.get('prompt_wav')

        if not all([tts_text, instruct_text, prompt_wav]):
            return JsonResponse({
                'error': '缺少必需参数: tts_text, instruct_text, prompt_wav'
            }, status=400)

        # 检查用户配额
        user = request.user if request.user.is_authenticated else None
        if user and not user.can_use_quota():
            return JsonResponse({
                'error': '配额不足',
                'quota_used': user.quota_used,
                'quota_limit': user.quota_limit
            }, status=429)

        # 创建任务记录
        task = Task.objects.create(
            user=user,
            task_type=TaskType.INSTRUCT2,
            request_data={
                'tts_text': tts_text,
                'instruct_text': instruct_text,
                'prompt_wav_filename': prompt_wav.name,
            },
            status=TaskStatus.PROCESSING,
            client_ip=get_client_ip(request),
            user_agent=request.META.get('HTTP_USER_AGENT', ''),
        )

        try:
            # 保存临时音频文件
            with tempfile.NamedTemporaryFile(delete=False, suffix='.wav') as temp_file:
                for chunk in prompt_wav.chunks():
                    temp_file.write(chunk)
                temp_wav_path = temp_file.name

            try:
                # 执行推理
                result = engine.inference_instruct2(tts_text, instruct_text, temp_wav_path)

                # 保存音频文件
                audio_file = save_audio_to_file_model(result['audio_info'], task, user)

                # 使用配额
                if user:
                    user.use_quota(3)  # 高级指令式推理消耗更多配额

                logger.info(f"高级指令式推理成功: {task.id}, 文本: '{tts_text}', 指令: '{instruct_text}'")

                return JsonResponse({
                    'success': True,
                    'message': '高级指令式推理成功',
                    'task_id': str(task.id),
                    'audio_info': {
                        'filename': audio_file.filename,
                        'duration': audio_file.duration,
                        'file_size': audio_file.file_size,
                        'sample_rate': audio_file.sample_rate,
                        'download_url': f"/media/generated_audio/{audio_file.filename}",
                        'file_id': str(audio_file.id)
                    },
                    'text': tts_text,
                    'instruction': instruct_text,
                    'reference_audio': prompt_wav.name
                })

            finally:
                # 清理临时文件
                if os.path.exists(temp_wav_path):
                    os.unlink(temp_wav_path)

        except Exception as e:
            # 更新任务状态为失败
            task.status = TaskStatus.FAILED
            task.error_message = str(e)
            task.save()
            raise

    except Exception as e:
        logger.error(f"高级指令式推理失败: {e}")
        return JsonResponse({'error': str(e)}, status=500)


@extend_schema(
    summary="CosyVoice引擎状态",
    description="获取CosyVoice引擎的当前状态",
    tags=["直接推理API"]
)
@api_view(['GET'])
@permission_classes([AllowAny])
def engine_status(request):
    """获取CosyVoice引擎状态"""
    try:
        engine = get_cosyvoice_engine()
        
        return JsonResponse({
            'engine_available': engine.is_available(),
            'model_dir': str(engine.model_dir),
            'output_dir': str(engine.output_dir),
            'model_dir_exists': os.path.exists(engine.model_dir),
            'output_dir_exists': os.path.exists(engine.output_dir),
            'sample_rate': engine.sample_rate,
        })
        
    except Exception as e:
        logger.error(f"获取引擎状态失败: {e}")
        return JsonResponse({'error': str(e)}, status=500)
