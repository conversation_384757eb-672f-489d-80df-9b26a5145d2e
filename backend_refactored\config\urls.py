"""
CosyVoice API任务管理系统 URL配置
"""
from django.contrib import admin
from django.urls import path, include
from django.conf import settings
from django.conf.urls.static import static
from drf_spectacular.views import SpectacularAPIView, SpectacularSwaggerView, SpectacularRedocView
from apps.common.api_views import api_root, custom_swagger_ui

urlpatterns = [
    # 管理后台仪表板 (必须在admin/之前)
    path('admin/dashboard/', include('apps.common.admin_urls')),

    # TTS生成器 (必须在admin/之前)
    path('admin/tts-generator/', include('apps.tts_generator.urls')),

    # Django管理后台
    path('admin/', admin.site.urls),
    
    # API文档
    path('api/schema/', SpectacularAPIView.as_view(), name='schema'),
    path('docs/', SpectacularSwaggerView.as_view(url_name='schema'), name='swagger-ui'),
    path('redoc/', SpectacularRedocView.as_view(url_name='schema'), name='redoc'),
    
    # API路由
    path('api/', include([
        # API根视图
        path('', api_root, name='api-root'),

        # API文档
        path('docs/', custom_swagger_ui, name='api-docs'),  # 自定义Swagger UI
        path('docs/swagger/', SpectacularSwaggerView.as_view(url_name='schema'), name='swagger-ui'),  # 原始Swagger
        path('schema/', SpectacularAPIView.as_view(), name='api-schema'),

        # 应用API
        path('auth/', include('apps.authentication.urls')),
        path('users/', include('apps.users.urls')),
        path('tasks/', include('apps.tasks.urls')),
        path('files/', include('apps.files.urls')),
    ])),

    # 兼容v1路径
    path('api/v1/', include([
        path('auth/', include('apps.authentication.urls')),
        path('users/', include('apps.users.urls')),
        path('tasks/', include('apps.tasks.urls')),
        path('files/', include('apps.files.urls')),
    ])),
    
    # 兼容性API路由 (保持原有接口不变)
    path('', include('apps.common.urls')),
]

# 开发环境静态文件服务
if settings.DEBUG:
    urlpatterns += static(settings.MEDIA_URL, document_root=settings.MEDIA_ROOT)
    urlpatterns += static(settings.STATIC_URL, document_root=settings.STATIC_ROOT)
    
    # 开发工具
    if 'debug_toolbar' in settings.INSTALLED_APPS:
        import debug_toolbar
        urlpatterns = [
            path('__debug__/', include(debug_toolbar.urls)),
        ] + urlpatterns
