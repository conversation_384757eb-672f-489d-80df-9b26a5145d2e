# Generated by Django 4.2.7 on 2025-07-25 01:06

from django.conf import settings
from django.db import migrations, models
import django.db.models.deletion
import uuid


class Migration(migrations.Migration):
    initial = True

    dependencies = [
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
    ]

    operations = [
        migrations.CreateModel(
            name="Speaker",
            fields=[
                (
                    "id",
                    models.UUIDField(
                        default=uuid.uuid4,
                        editable=False,
                        primary_key=True,
                        serialize=False,
                    ),
                ),
                ("name", models.CharField(max_length=100, verbose_name="说话人名称")),
                (
                    "speaker_id",
                    models.CharField(max_length=50, unique=True, verbose_name="说话人ID"),
                ),
                (
                    "language",
                    models.CharField(default="zh", max_length=20, verbose_name="语言"),
                ),
                (
                    "gender",
                    models.CharField(
                        choices=[("male", "男性"), ("female", "女性"), ("neutral", "中性")],
                        default="female",
                        max_length=10,
                        verbose_name="性别",
                    ),
                ),
                ("description", models.TextField(blank=True, verbose_name="描述")),
                ("is_active", models.BooleanField(default=True, verbose_name="是否启用")),
                (
                    "created_at",
                    models.DateTimeField(auto_now_add=True, verbose_name="创建时间"),
                ),
                (
                    "updated_at",
                    models.DateTimeField(auto_now=True, verbose_name="更新时间"),
                ),
            ],
            options={
                "verbose_name": "说话人",
                "verbose_name_plural": "说话人管理",
                "ordering": ["name"],
            },
        ),
        migrations.CreateModel(
            name="TTSTemplate",
            fields=[
                (
                    "id",
                    models.UUIDField(
                        default=uuid.uuid4,
                        editable=False,
                        primary_key=True,
                        serialize=False,
                    ),
                ),
                ("name", models.CharField(max_length=100, verbose_name="模板名称")),
                (
                    "template_type",
                    models.CharField(
                        choices=[
                            ("sft", "SFT模式"),
                            ("zero_shot", "零样本克隆"),
                            ("cross_lingual", "跨语种复刻"),
                            ("instruct", "指令控制"),
                        ],
                        max_length=20,
                        verbose_name="模板类型",
                    ),
                ),
                (
                    "text_template",
                    models.TextField(
                        help_text="支持变量替换，如 {name}, {content}", verbose_name="文本模板"
                    ),
                ),
                (
                    "instruct_text",
                    models.TextField(
                        blank=True, help_text="仅指令控制模式使用", verbose_name="指令文本"
                    ),
                ),
                ("is_active", models.BooleanField(default=True, verbose_name="是否启用")),
                (
                    "created_at",
                    models.DateTimeField(auto_now_add=True, verbose_name="创建时间"),
                ),
                (
                    "updated_at",
                    models.DateTimeField(auto_now=True, verbose_name="更新时间"),
                ),
                (
                    "created_by",
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.CASCADE,
                        to=settings.AUTH_USER_MODEL,
                        verbose_name="创建者",
                    ),
                ),
                (
                    "speaker",
                    models.ForeignKey(
                        blank=True,
                        null=True,
                        on_delete=django.db.models.deletion.CASCADE,
                        to="tts_generator.speaker",
                        verbose_name="默认说话人",
                    ),
                ),
            ],
            options={
                "verbose_name": "TTS模板",
                "verbose_name_plural": "TTS模板管理",
                "ordering": ["-created_at"],
            },
        ),
        migrations.CreateModel(
            name="QuickGeneration",
            fields=[
                (
                    "id",
                    models.UUIDField(
                        default=uuid.uuid4,
                        editable=False,
                        primary_key=True,
                        serialize=False,
                    ),
                ),
                (
                    "generation_type",
                    models.CharField(
                        choices=[
                            ("sft", "SFT模式"),
                            ("zero_shot", "零样本克隆"),
                            ("cross_lingual", "跨语种复刻"),
                            ("instruct", "指令控制"),
                        ],
                        max_length=20,
                        verbose_name="生成类型",
                    ),
                ),
                ("text_content", models.TextField(verbose_name="文本内容")),
                ("instruct_text", models.TextField(blank=True, verbose_name="指令文本")),
                (
                    "reference_audio",
                    models.FileField(
                        blank=True,
                        null=True,
                        upload_to="reference_audio/",
                        verbose_name="参考音频",
                    ),
                ),
                ("reference_text", models.TextField(blank=True, verbose_name="参考文本")),
                (
                    "task_id",
                    models.CharField(blank=True, max_length=100, verbose_name="任务ID"),
                ),
                (
                    "status",
                    models.CharField(
                        choices=[
                            ("pending", "等待中"),
                            ("processing", "处理中"),
                            ("completed", "已完成"),
                            ("failed", "失败"),
                        ],
                        default="pending",
                        max_length=20,
                        verbose_name="状态",
                    ),
                ),
                (
                    "result_audio",
                    models.FileField(
                        blank=True,
                        null=True,
                        upload_to="generated_audio/",
                        verbose_name="生成音频",
                    ),
                ),
                ("error_message", models.TextField(blank=True, verbose_name="错误信息")),
                (
                    "created_at",
                    models.DateTimeField(auto_now_add=True, verbose_name="创建时间"),
                ),
                (
                    "started_at",
                    models.DateTimeField(blank=True, null=True, verbose_name="开始时间"),
                ),
                (
                    "completed_at",
                    models.DateTimeField(blank=True, null=True, verbose_name="完成时间"),
                ),
                (
                    "processing_time",
                    models.FloatField(blank=True, null=True, verbose_name="处理时长(秒)"),
                ),
                (
                    "audio_duration",
                    models.FloatField(blank=True, null=True, verbose_name="音频时长(秒)"),
                ),
                (
                    "speaker",
                    models.ForeignKey(
                        blank=True,
                        null=True,
                        on_delete=django.db.models.deletion.SET_NULL,
                        to="tts_generator.speaker",
                        verbose_name="说话人",
                    ),
                ),
                (
                    "user",
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.CASCADE,
                        to=settings.AUTH_USER_MODEL,
                        verbose_name="用户",
                    ),
                ),
            ],
            options={
                "verbose_name": "快速生成",
                "verbose_name_plural": "快速生成记录",
                "ordering": ["-created_at"],
            },
        ),
        migrations.CreateModel(
            name="APITestRecord",
            fields=[
                (
                    "id",
                    models.UUIDField(
                        default=uuid.uuid4,
                        editable=False,
                        primary_key=True,
                        serialize=False,
                    ),
                ),
                (
                    "api_endpoint",
                    models.CharField(max_length=200, verbose_name="API端点"),
                ),
                (
                    "method",
                    models.CharField(
                        choices=[
                            ("GET", "GET"),
                            ("POST", "POST"),
                            ("PUT", "PUT"),
                            ("DELETE", "DELETE"),
                        ],
                        max_length=10,
                        verbose_name="请求方法",
                    ),
                ),
                ("request_data", models.JSONField(default=dict, verbose_name="请求数据")),
                (
                    "response_status",
                    models.IntegerField(blank=True, null=True, verbose_name="响应状态码"),
                ),
                ("response_data", models.JSONField(default=dict, verbose_name="响应数据")),
                (
                    "response_time",
                    models.FloatField(blank=True, null=True, verbose_name="响应时间(毫秒)"),
                ),
                ("is_success", models.BooleanField(default=False, verbose_name="是否成功")),
                ("error_message", models.TextField(blank=True, verbose_name="错误信息")),
                (
                    "created_at",
                    models.DateTimeField(auto_now_add=True, verbose_name="测试时间"),
                ),
                (
                    "user",
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.CASCADE,
                        to=settings.AUTH_USER_MODEL,
                        verbose_name="测试用户",
                    ),
                ),
            ],
            options={
                "verbose_name": "API测试记录",
                "verbose_name_plural": "API测试记录",
                "ordering": ["-created_at"],
            },
        ),
    ]
