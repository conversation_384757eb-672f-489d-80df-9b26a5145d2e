"""
TTS生成器应用配置
"""
from django.apps import AppConfig


class TtsGeneratorConfig(AppConfig):
    default_auto_field = 'django.db.models.BigAutoField'
    name = 'apps.tts_generator'
    verbose_name = 'TTS生成器'

    def ready(self):
        """应用就绪时的初始化"""
        try:
            # 初始化默认说话人
            self.init_default_speakers()
        except Exception as e:
            print(f"TTS生成器初始化失败: {e}")

    def init_default_speakers(self):
        """初始化默认说话人"""
        from .models import Speaker
        
        # 默认说话人列表
        default_speakers = [
            {'name': '中文女声', 'speaker_id': '中文女', 'language': 'zh', 'gender': 'female'},
            {'name': '中文男声', 'speaker_id': '中文男', 'language': 'zh', 'gender': 'male'},
            {'name': '英文女声', 'speaker_id': '英文女', 'language': 'en', 'gender': 'female'},
            {'name': '英文男声', 'speaker_id': '英文男', 'language': 'en', 'gender': 'male'},
            {'name': '日语女声', 'speaker_id': '日语女', 'language': 'ja', 'gender': 'female'},
            {'name': '日语男声', 'speaker_id': '日语男', 'language': 'ja', 'gender': 'male'},
            {'name': '韩语女声', 'speaker_id': '韩语女', 'language': 'ko', 'gender': 'female'},
            {'name': '韩语男声', 'speaker_id': '韩语男', 'language': 'ko', 'gender': 'male'},
            {'name': '粤语女声', 'speaker_id': '粤语女', 'language': 'yue', 'gender': 'female'},
            {'name': '粤语男声', 'speaker_id': '粤语男', 'language': 'yue', 'gender': 'male'},
        ]
        
        # 创建不存在的说话人
        for speaker_data in default_speakers:
            Speaker.objects.get_or_create(
                speaker_id=speaker_data['speaker_id'],
                defaults=speaker_data
            )
