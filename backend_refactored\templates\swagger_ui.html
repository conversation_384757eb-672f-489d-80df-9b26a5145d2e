<!DOCTYPE html>
<html>
<head>
    <title>CosyVoice API Documentation</title>
    <meta charset="utf-8"/>
    <meta name="viewport" content="width=device-width, initial-scale=1">
    <style>
        body {
            margin: 0;
            padding: 20px;
            font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, sans-serif;
            background-color: #fafafa;
        }
        .container {
            max-width: 1200px;
            margin: 0 auto;
            background: white;
            border-radius: 8px;
            box-shadow: 0 2px 8px rgba(0,0,0,0.1);
            padding: 30px;
        }
        h1 {
            color: #333;
            border-bottom: 2px solid #4CAF50;
            padding-bottom: 10px;
        }
        .api-section {
            margin: 30px 0;
            padding: 20px;
            border: 1px solid #ddd;
            border-radius: 6px;
            background: #f9f9f9;
        }
        .api-endpoint {
            margin: 15px 0;
            padding: 15px;
            background: white;
            border-radius: 4px;
            border-left: 4px solid #4CAF50;
        }
        .method {
            display: inline-block;
            padding: 4px 8px;
            border-radius: 4px;
            color: white;
            font-weight: bold;
            margin-right: 10px;
        }
        .get { background-color: #61affe; }
        .post { background-color: #49cc90; }
        .put { background-color: #fca130; }
        .delete { background-color: #f93e3e; }
        .url {
            font-family: monospace;
            background: #f5f5f5;
            padding: 2px 6px;
            border-radius: 3px;
        }
        .description {
            margin-top: 10px;
            color: #666;
        }
        .test-form {
            margin-top: 15px;
            padding: 15px;
            background: #f0f8ff;
            border-radius: 4px;
        }
        .test-button {
            background: #4CAF50;
            color: white;
            border: none;
            padding: 8px 16px;
            border-radius: 4px;
            cursor: pointer;
        }
        .test-button:hover {
            background: #45a049;
        }
        textarea, input {
            width: 100%;
            padding: 8px;
            margin: 5px 0;
            border: 1px solid #ddd;
            border-radius: 4px;
        }
        .response {
            margin-top: 10px;
            padding: 10px;
            background: #f5f5f5;
            border-radius: 4px;
            font-family: monospace;
            white-space: pre-wrap;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🎵 CosyVoice API Documentation</h1>
        <p>CosyVoice语音合成API任务管理系统 - 版本 2.0.0</p>
        
        <div class="api-section">
            <h2>🔍 系统监控</h2>
            
            <div class="api-endpoint">
                <span class="method get">GET</span>
                <span class="url">/health/</span>
                <div class="description">系统健康检查</div>
                <div class="test-form">
                    <button class="test-button" onclick="testAPI('/health/', 'GET', 'response-health')">测试接口</button>
                    <div id="response-health" class="response" style="display:none;"></div>
                </div>
            </div>
            
            <div class="api-endpoint">
                <span class="method get">GET</span>
                <span class="url">/api/</span>
                <div class="description">API根路径信息</div>
                <div class="test-form">
                    <button class="test-button" onclick="testAPI('/api/', 'GET', 'response-api')">测试接口</button>
                    <div id="response-api" class="response" style="display:none;"></div>
                </div>
            </div>
        </div>

        <div class="api-section">
            <h2>🎵 语音合成 (兼容性API)</h2>

            <div class="api-endpoint">
                <span class="method post">POST</span>
                <span class="url">/inference_sft</span>
                <div class="description">SFT语音合成</div>
                <div class="test-form">
                    <label>请求参数 (JSON):</label>
                    <textarea id="sft-params" rows="4">{"tts_text": "你好，这是一个测试语音合成。", "spk_id": "中文女"}</textarea>
                    <button class="test-button" onclick="testAPIWithBody('/inference_sft', 'POST', 'sft-params', 'response-sft')">测试接口</button>
                    <div id="response-sft" class="response" style="display:none;"></div>
                </div>
            </div>

            <div class="api-endpoint">
                <span class="method post">POST</span>
                <span class="url">/inference_zero_shot</span>
                <div class="description">零样本语音合成</div>
                <div class="test-form">
                    <label>请求参数 (JSON):</label>
                    <textarea id="zero-params" rows="4">{"tts_text": "测试零样本语音合成", "prompt_text": "参考音频对应的文本"}</textarea>
                    <button class="test-button" onclick="testAPIWithBody('/inference_zero_shot', 'POST', 'zero-params', 'response-zero')">测试接口</button>
                    <div id="response-zero" class="response" style="display:none;"></div>
                </div>
            </div>

            <div class="api-endpoint">
                <span class="method post">POST</span>
                <span class="url">/inference_cross_lingual</span>
                <div class="description">跨语言语音合成</div>
                <div class="test-form">
                    <label>请求参数 (JSON):</label>
                    <textarea id="cross-params" rows="4">{"tts_text": "Cross-lingual speech synthesis test"}</textarea>
                    <button class="test-button" onclick="testAPIWithBody('/inference_cross_lingual', 'POST', 'cross-params', 'response-cross')">测试接口</button>
                    <div id="response-cross" class="response" style="display:none;"></div>
                </div>
            </div>

            <div class="api-endpoint">
                <span class="method post">POST</span>
                <span class="url">/inference_instruct</span>
                <div class="description">指令式语音合成</div>
                <div class="test-form">
                    <label>请求参数 (JSON):</label>
                    <textarea id="instruct-params" rows="4">{"tts_text": "指令式语音合成测试", "spk_id": "中文女", "instruct_text": "请用温柔的语调朗读"}</textarea>
                    <button class="test-button" onclick="testAPIWithBody('/inference_instruct', 'POST', 'instruct-params', 'response-instruct')">测试接口</button>
                    <div id="response-instruct" class="response" style="display:none;"></div>
                </div>
            </div>

            <div class="api-endpoint">
                <span class="method post">POST</span>
                <span class="url">/inference_instruct2</span>
                <div class="description">指令式语音合成 v2</div>
                <div class="test-form">
                    <label>请求参数 (JSON):</label>
                    <textarea id="instruct2-params" rows="4">{"tts_text": "指令式语音合成v2测试", "spk_id": "中文女", "instruct_text": "请用激动的语调朗读"}</textarea>
                    <button class="test-button" onclick="testAPIWithBody('/inference_instruct2', 'POST', 'instruct2-params', 'response-instruct2')">测试接口</button>
                    <div id="response-instruct2" class="response" style="display:none;"></div>
                </div>
            </div>
        </div>

        <div class="api-section">
            <h2>⚡ 直接推理API (集成引擎)</h2>

            <div class="api-endpoint">
                <span class="method post">POST</span>
                <span class="url">/direct/inference_sft</span>
                <div class="description">SFT语音合成 (直接返回结果)</div>
                <div class="test-form">
                    <label>请求参数 (JSON):</label>
                    <textarea id="direct-sft-params" rows="4">{"tts_text": "你好，这是直接推理测试。", "spk_id": "中文女"}</textarea>
                    <button class="test-button" onclick="testAPIWithBody('/direct/inference_sft', 'POST', 'direct-sft-params', 'response-direct-sft')">测试接口</button>
                    <div id="response-direct-sft" class="response" style="display:none;"></div>
                </div>
            </div>

            <div class="api-endpoint">
                <span class="method post">POST</span>
                <span class="url">/direct/inference_zero_shot</span>
                <div class="description">零样本语音合成 (直接返回结果)</div>
                <div class="test-form">
                    <label>请求参数 (需要上传音频文件):</label>
                    <textarea id="direct-zero-params" rows="4">{"tts_text": "直接零样本语音合成测试", "prompt_text": "参考音频对应的文本"}</textarea>
                    <button class="test-button" onclick="testAPIWithBody('/direct/inference_zero_shot', 'POST', 'direct-zero-params', 'response-direct-zero')">测试接口</button>
                    <div id="response-direct-zero" class="response" style="display:none;"></div>
                </div>
            </div>

            <div class="api-endpoint">
                <span class="method get">GET</span>
                <span class="url">/direct/list_speakers</span>
                <div class="description">获取可用说话人列表</div>
                <div class="test-form">
                    <button class="test-button" onclick="testAPIWithBody('/direct/list_speakers', 'GET', null, 'response-speakers')">测试接口</button>
                    <div id="response-speakers" class="response" style="display:none;"></div>
                </div>
            </div>

            <div class="api-endpoint">
                <span class="method post">POST</span>
                <span class="url">/direct/inference_cross_lingual</span>
                <div class="description">跨语言语音合成 (直接返回结果)</div>
                <div class="test-form">
                    <label>请求参数 (需要上传音频文件):</label>
                    <textarea id="direct-cross-params" rows="4">{"tts_text": "Hello, this is cross-lingual synthesis test."}</textarea>
                    <button class="test-button" onclick="testAPIWithBody('/direct/inference_cross_lingual', 'POST', 'direct-cross-params', 'response-direct-cross')">测试接口</button>
                    <div id="response-direct-cross" class="response" style="display:none;"></div>
                </div>
            </div>

            <div class="api-endpoint">
                <span class="method post">POST</span>
                <span class="url">/direct/inference_instruct</span>
                <div class="description">指令式语音合成 (直接返回结果)</div>
                <div class="test-form">
                    <label>请求参数 (JSON):</label>
                    <textarea id="direct-instruct-params" rows="4">{"tts_text": "今天天气很好。", "spk_id": "中文女", "instruct_text": "用温柔的语气说"}</textarea>
                    <button class="test-button" onclick="testAPIWithBody('/direct/inference_instruct', 'POST', 'direct-instruct-params', 'response-direct-instruct')">测试接口</button>
                    <div id="response-direct-instruct" class="response" style="display:none;"></div>
                </div>
            </div>

            <div class="api-endpoint">
                <span class="method post">POST</span>
                <span class="url">/direct/inference_instruct2</span>
                <div class="description">高级指令式语音合成 (直接返回结果)</div>
                <div class="test-form">
                    <label>请求参数 (需要上传音频文件):</label>
                    <textarea id="direct-instruct2-params" rows="4">{"tts_text": "这是高级指令式合成测试。", "instruct_text": "用激动的语气说"}</textarea>
                    <button class="test-button" onclick="testAPIWithBody('/direct/inference_instruct2', 'POST', 'direct-instruct2-params', 'response-direct-instruct2')">测试接口</button>
                    <div id="response-direct-instruct2" class="response" style="display:none;"></div>
                </div>
            </div>

            <div class="api-endpoint">
                <span class="method get">GET</span>
                <span class="url">/direct/engine_status</span>
                <div class="description">CosyVoice引擎状态</div>
                <div class="test-form">
                    <button class="test-button" onclick="testAPIWithBody('/direct/engine_status', 'GET', null, 'response-engine-status')">测试接口</button>
                    <div id="response-engine-status" class="response" style="display:none;"></div>
                </div>
            </div>
        </div>

        <div class="api-section">
            <h2>👤 用户认证</h2>

            <div class="api-endpoint">
                <span class="method post">POST</span>
                <span class="url">/api/auth/register/</span>
                <div class="description">用户注册</div>
                <div class="test-form">
                    <label>请求参数 (JSON):</label>
                    <textarea id="register-params" rows="6">{"username": "testuser", "email": "<EMAIL>", "password": "testpass123", "password_confirm": "testpass123"}</textarea>
                    <button class="test-button" onclick="testAPIWithBody('/api/auth/register/', 'POST', 'register-params', 'response-register')">测试接口</button>
                    <div id="response-register" class="response" style="display:none;"></div>
                </div>
            </div>

            <div class="api-endpoint">
                <span class="method post">POST</span>
                <span class="url">/api/auth/login/</span>
                <div class="description">用户登录</div>
                <div class="test-form">
                    <label>请求参数 (JSON):</label>
                    <textarea id="login-params" rows="4">{"email": "<EMAIL>", "password": "testpass123"}</textarea>
                    <button class="test-button" onclick="testAPIWithBody('/api/auth/login/', 'POST', 'login-params', 'response-login')">测试接口</button>
                    <div id="response-login" class="response" style="display:none;"></div>
                </div>
            </div>

            <div class="api-endpoint">
                <span class="method post">POST</span>
                <span class="url">/api/auth/token/refresh/</span>
                <div class="description">刷新Token</div>
                <div class="test-form">
                    <label>请求参数 (JSON):</label>
                    <textarea id="refresh-params" rows="3">{"refresh": "your_refresh_token_here"}</textarea>
                    <button class="test-button" onclick="testAPIWithBody('/api/auth/token/refresh/', 'POST', 'refresh-params', 'response-refresh')">测试接口</button>
                    <div id="response-refresh" class="response" style="display:none;"></div>
                </div>
            </div>

            <div class="api-endpoint">
                <span class="method get">GET</span>
                <span class="url">/api/auth/api-keys/</span>
                <div class="description">获取API密钥列表 (需要认证)</div>
                <div class="test-form">
                    <label>Authorization Header:</label>
                    <input type="text" id="auth-header" placeholder="Bearer your_access_token_here" />
                    <button class="test-button" onclick="testAPIWithAuth('/api/auth/api-keys/', 'GET', 'auth-header', 'response-apikeys')">测试接口</button>
                    <div id="response-apikeys" class="response" style="display:none;"></div>
                </div>
            </div>
        </div>

        <div class="api-section">
            <h2>📋 任务管理</h2>

            <div class="api-endpoint">
                <span class="method get">GET</span>
                <span class="url">/api/tasks/</span>
                <div class="description">获取任务列表 (需要认证)</div>
                <div class="test-form">
                    <label>Authorization Header:</label>
                    <input type="text" id="tasks-auth" placeholder="Bearer your_access_token_here" />
                    <button class="test-button" onclick="testAPIWithAuth('/api/tasks/', 'GET', 'tasks-auth', 'response-tasks')">测试接口</button>
                    <div id="response-tasks" class="response" style="display:none;"></div>
                </div>
            </div>

            <div class="api-endpoint">
                <span class="method post">POST</span>
                <span class="url">/api/tasks/create/</span>
                <div class="description">创建新任务 (需要认证)</div>
                <div class="test-form">
                    <label>Authorization Header:</label>
                    <input type="text" id="create-task-auth" placeholder="Bearer your_access_token_here" />
                    <label>请求参数 (JSON):</label>
                    <textarea id="create-task-params" rows="6">{"task_type": "sft", "request_data": {"tts_text": "测试任务", "spk_id": "中文女"}, "priority": "normal"}</textarea>
                    <button class="test-button" onclick="testAPIWithAuthAndBody('/api/tasks/create/', 'POST', 'create-task-auth', 'create-task-params', 'response-create-task')">测试接口</button>
                    <div id="response-create-task" class="response" style="display:none;"></div>
                </div>
            </div>

            <div class="api-endpoint">
                <span class="method get">GET</span>
                <span class="url">/api/tasks/stats/</span>
                <div class="description">获取任务统计 (需要认证)</div>
                <div class="test-form">
                    <label>Authorization Header:</label>
                    <input type="text" id="task-stats-auth" placeholder="Bearer your_access_token_here" />
                    <button class="test-button" onclick="testAPIWithAuth('/api/tasks/stats/', 'GET', 'task-stats-auth', 'response-task-stats')">测试接口</button>
                    <div id="response-task-stats" class="response" style="display:none;"></div>
                </div>
            </div>
        </div>

        <div class="api-section">
            <h2>📁 文件管理</h2>

            <div class="api-endpoint">
                <span class="method get">GET</span>
                <span class="url">/api/files/</span>
                <div class="description">获取文件列表 (需要认证)</div>
                <div class="test-form">
                    <label>Authorization Header:</label>
                    <input type="text" id="files-auth" placeholder="Bearer your_access_token_here" />
                    <button class="test-button" onclick="testAPIWithAuth('/api/files/', 'GET', 'files-auth', 'response-files')">测试接口</button>
                    <div id="response-files" class="response" style="display:none;"></div>
                </div>
            </div>

            <div class="api-endpoint">
                <span class="method get">GET</span>
                <span class="url">/api/files/stats/</span>
                <div class="description">获取文件统计 (需要认证)</div>
                <div class="test-form">
                    <label>Authorization Header:</label>
                    <input type="text" id="file-stats-auth" placeholder="Bearer your_access_token_here" />
                    <button class="test-button" onclick="testAPIWithAuth('/api/files/stats/', 'GET', 'file-stats-auth', 'response-file-stats')">测试接口</button>
                    <div id="response-file-stats" class="response" style="display:none;"></div>
                </div>
            </div>
        </div>

        <div class="api-section">
            <h2>👥 用户管理</h2>

            <div class="api-endpoint">
                <span class="method get">GET</span>
                <span class="url">/api/users/profile/</span>
                <div class="description">获取用户资料 (需要认证)</div>
                <div class="test-form">
                    <label>Authorization Header:</label>
                    <input type="text" id="profile-auth" placeholder="Bearer your_access_token_here" />
                    <button class="test-button" onclick="testAPIWithAuth('/api/users/profile/', 'GET', 'profile-auth', 'response-profile')">测试接口</button>
                    <div id="response-profile" class="response" style="display:none;"></div>
                </div>
            </div>

            <div class="api-endpoint">
                <span class="method get">GET</span>
                <span class="url">/api/users/stats/</span>
                <div class="description">获取用户统计 (需要认证)</div>
                <div class="test-form">
                    <label>Authorization Header:</label>
                    <input type="text" id="user-stats-auth" placeholder="Bearer your_access_token_here" />
                    <button class="test-button" onclick="testAPIWithAuth('/api/users/stats/', 'GET', 'user-stats-auth', 'response-user-stats')">测试接口</button>
                    <div id="response-user-stats" class="response" style="display:none;"></div>
                </div>
            </div>
        </div>

        <div class="api-section">
            <h2>🔗 完整API文档</h2>
            <p>
                <a href="/api/schema/" target="_blank">📄 OpenAPI Schema</a> |
                <a href="/redoc/" target="_blank">📚 ReDoc文档</a> |
                <a href="/admin/" target="_blank">🔧 管理界面</a>
            </p>
        </div>
    </div>

    <script>
        // JSON格式化函数
        function formatJSON(jsonString) {
            try {
                const parsed = JSON.parse(jsonString);
                return JSON.stringify(parsed, null, 2);
            } catch (e) {
                return jsonString; // 如果不是有效JSON，返回原始字符串
            }
        }

        // 基础API测试函数
        async function testAPI(url, method, responseId) {
            const responseDiv = document.getElementById(responseId || 'response-' + url.split('/').filter(x => x).join('-'));

            try {
                if (responseDiv) {
                    responseDiv.style.display = 'block';
                    responseDiv.textContent = '请求中...';
                }

                const response = await fetch(url, {
                    method: method,
                    headers: {
                        'Content-Type': 'application/json',
                    }
                });

                const data = await response.text();
                const formattedData = formatJSON(data);

                if (responseDiv) {
                    responseDiv.textContent = `状态码: ${response.status}\n\n响应:\n${formattedData}`;
                }
            } catch (error) {
                if (responseDiv) {
                    responseDiv.textContent = `错误: ${error.message}`;
                }
            }
        }

        // 带请求体的API测试函数
        async function testAPIWithBody(url, method, paramsId, responseId) {
            const responseDiv = document.getElementById(responseId);
            const paramsTextarea = document.getElementById(paramsId);

            if (!responseDiv || !paramsTextarea) {
                console.error('找不到响应区域或参数输入框');
                return;
            }

            try {
                responseDiv.style.display = 'block';
                responseDiv.textContent = '请求中...';

                let requestOptions = {
                    method: method
                };

                // 判断是否为兼容性API（inference_*）
                if (url.includes('/inference_')) {
                    // 兼容性API使用form data
                    const params = JSON.parse(paramsTextarea.value);
                    const formData = new FormData();
                    for (const [key, value] of Object.entries(params)) {
                        formData.append(key, value);
                    }
                    requestOptions.body = formData;
                } else {
                    // 新API使用JSON
                    requestOptions.headers = {
                        'Content-Type': 'application/json',
                    };
                    requestOptions.body = paramsTextarea.value;
                }

                const response = await fetch(url, requestOptions);

                const data = await response.text();
                const formattedData = formatJSON(data);
                responseDiv.textContent = `状态码: ${response.status}\n\n响应:\n${formattedData}`;
            } catch (error) {
                responseDiv.textContent = `错误: ${error.message}`;
            }
        }

        // 带认证的API测试函数
        async function testAPIWithAuth(url, method, authId, responseId) {
            const responseDiv = document.getElementById(responseId);
            const authInput = document.getElementById(authId);

            if (!responseDiv || !authInput) {
                console.error('找不到响应区域或认证输入框');
                return;
            }

            try {
                responseDiv.style.display = 'block';
                responseDiv.textContent = '请求中...';

                const headers = {
                    'Content-Type': 'application/json',
                };

                if (authInput.value.trim()) {
                    headers['Authorization'] = authInput.value.trim();
                }

                const response = await fetch(url, {
                    method: method,
                    headers: headers
                });

                const data = await response.text();
                const formattedData = formatJSON(data);
                responseDiv.textContent = `状态码: ${response.status}\n\n响应:\n${formattedData}`;
            } catch (error) {
                responseDiv.textContent = `错误: ${error.message}`;
            }
        }

        // 带认证和请求体的API测试函数
        async function testAPIWithAuthAndBody(url, method, authId, paramsId, responseId) {
            const responseDiv = document.getElementById(responseId);
            const authInput = document.getElementById(authId);
            const paramsTextarea = document.getElementById(paramsId);

            if (!responseDiv || !authInput || !paramsTextarea) {
                console.error('找不到响应区域、认证输入框或参数输入框');
                return;
            }

            try {
                responseDiv.style.display = 'block';
                responseDiv.textContent = '请求中...';

                const headers = {
                    'Content-Type': 'application/json',
                };

                if (authInput.value.trim()) {
                    headers['Authorization'] = authInput.value.trim();
                }

                const response = await fetch(url, {
                    method: method,
                    headers: headers,
                    body: paramsTextarea.value
                });

                const data = await response.text();
                const formattedData = formatJSON(data);
                responseDiv.textContent = `状态码: ${response.status}\n\n响应:\n${formattedData}`;
            } catch (error) {
                responseDiv.textContent = `错误: ${error.message}`;
            }
        }

        // 页面加载完成后的初始化
        document.addEventListener('DOMContentLoaded', function() {
            console.log('CosyVoice API文档已加载');
        });
    </script>
</body>
</html>
