"""
系统监控管理命令
"""
import time
import signal
import sys
from django.core.management.base import BaseCommand
from apps.common.monitoring import system_monitor
from apps.common.alerts import alert_manager
from apps.common.logging import system_logger


class Command(BaseCommand):
    help = '启动系统监控服务'
    
    def __init__(self):
        super().__init__()
        self.running = True
    
    def add_arguments(self, parser):
        parser.add_argument(
            '--interval',
            type=int,
            default=60,
            help='监控间隔时间（秒），默认60秒'
        )
        
        parser.add_argument(
            '--daemon',
            action='store_true',
            help='以守护进程模式运行'
        )
    
    def handle(self, *args, **options):
        interval = options['interval']
        daemon = options['daemon']
        
        # 设置信号处理器
        signal.signal(signal.SIGINT, self.signal_handler)
        signal.signal(signal.SIGTERM, self.signal_handler)
        
        self.stdout.write(f"启动系统监控服务，监控间隔: {interval}秒")
        system_logger.info(f"系统监控服务启动，间隔: {interval}秒")
        
        if daemon:
            self.stdout.write("以守护进程模式运行...")
        
        try:
            while self.running:
                self.collect_all_metrics()
                
                if not daemon:
                    self.stdout.write(f"监控数据收集完成，等待 {interval} 秒...")
                
                # 分段睡眠，以便能够响应信号
                for _ in range(interval):
                    if not self.running:
                        break
                    time.sleep(1)
                    
        except KeyboardInterrupt:
            self.stdout.write("收到中断信号，正在停止监控服务...")
        except Exception as e:
            self.stdout.write(self.style.ERROR(f"监控服务异常: {e}"))
            system_logger.error(f"监控服务异常: {e}")
        finally:
            self.stdout.write("系统监控服务已停止")
            system_logger.info("系统监控服务已停止")
    
    def collect_all_metrics(self):
        """收集所有监控指标"""
        try:
            # 收集系统指标
            system_monitor.collect_system_metrics()
            
            # 收集数据库指标
            system_monitor.collect_database_metrics()
            
            # 收集Redis指标
            system_monitor.collect_redis_metrics()
            
            # 收集应用指标
            system_monitor.collect_application_metrics()
            
            # 检查告警
            self.check_alerts()
            
        except Exception as e:
            system_logger.error(f"收集监控指标失败: {e}")
    
    def check_alerts(self):
        """检查告警条件"""
        try:
            metrics = system_monitor.get_metrics()
            
            for metric_name, metric_data in metrics.items():
                alert_manager.check_metric_alerts(metric_name, metric_data['value'])
                
        except Exception as e:
            system_logger.error(f"检查告警失败: {e}")
    
    def signal_handler(self, signum, frame):
        """信号处理器"""
        self.stdout.write(f"收到信号 {signum}，正在停止监控服务...")
        self.running = False
