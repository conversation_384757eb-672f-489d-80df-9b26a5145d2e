"""
文件管理URL配置
"""
from django.urls import path
from . import views

urlpatterns = [
    # 音频文件管理
    path('', views.AudioFileListView.as_view(), name='audio_file_list'),
    path('upload/', views.AudioFileUploadView.as_view(), name='audio_file_upload'),
    path('<uuid:pk>/', views.AudioFileDetailView.as_view(), name='audio_file_detail'),

    # 文件分享
    path('shares/', views.FileShareListCreateView.as_view(), name='file_share_list_create'),
    path('shares/<uuid:pk>/', views.FileShareDetailView.as_view(), name='file_share_detail'),
    path('share/<str:share_token>/', views.download_shared_file, name='download_shared_file'),

    # 文件统计和操作
    path('stats/', views.file_stats, name='file_stats'),
    path('batch/', views.batch_file_operation, name='batch_file_operation'),
]
