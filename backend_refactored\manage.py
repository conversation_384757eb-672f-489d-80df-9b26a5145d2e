#!/usr/bin/env python
"""Django's command-line utility for administrative tasks."""
import os
import sys


def main():
    """Run administrative tasks."""
    os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'config.settings.development')

    # 在Django启动前清理可能生成的SQLite文件
    try:
        from cleanup_sqlite import cleanup_sqlite_files
        cleanup_sqlite_files()
    except ImportError:
        pass  # 如果清理脚本不存在，忽略错误
    except Exception:
        pass  # 忽略清理过程中的任何错误

    try:
        from django.core.management import execute_from_command_line
    except ImportError as exc:
        raise ImportError(
            "Couldn't import Django. Are you sure it's installed and "
            "available on your PYTHONPATH environment variable? Did you "
            "forget to activate a virtual environment?"
        ) from exc
    execute_from_command_line(sys.argv)


if __name__ == '__main__':
    main()
