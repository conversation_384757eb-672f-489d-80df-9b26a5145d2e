#!/usr/bin/env python
"""
验证系统中没有SQLite引用的脚本
"""
import os
import sys
import glob
import re
from pathlib import Path

def check_sqlite_references():
    """检查代码中的SQLite引用"""
    base_dir = Path(__file__).resolve().parent
    
    # 要检查的文件类型
    file_patterns = [
        '**/*.py',
        '**/*.md',
        '**/*.txt',
        '**/*.yml',
        '**/*.yaml',
        '**/*.json',
        '**/*.env*'
    ]
    
    # SQLite相关的关键词
    sqlite_keywords = [
        r'sqlite3?',
        r'db\.sqlite3',
        r'USE_SQLITE',
        r'django\.db\.backends\.sqlite3'
    ]
    
    found_references = []
    
    for pattern in file_patterns:
        files = glob.glob(str(base_dir / pattern), recursive=True)
        for file_path in files:
            # 跳过一些不需要检查的文件
            if any(skip in file_path for skip in [
                '__pycache__', '.git', 'node_modules',
                'staticfiles', 'media', 'logs',
                'verify_no_sqlite.py', 'cleanup_sqlite.py',
                'manage.py'  # manage.py中的SQLite引用是用于清理的，属于正常情况
            ]):
                continue
            
            try:
                with open(file_path, 'r', encoding='utf-8', errors='ignore') as f:
                    content = f.read()
                    
                for keyword in sqlite_keywords:
                    matches = re.finditer(keyword, content, re.IGNORECASE)
                    for match in matches:
                        line_num = content[:match.start()].count('\n') + 1
                        line_content = content.split('\n')[line_num - 1].strip()
                        
                        found_references.append({
                            'file': file_path,
                            'line': line_num,
                            'keyword': match.group(),
                            'content': line_content
                        })
            except Exception as e:
                print(f"检查文件失败: {file_path}, 错误: {e}")
    
    return found_references

def check_sqlite_files():
    """检查是否存在SQLite文件"""
    base_dir = Path(__file__).resolve().parent
    
    sqlite_patterns = [
        '*.sqlite3',
        '*.db',
        '**/*.sqlite3',
        '**/*.db'
    ]
    
    found_files = []
    
    for pattern in sqlite_patterns:
        files = glob.glob(str(base_dir / pattern), recursive=True)
        for file_path in files:
            # 跳过一些系统文件
            if any(skip in file_path for skip in ['__pycache__', '.git']):
                continue
            found_files.append(file_path)
    
    return found_files

def main():
    """主函数"""
    print("=== SQLite引用检查报告 ===")
    print()
    
    # 检查代码引用
    print("🔍 检查代码中的SQLite引用...")
    references = check_sqlite_references()
    
    if references:
        print(f"❌ 发现 {len(references)} 个SQLite引用:")
        for ref in references:
            print(f"  📁 {ref['file']}:{ref['line']}")
            print(f"     关键词: {ref['keyword']}")
            print(f"     内容: {ref['content']}")
            print()
    else:
        print("✅ 没有发现SQLite代码引用")
    
    print()
    
    # 检查SQLite文件
    print("🔍 检查SQLite文件...")
    files = check_sqlite_files()
    
    if files:
        print(f"❌ 发现 {len(files)} 个SQLite文件:")
        for file_path in files:
            print(f"  📄 {file_path}")
    else:
        print("✅ 没有发现SQLite文件")
    
    print()
    print("=== 检查完成 ===")
    
    # 返回是否完全清理
    return len(references) == 0 and len(files) == 0

if __name__ == "__main__":
    clean = main()
    if clean:
        print("🎉 系统已完全移除SQLite引用！")
        sys.exit(0)
    else:
        print("⚠️  仍有SQLite引用需要处理")
        sys.exit(1)
