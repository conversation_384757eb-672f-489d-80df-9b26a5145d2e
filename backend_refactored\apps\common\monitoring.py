"""
系统监控和性能指标
"""
import time
import psutil
import threading
from datetime import datetime, timedelta
from django.core.cache import cache
from django.db import connection
from django.utils import timezone
from .logging import log_performance_metric, system_logger


class SystemMonitor:
    """系统监控器"""
    
    def __init__(self):
        self.metrics = {}
        self.alerts = []
        self._lock = threading.Lock()
    
    def collect_system_metrics(self):
        """收集系统指标"""
        try:
            # CPU使用率
            cpu_percent = psutil.cpu_percent(interval=1)
            self.record_metric('system.cpu.usage', cpu_percent, '%')
            
            # 内存使用情况
            memory = psutil.virtual_memory()
            self.record_metric('system.memory.usage', memory.percent, '%')
            self.record_metric('system.memory.available', memory.available, 'bytes')
            
            # 磁盘使用情况
            disk = psutil.disk_usage('/')
            disk_percent = (disk.used / disk.total) * 100
            self.record_metric('system.disk.usage', disk_percent, '%')
            self.record_metric('system.disk.free', disk.free, 'bytes')
            
            # 网络IO
            net_io = psutil.net_io_counters()
            self.record_metric('system.network.bytes_sent', net_io.bytes_sent, 'bytes')
            self.record_metric('system.network.bytes_recv', net_io.bytes_recv, 'bytes')
            
            # 进程数量
            process_count = len(psutil.pids())
            self.record_metric('system.processes.count', process_count, 'count')
            
            # 检查告警条件
            self.check_system_alerts(cpu_percent, memory.percent, disk_percent)
            
        except Exception as e:
            system_logger.error(f"收集系统指标失败: {e}")
    
    def collect_database_metrics(self):
        """收集数据库指标"""
        try:
            start_time = time.time()
            
            # 测试数据库连接
            with connection.cursor() as cursor:
                cursor.execute("SELECT 1")
                cursor.fetchone()
            
            db_response_time = (time.time() - start_time) * 1000
            self.record_metric('database.response_time', db_response_time, 'ms')
            
            # 数据库连接数
            with connection.cursor() as cursor:
                cursor.execute("SHOW STATUS LIKE 'Threads_connected'")
                result = cursor.fetchone()
                if result:
                    connections = int(result[1])
                    self.record_metric('database.connections', connections, 'count')
            
            # 检查数据库告警
            if db_response_time > 1000:  # 响应时间超过1秒
                self.add_alert('database', 'high_response_time', 
                             f'数据库响应时间过长: {db_response_time:.2f}ms')
            
        except Exception as e:
            system_logger.error(f"收集数据库指标失败: {e}")
            self.add_alert('database', 'connection_error', f'数据库连接失败: {e}')
    
    def collect_redis_metrics(self):
        """收集Redis指标"""
        try:
            start_time = time.time()
            
            # 测试Redis连接
            cache.set('monitor_test', 'ok', 10)
            result = cache.get('monitor_test')
            
            redis_response_time = (time.time() - start_time) * 1000
            self.record_metric('redis.response_time', redis_response_time, 'ms')
            
            if result != 'ok':
                self.add_alert('redis', 'connection_error', 'Redis读写测试失败')
            
        except Exception as e:
            system_logger.error(f"收集Redis指标失败: {e}")
            self.add_alert('redis', 'connection_error', f'Redis连接失败: {e}')
    
    def collect_application_metrics(self):
        """收集应用指标"""
        try:
            from apps.tasks.models import Task, TaskStatus
            from apps.users.models import User
            from apps.files.models import AudioFile
            
            # 任务统计
            total_tasks = Task.objects.count()
            pending_tasks = Task.objects.filter(status=TaskStatus.PENDING).count()
            processing_tasks = Task.objects.filter(status=TaskStatus.PROCESSING).count()
            failed_tasks = Task.objects.filter(status=TaskStatus.FAILED).count()
            
            self.record_metric('app.tasks.total', total_tasks, 'count')
            self.record_metric('app.tasks.pending', pending_tasks, 'count')
            self.record_metric('app.tasks.processing', processing_tasks, 'count')
            self.record_metric('app.tasks.failed', failed_tasks, 'count')
            
            # 用户统计
            total_users = User.objects.filter(is_active=True).count()
            active_users_today = User.objects.filter(
                last_login__gte=timezone.now() - timedelta(days=1)
            ).count()
            
            self.record_metric('app.users.total', total_users, 'count')
            self.record_metric('app.users.active_today', active_users_today, 'count')
            
            # 文件统计
            from django.db import models
            total_files = AudioFile.objects.filter(is_active=True).count()
            total_file_size = AudioFile.objects.filter(is_active=True).aggregate(
                total=models.Sum('file_size')
            )['total'] or 0
            
            self.record_metric('app.files.total', total_files, 'count')
            self.record_metric('app.files.total_size', total_file_size, 'bytes')
            
            # 检查应用告警
            if pending_tasks > 100:
                self.add_alert('app', 'high_pending_tasks', f'待处理任务过多: {pending_tasks}')
            
            if failed_tasks > 50:
                self.add_alert('app', 'high_failed_tasks', f'失败任务过多: {failed_tasks}')
            
        except Exception as e:
            system_logger.error(f"收集应用指标失败: {e}")
    
    def record_metric(self, name, value, unit=''):
        """记录指标"""
        with self._lock:
            timestamp = datetime.now()
            self.metrics[name] = {
                'value': value,
                'unit': unit,
                'timestamp': timestamp
            }
            
            # 记录到日志
            log_performance_metric(name, value, unit, {'timestamp': timestamp.isoformat()})
            
            # 存储到缓存（用于API查询）
            cache_key = f"metric:{name}"
            cache.set(cache_key, {
                'value': value,
                'unit': unit,
                'timestamp': timestamp.isoformat()
            }, 300)  # 5分钟过期
    
    def add_alert(self, category, alert_type, message, level='warning'):
        """添加告警"""
        alert = {
            'category': category,
            'type': alert_type,
            'message': message,
            'level': level,
            'timestamp': datetime.now(),
            'resolved': False
        }
        
        with self._lock:
            self.alerts.append(alert)
            
            # 只保留最近100个告警
            if len(self.alerts) > 100:
                self.alerts = self.alerts[-100:]
        
        # 记录告警日志
        system_logger.warning(f"系统告警: {alert}")
        
        # 存储到缓存
        cache_key = f"alerts:{category}:{alert_type}"
        cache.set(cache_key, alert, 3600)  # 1小时过期
    
    def check_system_alerts(self, cpu_percent, memory_percent, disk_percent):
        """检查系统告警条件"""
        if cpu_percent > 80:
            self.add_alert('system', 'high_cpu', f'CPU使用率过高: {cpu_percent:.1f}%')
        
        if memory_percent > 85:
            self.add_alert('system', 'high_memory', f'内存使用率过高: {memory_percent:.1f}%')
        
        if disk_percent > 90:
            self.add_alert('system', 'high_disk', f'磁盘使用率过高: {disk_percent:.1f}%')
    
    def get_metrics(self, category=None):
        """获取指标"""
        with self._lock:
            if category:
                return {k: v for k, v in self.metrics.items() if k.startswith(f"{category}.")}
            return self.metrics.copy()
    
    def get_alerts(self, category=None, unresolved_only=True):
        """获取告警"""
        with self._lock:
            alerts = self.alerts.copy()
        
        if category:
            alerts = [a for a in alerts if a['category'] == category]
        
        if unresolved_only:
            alerts = [a for a in alerts if not a['resolved']]
        
        return alerts


class PerformanceTracker:
    """性能跟踪器"""
    
    def __init__(self, name):
        self.name = name
        self.start_time = None
        self.end_time = None
        self.duration = None
    
    def __enter__(self):
        self.start_time = time.time()
        return self
    
    def __exit__(self, exc_type, exc_val, exc_tb):
        self.end_time = time.time()
        self.duration = self.end_time - self.start_time
        
        # 记录性能指标
        log_performance_metric(
            f"performance.{self.name}",
            self.duration * 1000,  # 转换为毫秒
            'ms',
            {
                'start_time': self.start_time,
                'end_time': self.end_time,
                'success': exc_type is None
            }
        )
        
        # 如果执行时间过长，记录警告
        if self.duration > 5.0:  # 超过5秒
            system_logger.warning(f"性能警告: {self.name} 执行时间过长: {self.duration:.3f}s")


def track_performance(name):
    """性能跟踪装饰器"""
    def decorator(func):
        def wrapper(*args, **kwargs):
            with PerformanceTracker(f"{func.__module__}.{func.__name__}"):
                return func(*args, **kwargs)
        return wrapper
    return decorator


# 全局监控器实例
system_monitor = SystemMonitor()
