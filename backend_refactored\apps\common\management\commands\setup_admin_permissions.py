"""
设置管理员权限的Django管理命令
"""
from django.core.management.base import BaseCommand
from django.contrib.auth.models import Permission
from django.apps import apps
from apps.common.signals import ensure_admin_permissions, create_default_groups
from apps.common.logger_config import logger


class Command(BaseCommand):
    help = '为admin用户设置完整权限，并创建默认用户组'

    def add_arguments(self, parser):
        parser.add_argument(
            '--create-groups',
            action='store_true',
            help='同时创建默认用户组',
        )
        parser.add_argument(
            '--username',
            type=str,
            help='指定用户名（默认为所有超级用户）',
        )

    def handle(self, *args, **options):
        self.stdout.write(self.style.SUCCESS('开始设置管理员权限...'))
        
        try:
            User = apps.get_model('users', 'User')
            
            # 获取所有权限数量
            total_permissions = Permission.objects.count()
            self.stdout.write(f'系统中共有 {total_permissions} 个权限')
            
            # 如果指定了用户名
            if options['username']:
                try:
                    user = User.objects.get(username=options['username'])
                    all_permissions = Permission.objects.all()
                    user.user_permissions.set(all_permissions)
                    user.is_superuser = True
                    user.is_staff = True
                    user.save()
                    
                    self.stdout.write(
                        self.style.SUCCESS(f'✅ 为用户 {user.username} 分配了所有权限')
                    )
                    
                except User.DoesNotExist:
                    self.stdout.write(
                        self.style.ERROR(f'❌ 用户 {options["username"]} 不存在')
                    )
                    return
            else:
                # 为所有admin用户分配权限
                admin_count = ensure_admin_permissions()
                self.stdout.write(
                    self.style.SUCCESS(f'✅ 为 {admin_count} 个管理员用户分配了完整权限')
                )
            
            # 创建默认用户组
            if options['create_groups']:
                self.stdout.write('正在创建默认用户组...')
                create_default_groups()
                self.stdout.write(self.style.SUCCESS('✅ 默认用户组创建完成'))
            
            # 显示权限统计
            self.show_permission_stats()
            
            self.stdout.write(self.style.SUCCESS('🎉 权限设置完成！'))
            
        except Exception as e:
            logger.error(f"权限设置失败: {e}")
            self.stdout.write(self.style.ERROR(f'❌ 权限设置失败: {e}'))

    def show_permission_stats(self):
        """显示权限统计信息"""
        self.stdout.write('\n📊 权限统计:')
        self.stdout.write('-' * 50)
        
        # 按应用分组统计权限
        from django.contrib.contenttypes.models import ContentType
        
        apps_permissions = {}
        for permission in Permission.objects.select_related('content_type'):
            app_label = permission.content_type.app_label
            if app_label not in apps_permissions:
                apps_permissions[app_label] = 0
            apps_permissions[app_label] += 1
        
        for app_label, count in sorted(apps_permissions.items()):
            self.stdout.write(f'  {app_label}: {count} 个权限')
        
        self.stdout.write(f'\n总计: {sum(apps_permissions.values())} 个权限')
        
        # 显示超级用户信息
        User = apps.get_model('users', 'User')
        superusers = User.objects.filter(is_superuser=True)
        
        self.stdout.write(f'\n👑 超级用户 ({superusers.count()} 个):')
        for user in superusers:
            user_perms = user.user_permissions.count()
            self.stdout.write(f'  {user.username} ({user.email}): {user_perms} 个权限')
