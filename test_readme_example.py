#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
测试README.md中的API示例代码
"""

import requests
import json

def test_readme_api_example():
    """测试README.md中的API使用示例"""
    print("🔍 测试README.md中的API示例代码")
    print("=" * 50)
    
    try:
        # 检查API服务器状态
        health_response = requests.get('http://127.0.0.1:8000/health', timeout=5)
        if health_response.status_code != 200:
            print("❌ API服务器未运行，请先启动服务器:")
            print("python api_server.py --port 8000 --model_dir pretrained_models/CosyVoice-300M-Instruct")
            return False
        
        print("✅ API服务器运行正常")
        
        # 执行README.md中的示例代码
        print("\n📝 执行README.md示例代码:")
        
        # Generate audio and get file path
        data = {'tts_text': '你好，世界！', 'spk_id': '中文女'}
        response = requests.post('http://127.0.0.1:8000/inference_sft', data=data)

        if response.status_code == 200:
            result = response.json()
            audio_path = result['audio_info']['file_path']
            print(f"✅ Generated audio: {audio_path}")
            
            # 验证返回的数据结构
            expected_keys = ['success', 'message', 'audio_info', 'text', 'speaker']
            for key in expected_keys:
                if key in result:
                    print(f"   ✅ {key}: {result[key] if key != 'audio_info' else 'OK'}")
                else:
                    print(f"   ❌ 缺少字段: {key}")
            
            # 验证audio_info结构
            audio_info = result.get('audio_info', {})
            expected_audio_keys = ['file_path', 'filename', 'file_size', 'duration', 'sample_rate', 'channels']
            print(f"\n   📊 音频信息验证:")
            for key in expected_audio_keys:
                if key in audio_info:
                    print(f"      ✅ {key}: {audio_info[key]}")
                else:
                    print(f"      ❌ 缺少字段: {key}")
            
            # List all generated files
            files_response = requests.get('http://127.0.0.1:8000/list_generated_audio')
            if files_response.status_code == 200:
                files_data = files_response.json()
                files = files_data['audio_files']
                print(f"\n✅ Total files: {len(files)}")
                print(f"   输出目录: {files_data['output_directory']}")
                
                if files:
                    print(f"   最新文件: {files[0]['filename']}")
                    print(f"   文件大小: {files[0]['file_size']} bytes")
                    print(f"   音频时长: {files[0]['duration']} 秒")
            else:
                print(f"❌ 获取文件列表失败: {files_response.status_code}")
                
        else:
            print(f"❌ 音频生成失败: {response.status_code}")
            print(f"   错误信息: {response.text}")
            return False
        
        print(f"\n🎉 README.md示例代码测试通过!")
        return True
        
    except Exception as e:
        print(f"❌ 测试异常: {e}")
        return False

def test_api_documentation_access():
    """测试API文档访问"""
    print(f"\n🔍 测试API文档访问")
    print("-" * 30)
    
    docs_urls = [
        ('Swagger UI', 'http://127.0.0.1:8000/docs'),
        ('ReDoc', 'http://127.0.0.1:8000/redoc'),
        ('OpenAPI JSON', 'http://127.0.0.1:8000/openapi.json')
    ]
    
    for name, url in docs_urls:
        try:
            response = requests.get(url, timeout=5)
            if response.status_code == 200:
                print(f"✅ {name}: {url}")
            else:
                print(f"❌ {name}: {url} (状态码: {response.status_code})")
        except Exception as e:
            print(f"❌ {name}: {url} (错误: {e})")

def test_file_management_endpoints():
    """测试文件管理端点"""
    print(f"\n🔍 测试文件管理端点")
    print("-" * 30)
    
    endpoints = [
        ('GET', '/list_generated_audio', '获取音频文件列表'),
        ('GET', '/health', '健康检查')
    ]
    
    for method, endpoint, description in endpoints:
        try:
            url = f'http://127.0.0.1:8000{endpoint}'
            response = requests.get(url, timeout=5)
            
            if response.status_code == 200:
                print(f"✅ {method} {endpoint}: {description}")
                if endpoint == '/health':
                    health_data = response.json()
                    print(f"   模型已加载: {health_data.get('model_loaded', 'Unknown')}")
                    print(f"   输出目录: {health_data.get('output_directory', 'Unknown')}")
            else:
                print(f"❌ {method} {endpoint}: {description} (状态码: {response.status_code})")
        except Exception as e:
            print(f"❌ {method} {endpoint}: {description} (错误: {e})")

def main():
    """主函数"""
    print("📖 README.md API功能验证测试")
    print("=" * 60)
    
    # 测试主要示例代码
    success = test_readme_api_example()
    
    # 测试API文档访问
    test_api_documentation_access()
    
    # 测试文件管理端点
    test_file_management_endpoints()
    
    print(f"\n" + "=" * 60)
    if success:
        print("🎉 README.md中的API功能描述准确!")
        print("✅ 示例代码可以正常工作")
        print("✅ API响应格式符合文档描述")
        print("✅ 文件管理功能正常")
    else:
        print("⚠️ README.md中的某些功能可能需要调整")
    
    print(f"\n💡 提示:")
    print("- 确保API服务器正在运行")
    print("- 访问 http://127.0.0.1:8000/docs 查看完整API文档")
    print("- 生成的音频文件保存在 generated_audio/ 目录中")

if __name__ == '__main__':
    main()
