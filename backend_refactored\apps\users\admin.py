"""
用户管理Admin配置
"""
from django.contrib import admin
from django.contrib.auth.admin import UserAdmin as BaseUserAdmin
from django.utils.html import format_html
from django.urls import reverse
from django.utils import timezone
from django.contrib.auth.forms import UserCreationForm, UserChangeForm
from .models import User, UserProfile, UserRole


class UserProfileInline(admin.StackedInline):
    """用户资料内联编辑"""
    model = UserProfile
    can_delete = False
    verbose_name = '用户资料'
    verbose_name_plural = '用户资料'
    extra = 0
    
    fieldsets = (
        ('联系信息', {
            'fields': ('phone', 'address', 'birth_date', 'company', 'website')
        }),
        ('偏好设置', {
            'fields': ('language', 'timezone', 'email_notifications')
        })
    )


@admin.register(User)
class UserAdmin(BaseUserAdmin):
    """用户管理"""
    inlines = [UserProfileInline]
    
    list_display = ('username', 'email', 'role_display', 'quota_display', 
                   'is_active', 'is_staff', 'is_superuser', 'last_login', 'created_at')
    list_filter = ('role', 'is_active', 'is_staff', 'is_superuser', 'created_at', 'last_login')
    search_fields = ('username', 'email', 'first_name', 'last_name')
    readonly_fields = ('id', 'created_at', 'updated_at', 'last_login', 'quota_usage_display', 'tasks_count')
    
    fieldsets = (
        ('基本信息', {
            'fields': ('id', 'username', 'email', 'first_name', 'last_name', 'password')
        }),
        ('权限信息', {
            'fields': ('role', 'is_active', 'is_staff', 'is_superuser', 'groups', 'user_permissions')
        }),
        ('配额管理', {
            'fields': ('quota_limit', 'quota_used', 'quota_usage_display')
        }),
        ('个人信息', {
            'fields': ('avatar_url', 'bio')
        }),
        ('时间信息', {
            'fields': ('created_at', 'updated_at', 'last_login', 'deleted_at')
        }),
        ('统计信息', {
            'fields': ('tasks_count',),
            'classes': ('collapse',)
        })
    )
    
    add_fieldsets = (
        ('基本信息', {
            'classes': ('wide',),
            'fields': ('username', 'email', 'password1', 'password2', 'role', 'quota_limit')
        }),
    )
    
    def role_display(self, obj):
        """角色显示"""
        colors = {
            UserRole.BASIC: '#6c757d',
            UserRole.PREMIUM: '#007bff', 
            UserRole.ADMIN: '#dc3545'
        }
        color = colors.get(obj.role, '#6c757d')
        return format_html(
            '<span style="color: {}; font-weight: bold;">{}</span>',
            color, obj.get_role_display()
        )
    role_display.short_description = '角色'
    
    def quota_display(self, obj):
        """配额显示"""
        percentage = obj.quota_usage_percentage
        if percentage >= 90:
            color = '#dc3545'  # 红色
        elif percentage >= 70:
            color = '#ffc107'  # 黄色
        else:
            color = '#28a745'  # 绿色
            
        return format_html(
            '<div style="width: 100px; background: #f8f9fa; border-radius: 3px; overflow: hidden;">'
            '<div style="width: {}%; height: 20px; background: {}; text-align: center; line-height: 20px; color: white; font-size: 12px;">'
            '{}/{}'
            '</div></div>',
            percentage, color, obj.quota_used, obj.quota_limit
        )
    quota_display.short_description = '配额使用'
    
    def quota_usage_display(self, obj):
        """配额使用详情"""
        return format_html(
            '已使用: {} / 总配额: {} ({}%)<br>'
            '剩余配额: {}',
            obj.quota_used, obj.quota_limit, 
            round(obj.quota_usage_percentage, 1),
            obj.quota_remaining
        )
    quota_usage_display.short_description = '配额详情'
    
    def tasks_count(self, obj):
        """任务数量统计"""
        from apps.tasks.models import TaskStatus
        total = obj.tasks.count()
        completed = obj.tasks.filter(status=TaskStatus.COMPLETED).count()
        failed = obj.tasks.filter(status=TaskStatus.FAILED).count()
        
        return format_html(
            '总任务: {} | 已完成: {} | 失败: {}',
            total, completed, failed
        )
    tasks_count.short_description = '任务统计'
    
    actions = ['activate_users', 'deactivate_users', 'reset_quota', 'upgrade_to_premium', 'make_admin']
    
    def activate_users(self, request, queryset):
        """激活用户"""
        updated = queryset.update(is_active=True, deleted_at=None)
        self.message_user(request, f'成功激活 {updated} 个用户')
    activate_users.short_description = '激活选中用户'
    
    def deactivate_users(self, request, queryset):
        """停用用户"""
        for user in queryset:
            user.soft_delete()
        self.message_user(request, f'成功停用 {queryset.count()} 个用户')
    deactivate_users.short_description = '停用选中用户'
    
    def reset_quota(self, request, queryset):
        """重置配额"""
        updated = queryset.update(quota_used=0)
        self.message_user(request, f'成功重置 {updated} 个用户的配额')
    reset_quota.short_description = '重置配额'
    
    def upgrade_to_premium(self, request, queryset):
        """升级为高级用户"""
        updated = queryset.filter(role=UserRole.BASIC).update(
            role=UserRole.PREMIUM, 
            quota_limit=5000
        )
        self.message_user(request, f'成功升级 {updated} 个用户为高级用户')
    upgrade_to_premium.short_description = '升级为高级用户'
    
    def make_admin(self, request, queryset):
        """设为管理员"""
        updated = queryset.update(
            role=UserRole.ADMIN,
            is_staff=True,
            is_superuser=True
        )
        self.message_user(request, f'成功设置 {updated} 个用户为管理员')
    make_admin.short_description = '设为管理员'


@admin.register(UserProfile)
class UserProfileAdmin(admin.ModelAdmin):
    """用户资料管理"""
    list_display = ('user_display', 'phone', 'company', 'language', 'timezone', 'email_notifications')
    list_filter = ('language', 'timezone', 'email_notifications', 'created_at')
    search_fields = ('user__username', 'user__email', 'phone', 'company')
    readonly_fields = ('id', 'created_at', 'updated_at')
    
    fieldsets = (
        ('基本信息', {
            'fields': ('id', 'user')
        }),
        ('联系信息', {
            'fields': ('phone', 'address', 'birth_date', 'company', 'website')
        }),
        ('偏好设置', {
            'fields': ('language', 'timezone', 'email_notifications')
        }),
        ('时间信息', {
            'fields': ('created_at', 'updated_at')
        })
    )
    
    def user_display(self, obj):
        """用户显示"""
        return f"{obj.user.username} ({obj.user.email})"
    user_display.short_description = '用户'
