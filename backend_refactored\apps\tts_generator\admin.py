"""
TTS生成器管理界面
"""
from django.contrib import admin
from django.utils.html import format_html
from django.urls import reverse
from django.utils.safestring import mark_safe
from django.http import JsonResponse
from django.shortcuts import render, redirect
from django.contrib import messages
from django.urls import path
from .models import Speaker, TTSTemplate, QuickGeneration, APITestRecord
import json


@admin.register(Speaker)
class SpeakerAdmin(admin.ModelAdmin):
    list_display = ['name', 'speaker_id', 'language', 'gender', 'is_active', 'created_at']
    list_filter = ['language', 'gender', 'is_active', 'created_at']
    search_fields = ['name', 'speaker_id', 'description']
    list_editable = ['is_active']
    readonly_fields = ['id', 'created_at', 'updated_at']
    
    fieldsets = (
        ('基本信息', {
            'fields': ('name', 'speaker_id', 'language', 'gender')
        }),
        ('详细信息', {
            'fields': ('description', 'is_active')
        }),
        ('系统信息', {
            'fields': ('id', 'created_at', 'updated_at'),
            'classes': ('collapse',)
        })
    )

    def get_queryset(self, request):
        return super().get_queryset(request).select_related()

    actions = ['enable_speakers', 'disable_speakers']

    def enable_speakers(self, request, queryset):
        updated = queryset.update(is_active=True)
        self.message_user(request, f'已启用 {updated} 个说话人')
    enable_speakers.short_description = '启用选中的说话人'

    def disable_speakers(self, request, queryset):
        updated = queryset.update(is_active=False)
        self.message_user(request, f'已禁用 {updated} 个说话人')
    disable_speakers.short_description = '禁用选中的说话人'


@admin.register(TTSTemplate)
class TTSTemplateAdmin(admin.ModelAdmin):
    list_display = ['name', 'template_type', 'speaker', 'is_active', 'created_by', 'created_at']
    list_filter = ['template_type', 'is_active', 'created_at', 'created_by']
    search_fields = ['name', 'text_template', 'instruct_text']
    list_editable = ['is_active']
    readonly_fields = ['id', 'created_at', 'updated_at']
    
    fieldsets = (
        ('基本信息', {
            'fields': ('name', 'template_type', 'speaker')
        }),
        ('模板内容', {
            'fields': ('text_template', 'instruct_text')
        }),
        ('设置', {
            'fields': ('is_active', 'created_by')
        }),
        ('系统信息', {
            'fields': ('id', 'created_at', 'updated_at'),
            'classes': ('collapse',)
        })
    )

    def save_model(self, request, obj, form, change):
        if not change:  # 新建时
            obj.created_by = request.user
        super().save_model(request, obj, form, change)

    def get_queryset(self, request):
        return super().get_queryset(request).select_related('speaker', 'created_by')


@admin.register(QuickGeneration)
class QuickGenerationAdmin(admin.ModelAdmin):
    list_display = ['text_preview', 'generation_type', 'speaker', 'status', 'duration_display', 'user', 'created_at']
    list_filter = ['generation_type', 'status', 'created_at', 'user']
    search_fields = ['text_content', 'instruct_text', 'reference_text']
    readonly_fields = ['id', 'task_id', 'processing_time', 'audio_duration', 'created_at', 'started_at', 'completed_at']
    
    fieldsets = (
        ('生成信息', {
            'fields': ('user', 'generation_type', 'text_content', 'speaker')
        }),
        ('高级设置', {
            'fields': ('instruct_text', 'reference_audio', 'reference_text'),
            'classes': ('collapse',)
        }),
        ('结果信息', {
            'fields': ('task_id', 'status', 'result_audio', 'error_message')
        }),
        ('性能指标', {
            'fields': ('processing_time', 'audio_duration', 'created_at', 'started_at', 'completed_at'),
            'classes': ('collapse',)
        }),
        ('系统信息', {
            'fields': ('id',),
            'classes': ('collapse',)
        })
    )

    def text_preview(self, obj):
        return obj.text_content[:50] + '...' if len(obj.text_content) > 50 else obj.text_content
    text_preview.short_description = '文本预览'

    def get_queryset(self, request):
        return super().get_queryset(request).select_related('user', 'speaker')

    def has_add_permission(self, request):
        return True

    def has_change_permission(self, request, obj=None):
        return True

    def has_delete_permission(self, request, obj=None):
        return True

    # 自定义操作
    actions = ['retry_failed_generations', 'export_successful_generations']

    def retry_failed_generations(self, request, queryset):
        failed_count = queryset.filter(status='failed').count()
        if failed_count > 0:
            # 这里可以添加重试逻辑
            self.message_user(request, f'已标记 {failed_count} 个失败任务进行重试')
        else:
            self.message_user(request, '没有失败的任务需要重试')
    retry_failed_generations.short_description = '重试失败的生成任务'

    def export_successful_generations(self, request, queryset):
        successful_count = queryset.filter(status='completed').count()
        self.message_user(request, f'已导出 {successful_count} 个成功的生成记录')
    export_successful_generations.short_description = '导出成功的生成记录'


@admin.register(APITestRecord)
class APITestRecordAdmin(admin.ModelAdmin):
    list_display = ['api_endpoint', 'method', 'response_status', 'is_success', 'response_time_display', 'user', 'created_at']
    list_filter = ['method', 'is_success', 'response_status', 'created_at', 'user']
    search_fields = ['api_endpoint', 'error_message']
    readonly_fields = ['id', 'request_data_display', 'response_data_display', 'created_at']
    
    fieldsets = (
        ('请求信息', {
            'fields': ('user', 'api_endpoint', 'method', 'request_data_display')
        }),
        ('响应信息', {
            'fields': ('response_status', 'response_data_display', 'response_time', 'is_success')
        }),
        ('错误信息', {
            'fields': ('error_message',),
            'classes': ('collapse',)
        }),
        ('系统信息', {
            'fields': ('id', 'created_at'),
            'classes': ('collapse',)
        })
    )

    def request_data_display(self, obj):
        if obj.request_data:
            return format_html('<pre>{}</pre>', json.dumps(obj.request_data, indent=2, ensure_ascii=False))
        return '-'
    request_data_display.short_description = '请求数据'

    def response_data_display(self, obj):
        if obj.response_data:
            return format_html('<pre>{}</pre>', json.dumps(obj.response_data, indent=2, ensure_ascii=False))
        return '-'
    response_data_display.short_description = '响应数据'

    def response_time_display(self, obj):
        if obj.response_time:
            return f"{obj.response_time:.2f}ms"
        return '-'
    response_time_display.short_description = '响应时间'

    def get_queryset(self, request):
        return super().get_queryset(request).select_related('user')

    def has_add_permission(self, request):
        return False  # 不允许手动添加测试记录

    def has_change_permission(self, request, obj=None):
        return False  # 不允许修改测试记录

    # 自定义操作
    actions = ['clear_old_records']

    def clear_old_records(self, request, queryset):
        from datetime import datetime, timedelta
        from django.utils import timezone
        
        # 删除30天前的记录
        cutoff_date = timezone.now() - timedelta(days=30)
        old_records = APITestRecord.objects.filter(created_at__lt=cutoff_date)
        count = old_records.count()
        old_records.delete()
        
        self.message_user(request, f'已清理 {count} 条30天前的测试记录')
    clear_old_records.short_description = '清理30天前的测试记录'


# 自定义管理页面
class TTSGeneratorAdminSite(admin.AdminSite):
    site_header = 'TTS生成器管理'
    site_title = 'TTS生成器'
    index_title = 'TTS生成器管理中心'

    def get_urls(self):
        urls = super().get_urls()
        custom_urls = [
            path('tts-generator/', self.admin_view(self.tts_generator_view), name='tts-generator'),
            path('api-tester/', self.admin_view(self.api_tester_view), name='api-tester'),
        ]
        return custom_urls + urls

    def tts_generator_view(self, request):
        """TTS生成器视图"""
        context = {
            'title': 'TTS生成器',
            'site_title': self.site_title,
            'site_header': self.site_header,
        }
        return render(request, 'admin/tts_generator/generator.html', context)

    def api_tester_view(self, request):
        """API测试器视图"""
        context = {
            'title': 'API测试器',
            'site_title': self.site_title,
            'site_header': self.site_header,
        }
        return render(request, 'admin/tts_generator/api_tester.html', context)
