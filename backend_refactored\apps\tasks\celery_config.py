"""
Celery定时任务配置
"""
from celery.schedules import crontab

# Celery Beat定时任务配置
CELERY_BEAT_SCHEDULE = {
    # 每小时清理过期文件
    'cleanup-expired-files': {
        'task': 'apps.tasks.tasks.cleanup_expired_files',
        'schedule': crontab(minute=0),  # 每小时执行
    },
    
    # 每天凌晨生成日报
    'generate-daily-report': {
        'task': 'apps.tasks.tasks.generate_daily_report',
        'schedule': crontab(hour=1, minute=0),  # 每天凌晨1点执行
    },
    
    # 每5分钟检查超时任务
    'check-timeout-tasks': {
        'task': 'apps.tasks.tasks.check_timeout_tasks',
        'schedule': crontab(minute='*/5'),  # 每5分钟执行
    },
    
    # 每30分钟清理临时文件
    'cleanup-temp-files': {
        'task': 'apps.tasks.tasks.cleanup_temp_files',
        'schedule': crontab(minute='*/30'),  # 每30分钟执行
    },
    
    # 每周日凌晨清理旧日志
    'cleanup-old-logs': {
        'task': 'apps.tasks.tasks.cleanup_old_logs',
        'schedule': crontab(hour=2, minute=0, day_of_week=0),  # 每周日凌晨2点执行
    },
}

# 时区设置
CELERY_TIMEZONE = 'Asia/Shanghai'
