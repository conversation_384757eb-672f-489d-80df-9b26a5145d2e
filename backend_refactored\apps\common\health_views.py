"""
健康检查视图
"""
import os
from django.http import JsonResponse
from django.db import connection
from django.core.cache import cache
from django.conf import settings
from rest_framework.decorators import api_view, permission_classes
from rest_framework.permissions import AllowAny
from drf_spectacular.utils import extend_schema
from loguru import logger


@extend_schema(
    summary="系统健康检查",
    description="检查系统整体健康状态",
    tags=["健康检查"]
)
@api_view(['GET'])
@permission_classes([AllowAny])
def health_check(request):
    """系统健康检查"""
    try:
        # 简化的健康检查，避免复杂的依赖
        health_status = {
            'status': 'healthy',
            'timestamp': None,
            'services': {
                'database': check_database_simple(),
                'model': check_model_simple(),
            }
        }

        # 检查是否有服务不健康
        unhealthy_services = [
            service for service, status in health_status['services'].items()
            if not status.get('healthy', False)
        ]

        if unhealthy_services:
            health_status['status'] = 'unhealthy'
            health_status['unhealthy_services'] = unhealthy_services

        # 添加时间戳
        from django.utils import timezone
        health_status['timestamp'] = timezone.now().isoformat()

        # 根据健康状态返回相应的HTTP状态码
        status_code = 200 if health_status['status'] == 'healthy' else 503

        return JsonResponse(health_status, status=status_code)

    except Exception as e:
        logger.error(f"健康检查失败: {e}")
        return JsonResponse({
            'status': 'error',
            'message': f'健康检查失败: {str(e)}',
            'timestamp': timezone.now().isoformat()
        }, status=500)


@extend_schema(
    summary="数据库健康检查",
    description="检查数据库连接状态",
    tags=["健康检查"]
)
@api_view(['GET'])
@permission_classes([AllowAny])
def db_health_check(request):
    """数据库健康检查"""
    result = check_database()
    status_code = 200 if result['healthy'] else 503
    return JsonResponse(result, status=status_code)


@extend_schema(
    summary="Redis健康检查",
    description="检查Redis连接状态",
    tags=["健康检查"]
)
@api_view(['GET'])
@permission_classes([AllowAny])
def redis_health_check(request):
    """Redis健康检查"""
    result = check_redis()
    status_code = 200 if result['healthy'] else 503
    return JsonResponse(result, status=status_code)


@extend_schema(
    summary="Celery健康检查",
    description="检查Celery任务队列状态",
    tags=["健康检查"]
)
@api_view(['GET'])
@permission_classes([AllowAny])
def celery_health_check(request):
    """Celery健康检查"""
    result = check_celery()
    status_code = 200 if result['healthy'] else 503
    return JsonResponse(result, status=status_code)


@extend_schema(
    summary="模型健康检查",
    description="检查CosyVoice模型状态",
    tags=["健康检查"]
)
@api_view(['GET'])
@permission_classes([AllowAny])
def model_health_check(request):
    """模型健康检查"""
    result = check_model()
    status_code = 200 if result['healthy'] else 503
    return JsonResponse(result, status=status_code)


def check_database():
    """检查数据库连接"""
    try:
        with connection.cursor() as cursor:
            cursor.execute("SELECT 1")
            cursor.fetchone()
        
        return {
            'healthy': True,
            'message': '数据库连接正常',
            'database': settings.DATABASES['default']['NAME']
        }
    except Exception as e:
        logger.error(f"数据库健康检查失败: {e}")
        return {
            'healthy': False,
            'message': f'数据库连接失败: {str(e)}',
            'database': settings.DATABASES['default']['NAME']
        }


def check_redis():
    """检查Redis连接"""
    try:
        # 测试Redis连接
        cache.set('health_check', 'ok', 10)
        result = cache.get('health_check')
        
        if result == 'ok':
            return {
                'healthy': True,
                'message': 'Redis连接正常'
            }
        else:
            return {
                'healthy': False,
                'message': 'Redis读写测试失败'
            }
    except Exception as e:
        logger.error(f"Redis健康检查失败: {e}")
        return {
            'healthy': False,
            'message': f'Redis连接失败: {str(e)}'
        }


def check_celery():
    """检查Celery状态"""
    try:
        from celery import current_app
        
        # 检查Celery工作节点
        inspect = current_app.control.inspect()
        stats = inspect.stats()
        
        if stats:
            active_workers = len(stats)
            return {
                'healthy': True,
                'message': f'Celery正常运行，{active_workers}个工作节点',
                'workers': active_workers,
                'worker_stats': stats
            }
        else:
            return {
                'healthy': False,
                'message': '没有活跃的Celery工作节点',
                'workers': 0
            }
    except Exception as e:
        logger.error(f"Celery健康检查失败: {e}")
        return {
            'healthy': False,
            'message': f'Celery检查失败: {str(e)}'
        }


def check_model():
    """检查CosyVoice模型"""
    try:
        model_dir = settings.COSYVOICE_MODEL_DIR
        
        # 检查模型目录是否存在
        if not os.path.exists(model_dir):
            return {
                'healthy': False,
                'message': f'模型目录不存在: {model_dir}',
                'model_dir': str(model_dir)
            }
        
        # 检查模型文件
        model_files = os.listdir(model_dir)
        if not model_files:
            return {
                'healthy': False,
                'message': '模型目录为空',
                'model_dir': str(model_dir)
            }
        
        return {
            'healthy': True,
            'message': '模型文件正常',
            'model_dir': str(model_dir),
            'model_files_count': len(model_files)
        }
        
    except Exception as e:
        logger.error(f"模型健康检查失败: {e}")
        return {
            'healthy': False,
            'message': f'模型检查失败: {str(e)}'
        }


def check_storage():
    """检查存储空间"""
    try:
        import shutil
        
        # 检查媒体目录磁盘空间
        media_root = settings.MEDIA_ROOT
        total, used, free = shutil.disk_usage(media_root)
        
        # 计算使用率
        usage_percent = (used / total) * 100
        
        # 如果使用率超过90%，认为不健康
        healthy = usage_percent < 90
        
        return {
            'healthy': healthy,
            'message': f'存储使用率: {usage_percent:.1f}%',
            'storage': {
                'total': total,
                'used': used,
                'free': free,
                'usage_percent': round(usage_percent, 1)
            },
            'media_root': str(media_root)
        }
        
    except Exception as e:
        logger.error(f"存储健康检查失败: {e}")
        return {
            'healthy': False,
            'message': f'存储检查失败: {str(e)}'
        }


def check_database_simple():
    """简化的数据库检查"""
    try:
        # 尝试执行简单查询
        with connection.cursor() as cursor:
            cursor.execute("SELECT 1")
            cursor.fetchone()

        return {
            'healthy': True,
            'message': '数据库连接正常'
        }
    except Exception as e:
        logger.error(f"数据库健康检查失败: {e}")
        return {
            'healthy': False,
            'message': f'数据库连接失败: {str(e)}'
        }


def check_model_simple():
    """简化的模型检查"""
    try:
        model_dir = settings.COSYVOICE_MODEL_DIR

        # 检查模型目录是否存在
        if not os.path.exists(model_dir):
            return {
                'healthy': False,
                'message': f'模型目录不存在: {model_dir}'
            }

        return {
            'healthy': True,
            'message': '模型目录正常'
        }

    except Exception as e:
        logger.error(f"模型健康检查失败: {e}")
        return {
            'healthy': False,
            'message': f'模型检查失败: {str(e)}'
        }
