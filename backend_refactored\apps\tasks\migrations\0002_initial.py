# Generated by Django 4.2.7 on 2025-07-24 03:28

from django.conf import settings
from django.db import migrations, models
import django.db.models.deletion


class Migration(migrations.Migration):
    initial = True

    dependencies = [
        ("tasks", "0001_initial"),
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
    ]

    operations = [
        migrations.AddField(
            model_name="task",
            name="user",
            field=models.ForeignKey(
                blank=True,
                null=True,
                on_delete=django.db.models.deletion.CASCADE,
                related_name="tasks",
                to=settings.AUTH_USER_MODEL,
            ),
        ),
        migrations.AddIndex(
            model_name="tasklog",
            index=models.Index(
                fields=["task", "created_at"], name="task_logs_task_id_e5a91c_idx"
            ),
        ),
        migrations.AddIndex(
            model_name="tasklog",
            index=models.Index(fields=["level"], name="task_logs_level_098140_idx"),
        ),
        migrations.AddIndex(
            model_name="task",
            index=models.Index(
                fields=["user", "created_at"], name="tasks_user_id_90ebe9_idx"
            ),
        ),
        migrations.AddIndex(
            model_name="task",
            index=models.Index(fields=["status"], name="tasks_status_031d4c_idx"),
        ),
        migrations.AddIndex(
            model_name="task",
            index=models.Index(
                fields=["task_type", "status"], name="tasks_task_ty_ec75c1_idx"
            ),
        ),
        migrations.AddIndex(
            model_name="task",
            index=models.Index(
                fields=["priority", "created_at"], name="tasks_priorit_36b29a_idx"
            ),
        ),
        migrations.AddIndex(
            model_name="task",
            index=models.Index(fields=["created_at"], name="tasks_created_db4e37_idx"),
        ),
    ]
