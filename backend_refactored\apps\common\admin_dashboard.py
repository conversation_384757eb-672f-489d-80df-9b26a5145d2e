"""
自定义管理后台仪表板
"""
from django.contrib.admin import AdminSite
from django.shortcuts import render
from django.db.models import Count, Q
from django.utils import timezone
from datetime import datetime, timedelta
from django.contrib.auth.decorators import staff_member_required
from django.utils.decorators import method_decorator
from .logger_config import logger


class CosyVoiceAdminSite(AdminSite):
    """自定义管理后台站点"""
    site_header = 'CosyVoice 管理后台'
    site_title = 'CosyVoice Admin'
    index_title = '欢迎使用 CosyVoice 管理系统'
    
    def index(self, request, extra_context=None):
        """自定义首页"""
        extra_context = extra_context or {}
        
        # 获取统计数据
        stats = self.get_dashboard_stats()
        extra_context['stats'] = stats
        
        # 获取最近活动
        recent_activities = self.get_recent_activities()
        extra_context['recent_activities'] = recent_activities
        
        return super().index(request, extra_context)
    
    def get_dashboard_stats(self):
        """获取仪表板统计数据"""
        try:
            from apps.users.models import User
            from apps.tasks.models import Task, TaskStatus
            from apps.files.models import AudioFile
            
            # 获取今天的日期范围
            today = timezone.now().date()
            yesterday = today - timedelta(days=1)
            today_start = timezone.make_aware(datetime.combine(today, datetime.min.time()))
            yesterday_start = timezone.make_aware(datetime.combine(yesterday, datetime.min.time()))
            
            # 今日生成数量
            today_generated = Task.objects.filter(
                created_at__gte=today_start,
                status=TaskStatus.COMPLETED
            ).count()
            
            # 昨日生成数量
            yesterday_generated = Task.objects.filter(
                created_at__gte=yesterday_start,
                created_at__lt=today_start,
                status=TaskStatus.COMPLETED
            ).count()
            
            # 计算增长率
            today_growth = 0
            if yesterday_generated > 0:
                today_growth = round(((today_generated - yesterday_generated) / yesterday_generated) * 100, 1)
            elif today_generated > 0:
                today_growth = 100
            
            # 总用户数
            total_users = User.objects.count()
            
            # 活跃用户数（最近7天有登录的用户）
            week_ago = timezone.now() - timedelta(days=7)
            active_users = User.objects.filter(last_login__gte=week_ago).count()
            
            # 成功率
            total_tasks = Task.objects.count()
            completed_tasks = Task.objects.filter(status=TaskStatus.COMPLETED).count()
            success_rate = 0
            if total_tasks > 0:
                success_rate = round((completed_tasks / total_tasks) * 100, 1)
            
            # 音频文件总数
            total_files = AudioFile.objects.count()
            
            # 昨日文件数
            yesterday_files = AudioFile.objects.filter(
                created_at__gte=yesterday_start,
                created_at__lt=today_start
            ).count()
            
            # 今日文件数
            today_files = AudioFile.objects.filter(
                created_at__gte=today_start
            ).count()
            
            # 文件增长率
            files_growth = 0
            if yesterday_files > 0:
                files_growth = round(((today_files - yesterday_files) / yesterday_files) * 100, 1)
            elif today_files > 0:
                files_growth = 100
            
            return {
                'today_generated': today_generated,
                'today_growth': max(0, today_growth),  # 确保不显示负数
                'total_users': total_users,
                'active_users': active_users,
                'success_rate': success_rate,
                'success_growth': 2.1,  # 模拟数据
                'total_files': total_files,
                'files_growth': max(0, files_growth),
            }
            
        except Exception as e:
            logger.error(f"获取仪表板统计数据失败: {e}")
            return {
                'today_generated': 0,
                'today_growth': 0,
                'total_users': 0,
                'active_users': 0,
                'success_rate': 0,
                'success_growth': 0,
                'total_files': 0,
                'files_growth': 0,
            }
    
    def get_recent_activities(self):
        """获取最近活动"""
        try:
            from apps.tasks.models import Task, TaskStatus, TaskType
            from apps.users.models import User
            from apps.files.models import AudioFile
            
            activities = []
            
            # 最近的任务
            recent_tasks = Task.objects.filter(
                status=TaskStatus.COMPLETED
            ).order_by('-completed_at')[:3]
            
            for task in recent_tasks:
                time_diff = timezone.now() - (task.completed_at or task.created_at)
                if time_diff.days > 0:
                    time_str = f"{time_diff.days}天前"
                elif time_diff.seconds > 3600:
                    time_str = f"{time_diff.seconds // 3600}小时前"
                elif time_diff.seconds > 60:
                    time_str = f"{time_diff.seconds // 60}分钟前"
                else:
                    time_str = "刚刚"
                
                # 根据任务类型设置图标和颜色
                if task.task_type == TaskType.SFT:
                    icon, bg_color, color = "🎵", "#e6f7ff", "#1890ff"
                    title = "完成了SFT语音合成"
                elif task.task_type == TaskType.ZERO_SHOT:
                    icon, bg_color, color = "🎯", "#f6ffed", "#52c41a"
                    title = "完成了零样本语音克隆"
                elif task.task_type == TaskType.CROSS_LINGUAL:
                    icon, bg_color, color = "🌍", "#fff2e8", "#fa8c16"
                    title = "完成了跨语种复刻"
                else:
                    icon, bg_color, color = "🎵", "#e6f7ff", "#1890ff"
                    title = "完成了语音合成任务"
                
                activities.append({
                    'icon': icon,
                    'bg_color': bg_color,
                    'color': color,
                    'title': title,
                    'time': time_str
                })
            
            # 最近的用户注册
            recent_users = User.objects.order_by('-created_at')[:2]
            for user in recent_users:
                time_diff = timezone.now() - user.created_at
                if time_diff.days > 0:
                    time_str = f"{time_diff.days}天前"
                elif time_diff.seconds > 3600:
                    time_str = f"{time_diff.seconds // 3600}小时前"
                elif time_diff.seconds > 60:
                    time_str = f"{time_diff.seconds // 60}分钟前"
                else:
                    time_str = "刚刚"
                
                activities.append({
                    'icon': "👤",
                    'bg_color': "#fff2e8",
                    'color': "#fa8c16",
                    'title': f"新用户 {user.username} 注册",
                    'time': time_str
                })
            
            # 最近的文件上传
            recent_files = AudioFile.objects.order_by('-created_at')[:2]
            for file in recent_files:
                time_diff = timezone.now() - file.created_at
                if time_diff.days > 0:
                    time_str = f"{time_diff.days}天前"
                elif time_diff.seconds > 3600:
                    time_str = f"{time_diff.seconds // 3600}小时前"
                elif time_diff.seconds > 60:
                    time_str = f"{time_diff.seconds // 60}分钟前"
                else:
                    time_str = "刚刚"
                
                activities.append({
                    'icon': "📁",
                    'bg_color': "#f6ffed",
                    'color': "#52c41a",
                    'title': f"生成了音频文件 {file.filename[:20]}...",
                    'time': time_str
                })
            
            # 按时间排序并限制数量
            activities.sort(key=lambda x: x['time'])
            return activities[:5]
            
        except Exception as e:
            logger.error(f"获取最近活动失败: {e}")
            return []


# 创建自定义管理站点实例
admin_site = CosyVoiceAdminSite(name='cosyvoice_admin')


def get_admin_dashboard_context():
    """获取管理后台仪表板上下文数据"""
    return admin_site.get_dashboard_stats(), admin_site.get_recent_activities()
