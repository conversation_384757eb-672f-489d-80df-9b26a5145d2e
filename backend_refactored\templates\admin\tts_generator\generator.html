<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>TTS生成器 - CosyVoice管理后台</title>
    <style>
        body {
            margin: 0;
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, 'Helvetica Neue', Arial, sans-serif;
            background: #f5f5f5;
            color: #333;
        }
        
        .tts-container {
            padding: 20px;
            max-width: 1200px;
            margin: 0 auto;
        }
        
        .tts-header {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 30px;
            border-radius: 12px;
            margin-bottom: 30px;
            text-align: center;
        }
        
        .tts-header h1 {
            margin: 0 0 10px 0;
            font-size: 28px;
            font-weight: 600;
        }
        
        .tts-header p {
            margin: 0;
            opacity: 0.9;
            font-size: 16px;
        }
        
        .tts-modes {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 20px;
            margin-bottom: 30px;
        }
        
        .mode-card {
            background: white;
            padding: 24px;
            border-radius: 12px;
            box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
            border: 1px solid #f0f0f0;
            transition: transform 0.3s ease, box-shadow 0.3s ease;
        }
        
        .mode-card:hover {
            transform: translateY(-2px);
            box-shadow: 0 4px 16px rgba(0, 0, 0, 0.15);
        }
        
        .mode-card.active {
            border-color: #1890ff;
            box-shadow: 0 4px 16px rgba(24, 144, 255, 0.15);
        }
        
        .mode-header {
            display: flex;
            align-items: center;
            margin-bottom: 16px;
        }
        
        .mode-icon {
            width: 40px;
            height: 40px;
            border-radius: 8px;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 20px;
            margin-right: 12px;
        }
        
        .mode-title {
            font-size: 18px;
            font-weight: 600;
            margin: 0;
            color: #262626;
        }
        
        .mode-description {
            color: #8c8c8c;
            font-size: 14px;
            margin-bottom: 16px;
            line-height: 1.5;
        }
        
        .mode-button {
            width: 100%;
            padding: 12px 24px;
            background: #1890ff;
            color: white;
            border: none;
            border-radius: 6px;
            font-size: 14px;
            cursor: pointer;
            transition: background 0.3s;
        }
        
        .mode-button:hover {
            background: #40a9ff;
        }
        
        .mode-button:disabled {
            background: #d9d9d9;
            cursor: not-allowed;
        }
        
        .generation-form {
            background: white;
            padding: 24px;
            border-radius: 12px;
            box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
            border: 1px solid #f0f0f0;
            margin-bottom: 30px;
            display: none;
        }
        
        .generation-form.active {
            display: block;
        }
        
        .form-group {
            margin-bottom: 20px;
        }
        
        .form-label {
            display: block;
            margin-bottom: 8px;
            font-weight: 500;
            color: #262626;
        }
        
        .form-input {
            width: 100%;
            padding: 12px;
            border: 1px solid #d9d9d9;
            border-radius: 6px;
            font-size: 14px;
            transition: border-color 0.3s;
        }
        
        .form-input:focus {
            outline: none;
            border-color: #1890ff;
            box-shadow: 0 0 0 2px rgba(24, 144, 255, 0.2);
        }
        
        .form-textarea {
            min-height: 100px;
            resize: vertical;
        }
        
        .form-select {
            width: 100%;
            padding: 12px;
            border: 1px solid #d9d9d9;
            border-radius: 6px;
            font-size: 14px;
            background: white;
        }
        
        .form-file {
            width: 100%;
            padding: 12px;
            border: 1px solid #d9d9d9;
            border-radius: 6px;
            font-size: 14px;
            cursor: pointer;
            transition: border-color 0.3s;
        }

        .form-file:hover {
            border-color: #40a9ff;
        }

        .form-file:focus {
            outline: none;
            border-color: #1890ff;
            box-shadow: 0 0 0 2px rgba(24, 144, 255, 0.2);
        }

        /* 自定义文件上传样式 */
        .file-upload-wrapper {
            position: relative;
            display: inline-block;
            width: 100%;
        }

        .file-upload-input {
            position: absolute;
            left: -9999px;
            opacity: 0;
        }

        .file-upload-button {
            display: inline-block;
            width: 100%;
            padding: 12px;
            border: 2px dashed #d9d9d9;
            border-radius: 6px;
            text-align: center;
            cursor: pointer;
            transition: all 0.3s;
            background: #fafafa;
            color: #666;
        }

        .file-upload-button:hover {
            border-color: #40a9ff;
            background: #f0f8ff;
            color: #1890ff;
        }

        .file-upload-button.has-file {
            border-color: #52c41a;
            background: #f6ffed;
            color: #52c41a;
        }

        .file-upload-icon {
            font-size: 24px;
            margin-bottom: 8px;
            display: block;
        }

        .file-upload-text {
            font-size: 14px;
            margin-bottom: 4px;
        }

        .file-upload-hint {
            font-size: 12px;
            color: #8c8c8c;
        }
        
        .generate-button {
            width: 100%;
            padding: 16px 24px;
            background: #52c41a;
            color: white;
            border: none;
            border-radius: 6px;
            font-size: 16px;
            font-weight: 600;
            cursor: pointer;
            transition: background 0.3s;
        }
        
        .generate-button:hover {
            background: #73d13d;
        }
        
        .generate-button:disabled {
            background: #d9d9d9;
            cursor: not-allowed;
        }
        
        .result-section {
            background: white;
            padding: 24px;
            border-radius: 12px;
            box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
            border: 1px solid #f0f0f0;
            display: none;
        }
        
        .result-section.active {
            display: block;
        }
        
        .result-header {
            display: flex;
            align-items: center;
            margin-bottom: 16px;
        }
        
        .result-status {
            padding: 4px 12px;
            border-radius: 12px;
            font-size: 12px;
            font-weight: 500;
            margin-right: 12px;
        }
        
        .status-processing {
            background: #fff7e6;
            color: #fa8c16;
        }
        
        .status-completed {
            background: #f6ffed;
            color: #52c41a;
        }
        
        .status-failed {
            background: #fff2f0;
            color: #ff4d4f;
        }
        
        .audio-player {
            width: 100%;
            margin-top: 16px;
        }
        
        .back-to-admin {
            position: fixed;
            top: 20px;
            right: 20px;
            background: #1890ff;
            color: white;
            padding: 10px 20px;
            border-radius: 6px;
            text-decoration: none;
            font-size: 14px;
            box-shadow: 0 2px 8px rgba(24, 144, 255, 0.3);
            transition: all 0.3s;
        }
        
        .back-to-admin:hover {
            background: #40a9ff;
            color: white;
            text-decoration: none;
            transform: translateY(-1px);
            box-shadow: 0 4px 12px rgba(24, 144, 255, 0.4);
        }
        
        /* 响应式设计 */
        @media (max-width: 768px) {
            .tts-modes {
                grid-template-columns: 1fr;
            }
            
            .tts-container {
                padding: 10px;
            }
        }
    </style>
</head>
<body>
    <!-- 返回管理后台按钮 -->
    <a href="/admin/" class="back-to-admin">← 返回管理后台</a>
    
    <div class="tts-container">
        <!-- 头部区域 -->
        <div class="tts-header">
            <h1>🎵 TTS语音生成器</h1>
            <p>选择生成模式，快速创建高质量的语音合成</p>
        </div>
        
        <!-- 生成模式选择 -->
        <div class="tts-modes">
            <div class="mode-card" data-mode="sft">
                <div class="mode-header">
                    <div class="mode-icon" style="background: #e6f7ff; color: #1890ff;">🎵</div>
                    <h3 class="mode-title">SFT模式</h3>
                </div>
                <p class="mode-description">使用预训练说话人进行快速语音合成，支持多种语言和音色</p>
                <button class="mode-button" onclick="selectMode('sft')">选择此模式</button>
            </div>
            
            <div class="mode-card" data-mode="zero_shot">
                <div class="mode-header">
                    <div class="mode-icon" style="background: #f6ffed; color: #52c41a;">🎯</div>
                    <h3 class="mode-title">零样本克隆</h3>
                </div>
                <p class="mode-description">上传3-30秒参考音频，即可克隆任意音色进行语音合成</p>
                <button class="mode-button" onclick="selectMode('zero_shot')">选择此模式</button>
            </div>
            
            <div class="mode-card" data-mode="cross_lingual">
                <div class="mode-header">
                    <div class="mode-icon" style="background: #fff2e8; color: #fa8c16;">🌍</div>
                    <h3 class="mode-title">跨语种复刻</h3>
                </div>
                <p class="mode-description">使用一种语言的音频，生成另一种语言的语音</p>
                <button class="mode-button" onclick="selectMode('cross_lingual')">选择此模式</button>
            </div>
            
            <div class="mode-card" data-mode="instruct">
                <div class="mode-header">
                    <div class="mode-icon" style="background: #f9f0ff; color: #722ed1;">🎭</div>
                    <h3 class="mode-title">指令控制</h3>
                </div>
                <p class="mode-description">通过自然语言指令控制语音的情感、语调和风格</p>
                <button class="mode-button" onclick="selectMode('instruct')">选择此模式</button>
            </div>
        </div>
        
        <!-- SFT模式表单 -->
        <div id="sft-form" class="generation-form">
            <h3>SFT模式语音合成</h3>
            <form onsubmit="generateTTS('sft', event)">
                <div class="form-group">
                    <label class="form-label">要合成的文本 *</label>
                    <textarea name="text_content" class="form-input form-textarea" placeholder="请输入要合成的文本内容..." required></textarea>
                </div>
                <div class="form-group">
                    <label class="form-label">说话人 *</label>
                    <select name="speaker_id" class="form-select" required>
                        <option value="">请选择说话人</option>
                        <option value="中文女">中文女声</option>
                        <option value="中文男">中文男声</option>
                        <option value="英文女">英文女声</option>
                        <option value="英文男">英文男声</option>
                    </select>
                </div>
                <button type="submit" class="generate-button">🎵 开始生成</button>
            </form>
        </div>
        
        <!-- 零样本克隆表单 -->
        <div id="zero_shot-form" class="generation-form">
            <h3>零样本语音克隆</h3>
            <form onsubmit="generateTTS('zero_shot', event)">
                <div class="form-group">
                    <label class="form-label">要合成的文本 *</label>
                    <textarea name="text_content" class="form-input form-textarea" placeholder="请输入要合成的文本内容..." required></textarea>
                </div>
                <div class="form-group">
                    <label class="form-label">参考音频 *</label>
                    <div class="file-upload-wrapper">
                        <input type="file" name="reference_audio" class="file-upload-input" id="zero-shot-audio" accept=".wav,.mp3,.flac" required>
                        <label for="zero-shot-audio" class="file-upload-button" id="zero-shot-upload-btn">
                            <span class="file-upload-icon">📁</span>
                            <div class="file-upload-text">点击选择音频文件</div>
                            <div class="file-upload-hint">支持wav/mp3/flac格式，建议3-30秒</div>
                        </label>
                    </div>
                </div>
                <div class="form-group">
                    <label class="form-label">参考音频对应的文本 *</label>
                    <textarea name="reference_text" class="form-input form-textarea" placeholder="请输入参考音频中说话的内容..." required></textarea>
                </div>
                <button type="submit" class="generate-button">🎯 开始克隆</button>
            </form>
        </div>
        
        <!-- 跨语种复刻表单 -->
        <div id="cross_lingual-form" class="generation-form">
            <h3>跨语种语音复刻</h3>
            <form onsubmit="generateTTS('cross_lingual', event)">
                <div class="form-group">
                    <label class="form-label">要合成的文本 *</label>
                    <textarea name="text_content" class="form-input form-textarea" placeholder="请输入要合成的文本内容..." required></textarea>
                </div>
                <div class="form-group">
                    <label class="form-label">参考音频 *</label>
                    <div class="file-upload-wrapper">
                        <input type="file" name="reference_audio" class="file-upload-input" id="cross-lingual-audio" accept=".wav,.mp3,.flac" required>
                        <label for="cross-lingual-audio" class="file-upload-button" id="cross-lingual-upload-btn">
                            <span class="file-upload-icon">📁</span>
                            <div class="file-upload-text">点击选择音频文件</div>
                            <div class="file-upload-hint">支持wav/mp3/flac格式</div>
                        </label>
                    </div>
                </div>
                <button type="submit" class="generate-button">🌍 开始复刻</button>
            </form>
        </div>
        
        <!-- 指令控制表单 -->
        <div id="instruct-form" class="generation-form">
            <h3>指令控制语音合成</h3>
            <form onsubmit="generateTTS('instruct', event)">
                <div class="form-group">
                    <label class="form-label">要合成的文本 *</label>
                    <textarea name="text_content" class="form-input form-textarea" placeholder="请输入要合成的文本内容..." required></textarea>
                </div>
                <div class="form-group">
                    <label class="form-label">基础说话人 *</label>
                    <select name="speaker_id" class="form-select" required>
                        <option value="">请选择说话人</option>
                        <option value="中文女">中文女声</option>
                        <option value="中文男">中文男声</option>
                    </select>
                </div>
                <div class="form-group">
                    <label class="form-label">控制指令 *</label>
                    <textarea name="instruct_text" class="form-input form-textarea" placeholder="例如：用温柔的语气说、用激动的语气说、说得慢一点..." required></textarea>
                </div>
                <button type="submit" class="generate-button">🎭 开始生成</button>
            </form>
        </div>
        
        <!-- 结果显示区域 -->
        <div id="result-section" class="result-section">
            <div class="result-header">
                <span id="result-status" class="result-status status-processing">处理中</span>
                <span id="result-text">正在生成语音，请稍候...</span>
            </div>
            <div id="result-content">
                <!-- 结果内容将在这里显示 -->
            </div>
        </div>
    </div>

    <script>
        let currentMode = null;
        
        function selectMode(mode) {
            // 重置所有模式卡片
            document.querySelectorAll('.mode-card').forEach(card => {
                card.classList.remove('active');
            });
            
            // 隐藏所有表单
            document.querySelectorAll('.generation-form').forEach(form => {
                form.classList.remove('active');
            });
            
            // 激活选中的模式
            document.querySelector(`[data-mode="${mode}"]`).classList.add('active');
            document.getElementById(`${mode}-form`).classList.add('active');
            
            // 隐藏结果区域
            document.getElementById('result-section').classList.remove('active');
            
            currentMode = mode;
        }
        
        async function generateTTS(mode, event) {
            event.preventDefault();
            
            const form = event.target;
            const formData = new FormData(form);
            formData.append('generation_type', mode);
            
            // 显示结果区域
            const resultSection = document.getElementById('result-section');
            const resultStatus = document.getElementById('result-status');
            const resultText = document.getElementById('result-text');
            const resultContent = document.getElementById('result-content');
            
            resultSection.classList.add('active');
            resultStatus.className = 'result-status status-processing';
            resultStatus.textContent = '处理中';
            resultText.textContent = '正在生成语音，请稍候...';
            resultContent.innerHTML = '';
            
            // 禁用生成按钮
            const submitButton = form.querySelector('button[type="submit"]');
            submitButton.disabled = true;
            submitButton.textContent = '生成中...';
            
            try {
                const response = await fetch('/admin/tts-generator/quick-generate/', {
                    method: 'POST',
                    body: formData,
                    headers: {
                        'X-CSRFToken': getCookie('csrftoken')
                    }
                });
                
                const result = await response.json();
                
                if (result.success) {
                    resultStatus.className = 'result-status status-completed';
                    resultStatus.textContent = '生成成功';
                    resultText.textContent = `处理时间: ${result.processing_time}`;

                    // 检查音频信息并生成播放器
                    const audioUrl = result.audio_url || (result.audio_info && result.audio_info.url);
                    const generationId = result.generation_id || (result.audio_info && result.audio_info.file_id);

                    if (audioUrl) {
                        resultContent.innerHTML = `
                            <audio controls class="audio-player">
                                <source src="${audioUrl}" type="audio/wav">
                                <source src="${audioUrl}" type="audio/mpeg">
                                <source src="${audioUrl}" type="audio/flac">
                                您的浏览器不支持音频播放。
                            </audio>
                            <p style="margin-top: 12px; color: #8c8c8c; font-size: 14px;">
                                生成ID: ${generationId || 'N/A'}
                            </p>
                        `;

                        // 添加音频加载错误处理
                        const audioElement = resultContent.querySelector('audio');
                        if (audioElement) {
                            audioElement.addEventListener('error', function(e) {
                                console.error('音频加载失败:', e);
                                resultContent.innerHTML = `
                                    <div style="padding: 16px; background: #fff2f0; border: 1px solid #ffccc7; border-radius: 6px; color: #ff4d4f;">
                                        ❌ 音频文件加载失败，请检查文件是否存在
                                    </div>
                                    <p style="margin-top: 12px; color: #8c8c8c; font-size: 14px;">
                                        生成ID: ${generationId || 'N/A'}
                                    </p>
                                `;
                            });

                            audioElement.addEventListener('loadeddata', function() {
                                console.log('音频加载成功:', audioUrl);
                            });
                        }
                    } else {
                        resultContent.innerHTML = `
                            <div style="padding: 16px; background: #fff7e6; border: 1px solid #ffd591; border-radius: 6px; color: #d46b08;">
                                ⚠️ 音频生成成功，但无法获取播放链接
                            </div>
                            <p style="margin-top: 12px; color: #8c8c8c; font-size: 14px;">
                                生成ID: ${generationId || 'N/A'}
                            </p>
                        `;
                    }
                } else {
                    resultStatus.className = 'result-status status-failed';
                    resultStatus.textContent = '生成失败';
                    resultText.textContent = result.error || '未知错误';
                }
                
            } catch (error) {
                resultStatus.className = 'result-status status-failed';
                resultStatus.textContent = '生成失败';
                resultText.textContent = '网络错误: ' + error.message;
            } finally {
                // 恢复生成按钮
                submitButton.disabled = false;
                submitButton.textContent = submitButton.textContent.replace('生成中...', '开始生成');
            }
        }
        
        function getCookie(name) {
            let cookieValue = null;
            if (document.cookie && document.cookie !== '') {
                const cookies = document.cookie.split(';');
                for (let i = 0; i < cookies.length; i++) {
                    const cookie = cookies[i].trim();
                    if (cookie.substring(0, name.length + 1) === (name + '=')) {
                        cookieValue = decodeURIComponent(cookie.substring(name.length + 1));
                        break;
                    }
                }
            }
            return cookieValue;
        }
        
        // 文件上传处理
        function setupFileUpload() {
            // 零样本克隆文件上传
            const zeroShotInput = document.getElementById('zero-shot-audio');
            const zeroShotBtn = document.getElementById('zero-shot-upload-btn');

            if (zeroShotInput && zeroShotBtn) {
                zeroShotInput.addEventListener('change', function(e) {
                    const file = e.target.files[0];
                    if (file) {
                        zeroShotBtn.classList.add('has-file');
                        zeroShotBtn.innerHTML = `
                            <span class="file-upload-icon">✅</span>
                            <div class="file-upload-text">已选择: ${file.name}</div>
                            <div class="file-upload-hint">文件大小: ${(file.size / 1024 / 1024).toFixed(2)} MB</div>
                        `;
                    } else {
                        zeroShotBtn.classList.remove('has-file');
                        zeroShotBtn.innerHTML = `
                            <span class="file-upload-icon">📁</span>
                            <div class="file-upload-text">点击选择音频文件</div>
                            <div class="file-upload-hint">支持wav/mp3/flac格式，建议3-30秒</div>
                        `;
                    }
                });
            }

            // 跨语种复刻文件上传
            const crossLingualInput = document.getElementById('cross-lingual-audio');
            const crossLingualBtn = document.getElementById('cross-lingual-upload-btn');

            if (crossLingualInput && crossLingualBtn) {
                crossLingualInput.addEventListener('change', function(e) {
                    const file = e.target.files[0];
                    if (file) {
                        crossLingualBtn.classList.add('has-file');
                        crossLingualBtn.innerHTML = `
                            <span class="file-upload-icon">✅</span>
                            <div class="file-upload-text">已选择: ${file.name}</div>
                            <div class="file-upload-hint">文件大小: ${(file.size / 1024 / 1024).toFixed(2)} MB</div>
                        `;
                    } else {
                        crossLingualBtn.classList.remove('has-file');
                        crossLingualBtn.innerHTML = `
                            <span class="file-upload-icon">📁</span>
                            <div class="file-upload-text">点击选择音频文件</div>
                            <div class="file-upload-hint">支持wav/mp3/flac格式</div>
                        `;
                    }
                });
            }
        }

        // 页面加载完成后默认选择SFT模式并设置文件上传
        document.addEventListener('DOMContentLoaded', function() {
            selectMode('sft');
            setupFileUpload();
        });
    </script>
</body>
</html>
