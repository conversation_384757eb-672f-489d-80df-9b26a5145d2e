"""
日志配置和管理
"""
import os
import sys
from pathlib import Path
from loguru import logger
from django.conf import settings


def setup_logging():
    """设置Loguru日志配置"""
    
    # 移除默认的日志处理器
    logger.remove()
    
    # 确保日志目录存在
    log_dir = Path(settings.BASE_DIR) / 'logs'
    log_dir.mkdir(exist_ok=True)
    
    # 控制台日志 - 开发环境
    if settings.DEBUG:
        logger.add(
            sys.stdout,
            format="<green>{time:YYYY-MM-DD HH:mm:ss}</green> | "
                   "<level>{level: <8}</level> | "
                   "<cyan>{name}</cyan>:<cyan>{function}</cyan>:<cyan>{line}</cyan> | "
                   "<level>{message}</level>",
            level="DEBUG",
            colorize=True
        )
    
    # 应用日志文件
    logger.add(
        log_dir / "app.log",
        format="{time:YYYY-MM-DD HH:mm:ss} | {level: <8} | {name}:{function}:{line} | {message}",
        level="INFO",
        rotation="100 MB",
        retention="30 days",
        compression="zip",
        encoding="utf-8"
    )
    
    # 错误日志文件
    logger.add(
        log_dir / "error.log",
        format="{time:YYYY-MM-DD HH:mm:ss} | {level: <8} | {name}:{function}:{line} | {message}",
        level="ERROR",
        rotation="50 MB",
        retention="90 days",
        compression="zip",
        encoding="utf-8"
    )
    
    # API访问日志
    logger.add(
        log_dir / "api.log",
        format="{time:YYYY-MM-DD HH:mm:ss} | {level: <8} | {extra[request_id]} | {message}",
        level="INFO",
        rotation="200 MB",
        retention="30 days",
        compression="zip",
        encoding="utf-8",
        filter=lambda record: record["extra"].get("log_type") == "api"
    )
    
    # 任务日志
    logger.add(
        log_dir / "tasks.log",
        format="{time:YYYY-MM-DD HH:mm:ss} | {level: <8} | {extra[task_id]} | {message}",
        level="INFO",
        rotation="100 MB",
        retention="30 days",
        compression="zip",
        encoding="utf-8",
        filter=lambda record: record["extra"].get("log_type") == "task"
    )
    
    # 安全日志
    logger.add(
        log_dir / "security.log",
        format="{time:YYYY-MM-DD HH:mm:ss} | {level: <8} | {extra[ip]} | {message}",
        level="WARNING",
        rotation="50 MB",
        retention="180 days",
        compression="zip",
        encoding="utf-8",
        filter=lambda record: record["extra"].get("log_type") == "security"
    )
    
    # 性能日志
    logger.add(
        log_dir / "performance.log",
        format="{time:YYYY-MM-DD HH:mm:ss} | {level: <8} | {extra[duration]} | {message}",
        level="INFO",
        rotation="100 MB",
        retention="7 days",
        compression="zip",
        encoding="utf-8",
        filter=lambda record: record["extra"].get("log_type") == "performance"
    )


def get_api_logger():
    """获取API日志记录器"""
    return logger.bind(log_type="api")


def get_task_logger(task_id=None):
    """获取任务日志记录器"""
    return logger.bind(log_type="task", task_id=task_id or "unknown")


def get_security_logger(ip=None):
    """获取安全日志记录器"""
    return logger.bind(log_type="security", ip=ip or "unknown")


def get_performance_logger():
    """获取性能日志记录器"""
    return logger.bind(log_type="performance")


class LoggerMixin:
    """日志记录器混入类"""
    
    @property
    def logger(self):
        """获取类专用的日志记录器"""
        return logger.bind(class_name=self.__class__.__name__)


def log_api_request(request, response=None, duration=None, error=None):
    """记录API请求日志"""
    api_logger = get_api_logger()
    
    log_data = {
        'method': request.method,
        'path': request.path,
        'user': str(request.user) if hasattr(request, 'user') and request.user.is_authenticated else 'Anonymous',
        'ip': get_client_ip(request),
        'user_agent': request.META.get('HTTP_USER_AGENT', ''),
    }
    
    if response:
        log_data['status_code'] = response.status_code
    
    if duration:
        log_data['duration'] = f"{duration:.3f}s"
    
    if error:
        log_data['error'] = str(error)
        api_logger.error(f"API请求失败: {log_data}")
    else:
        api_logger.info(f"API请求: {log_data}")


def log_task_event(task_id, event, message, level="info", **extra):
    """记录任务事件日志"""
    task_logger = get_task_logger(task_id)
    
    log_data = {
        'event': event,
        'message': message,
        **extra
    }
    
    getattr(task_logger, level)(f"任务事件: {log_data}")


def log_security_event(event_type, message, ip=None, user=None, level="warning", **extra):
    """记录安全事件日志"""
    security_logger = get_security_logger(ip)
    
    log_data = {
        'event_type': event_type,
        'message': message,
        'user': str(user) if user else 'Anonymous',
        **extra
    }
    
    getattr(security_logger, level)(f"安全事件: {log_data}")


def log_performance_metric(metric_name, value, unit="", context=None, **extra):
    """记录性能指标日志"""
    perf_logger = get_performance_logger()
    
    log_data = {
        'metric': metric_name,
        'value': value,
        'unit': unit,
        'context': context or {},
        **extra
    }
    
    perf_logger.info(f"性能指标: {log_data}")


def get_client_ip(request):
    """获取客户端IP地址"""
    x_forwarded_for = request.META.get('HTTP_X_FORWARDED_FOR')
    if x_forwarded_for:
        ip = x_forwarded_for.split(',')[0].strip()
    else:
        ip = request.META.get('REMOTE_ADDR')
    return ip


class StructuredLogger:
    """结构化日志记录器"""
    
    def __init__(self, name):
        self.name = name
        self.logger = logger.bind(logger_name=name)
    
    def info(self, message, **context):
        """记录信息日志"""
        self.logger.info(message, **context)
    
    def warning(self, message, **context):
        """记录警告日志"""
        self.logger.warning(message, **context)
    
    def error(self, message, **context):
        """记录错误日志"""
        self.logger.error(message, **context)
    
    def debug(self, message, **context):
        """记录调试日志"""
        self.logger.debug(message, **context)
    
    def critical(self, message, **context):
        """记录严重错误日志"""
        self.logger.critical(message, **context)


# 预定义的结构化日志记录器
auth_logger = StructuredLogger("authentication")
user_logger = StructuredLogger("user_management")
task_logger = StructuredLogger("task_management")
file_logger = StructuredLogger("file_management")
system_logger = StructuredLogger("system")
