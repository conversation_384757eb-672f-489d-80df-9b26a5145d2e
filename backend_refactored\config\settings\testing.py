"""
测试环境配置
"""
from .base import *
from dotenv import load_dotenv

# 加载.env文件
load_dotenv()

# 调试模式
DEBUG = True

# 测试数据库 - 使用MySQL（可以创建测试专用数据库）
DATABASES = {
    'default': {
        'ENGINE': 'django.db.backends.mysql',
        'NAME': os.environ.get('TEST_DB_NAME', 'cvapi_test'),
        'USER': os.environ.get('DB_USER', 'cvapi'),
        'PASSWORD': os.environ.get('DB_PASSWORD', 'NsF3wEe75HNPDeYj'),
        'HOST': os.environ.get('DB_HOST', '*************'),
        'PORT': os.environ.get('DB_PORT', '3306'),
        'OPTIONS': {
            'charset': 'utf8mb4',
            'init_command': "SET sql_mode='STRICT_TRANS_TABLES'",
        },
    }
}

# 缓存配置 - 测试环境使用本地内存
CACHES = {
    'default': {
        'BACKEND': 'django.core.cache.backends.locmem.LocMemCache',
    }
}

# Celery配置 - 测试环境同步执行
CELERY_TASK_ALWAYS_EAGER = True
CELERY_TASK_EAGER_PROPAGATES = True

# 邮件配置 - 测试环境使用内存后端
EMAIL_BACKEND = 'django.core.mail.backends.locmem.EmailBackend'

# 密码验证 - 测试环境简化
AUTH_PASSWORD_VALIDATORS = []

# 日志配置 - 测试环境最小化
LOGGING = {
    'version': 1,
    'disable_existing_loggers': False,
    'handlers': {
        'console': {
            'class': 'logging.StreamHandler',
        },
    },
    'root': {
        'handlers': ['console'],
        'level': 'WARNING',
    },
}

# 静态文件 - 测试环境
STATICFILES_STORAGE = 'django.contrib.staticfiles.storage.StaticFilesStorage'

# 媒体文件 - 测试环境使用临时目录
import tempfile
MEDIA_ROOT = tempfile.mkdtemp()

# CosyVoice配置 - 测试环境使用模拟
COSYVOICE_MODEL_DIR = 'tests/fixtures/mock_model'
COSYVOICE_OUTPUT_DIR = tempfile.mkdtemp()

# 测试配置
TEST_RUNNER = 'django.test.runner.DiscoverRunner'

# 禁用迁移以加速测试
class DisableMigrations:
    def __contains__(self, item):
        return True
    
    def __getitem__(self, item):
        return None

MIGRATION_MODULES = DisableMigrations()

# 密码哈希 - 测试环境使用快速哈希
PASSWORD_HASHERS = [
    'django.contrib.auth.hashers.MD5PasswordHasher',
]
