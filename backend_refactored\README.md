# CosyVoice API任务管理系统 - Django重构版本

## 项目概述

这是CosyVoice TTS系统的Django重构版本，在保持原有推理功能完全兼容的基础上，新增了企业级的用户管理、任务管理、文件管理和监控统计功能。

## 🚀 新增功能

### 用户管理
- 用户注册/登录/权限管理
- JWT Token + API Key双重认证
- 基于角色的访问控制(RBAC)
- API调用配额管理

### 任务管理
- API调用记录和状态跟踪
- 异步任务队列处理(Celery)
- 任务重试和错误处理
- 批量操作支持

### 文件管理
- 文件生命周期管理
- 访问权限控制
- 存储配额管理
- 阿里云OSS集成

### 监控统计
- 系统性能监控
- 用户行为分析
- 告警机制
- 运营数据统计

## 🛠️ 技术架构

### 技术栈
- **Web框架**: Django 4.2+ + Django REST framework
- **推理引擎**: CosyVoice Core (保持不变)
- **数据库**: MySQL 8.0+
- **缓存/队列**: Redis 7+
- **任务队列**: Celery 5+
- **认证**: JWT + Casbin + API Key
- **WebSocket**: Django Channels
- **日志**: Loguru
- **对象存储**: 阿里云OSS

### 架构设计
```
┌─────────────────────────────────────────────────────────────┐
│  接入层: Nginx + API Gateway + Load Balancer               │
├─────────────────────────────────────────────────────────────┤
│  应用层: Django + DRF + Task Manager + User Manager        │
├─────────────────────────────────────────────────────────────┤
│  业务层: CosyVoice Core + File Manager + Monitor Service   │
├─────────────────────────────────────────────────────────────┤
│  数据层: MySQL + Redis + OSS Storage                       │
└─────────────────────────────────────────────────────────────┘
```

## 📁 项目结构

```
backend_refactored/
├── manage.py                   # Django管理脚本
├── requirements.txt            # 依赖包列表
├── config/                     # 项目配置
│   ├── __init__.py
│   ├── settings/
│   │   ├── __init__.py
│   │   ├── base.py            # 基础配置
│   │   ├── development.py     # 开发环境配置
│   │   ├── production.py      # 生产环境配置
│   │   └── testing.py         # 测试环境配置
│   ├── urls.py                # 主URL配置
│   ├── wsgi.py                # WSGI配置
│   └── asgi.py                # ASGI配置
├── apps/                       # Django应用
│   ├── __init__.py
│   ├── authentication/        # 认证应用
│   ├── users/                 # 用户管理应用
│   ├── tasks/                 # 任务管理应用
│   ├── files/                 # 文件管理应用
│   └── common/                # 公共应用
├── static/                    # 静态文件
├── media/                     # 媒体文件
├── logs/                      # 日志文件
├── celery_app.py             # Celery配置
├── docker-compose.yml        # Docker配置
└── README.md                 # 项目说明
```

## 🔧 安装和部署

### 环境要求
- Python 3.10+
- Django 4.2+
- MySQL 8.0+
- Redis 7+
- Docker & Docker Compose (可选)

### 快速启动

#### 1. 本地开发环境
```bash
# 1. 克隆项目
git clone <repository>
cd backend_refactored

# 2. 创建虚拟环境
python -m venv venv
source venv/bin/activate  # Linux/Mac
# 或
venv\Scripts\activate     # Windows

# 3. 安装依赖
pip install -r requirements.txt

# 4. 配置环境变量
cp .env.example .env
# 编辑 .env 文件，配置数据库连接信息

# 5. 数据库迁移
python manage.py makemigrations
python manage.py migrate

# 6. 创建超级用户
python manage.py createsuperuser

# 7. 启动开发服务器
python manage.py runserver 0.0.0.0:8000

python manage.py runserver 0.0.0.0:8000 --settings=config.settings.demo

python manage.py runserver 0.0.0.0:8000 --settings=config.settings.minimal



```

#### 2. Docker部署
```bash
# 使用Docker Compose一键启动
docker-compose up -d

# 查看服务状态
docker-compose ps

# 查看日志
docker-compose logs -f web
```

## 📖 API文档

### 兼容性API (保持不变)
- `POST /inference_sft` - SFT模式推理
- `POST /inference_zero_shot` - 零样本语音克隆
- `POST /inference_cross_lingual` - 跨语种复刻
- `POST /inference_instruct` - 自然语言控制
- `GET /list_speakers` - 获取说话人列表
- `GET /reference_audios` - 获取参考音频

### 新增管理API
- `POST /api/v1/auth/register` - 用户注册
- `POST /api/v1/auth/login` - 用户登录
- `GET /api/v1/tasks/` - 获取任务列表
- `GET /api/v1/users/profile/` - 获取用户信息
- `GET /api/v1/files/` - 获取文件列表

详细API文档: http://localhost:8000/docs/

## 🔐 认证方式

### 1. JWT Token认证 (Web用户)
```bash
# 登录获取Token
curl -X POST "http://localhost:8000/api/v1/auth/login/" \
  -H "Content-Type: application/json" \
  -d '{"email": "<EMAIL>", "password": "password"}'

# 使用Token访问API
curl -X GET "http://localhost:8000/api/v1/tasks/" \
  -H "Authorization: Bearer <your_token>"
```

### 2. API Key认证 (程序调用)
```bash
# 使用API Key访问
curl -X POST "http://localhost:8000/inference_sft" \
  -H "X-API-Key: <your_api_key>" \
  -F "tts_text=你好世界" \
  -F "spk_id=中文女"
```

### 3. 向后兼容 (无认证)
```bash
# 原有调用方式仍然有效
curl -X POST "http://localhost:8000/inference_sft" \
  -F "tts_text=你好世界" \
  -F "spk_id=中文女"
```

## 🔄 迁移指南

### 现有用户迁移
1. 现有API调用无需修改，保持100%兼容
2. 可选择注册账户获得更多功能
3. 可选择使用API Key获得更好的管理体验

### 数据迁移
- 现有音频文件自动迁移到新的文件管理系统
- 文件访问链接保持有效
- 配置数据平滑迁移

## 📊 监控和运维

### 健康检查
- `GET /health/` - 系统健康检查
- `GET /health/db/` - 数据库健康检查
- `GET /health/redis/` - Redis健康检查
- `GET /health/celery/` - Celery健康检查

### 日志管理
- 结构化日志输出
- 错误日志聚合
- 操作审计日志
- 性能分析日志

## 🧪 测试

```bash
# 运行测试
python manage.py test

# 运行特定应用的测试
python manage.py test apps.users

# 生成测试覆盖率报告
coverage run --source='.' manage.py test
coverage report
```

## 📄 许可证

本项目采用 MIT 许可证 - 查看 [LICENSE](LICENSE) 文件了解详情

---

**重构版本**: v2.0.0  
**兼容版本**: v1.0.0 (完全兼容)  
**更新日期**: 2025-01-24
