#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
CosyVoice客户端
用于与CosyVoice模型服务通信
"""

import requests
import logging
from typing import Dict, Any, Optional
from django.conf import settings

logger = logging.getLogger(__name__)


class CosyVoiceClient:
    """CosyVoice客户端"""
    
    def __init__(self):
        self.service_port = getattr(settings, 'COSYVOICE_SERVICE_PORT', 8001)
        self.service_url = f"http://localhost:{self.service_port}"
        self.timeout = 30
    
    def is_service_available(self) -> bool:
        """检查服务是否可用"""
        try:
            response = requests.get(f"{self.service_url}/health", timeout=5)
            return response.status_code == 200
        except:
            return False
    
    def inference_sft(self, tts_text: str, spk_id: str = "中文女") -> Dict[str, Any]:
        """SFT模式推理"""
        try:
            data = {
                'tts_text': tts_text,
                'spk_id': spk_id
            }
            
            response = requests.post(
                f"{self.service_url}/inference_sft",
                data=data,
                timeout=self.timeout
            )
            
            response.raise_for_status()
            return response.json()
            
        except requests.exceptions.RequestException as e:
            logger.error(f"SFT推理请求失败: {e}")
            raise RuntimeError(f"CosyVoice服务请求失败: {e}")
    
    def inference_zero_shot(self, tts_text: str, prompt_text: str, prompt_wav_file) -> Dict[str, Any]:
        """零样本语音克隆"""
        try:
            data = {
                'tts_text': tts_text,
                'prompt_text': prompt_text
            }
            
            files = {
                'prompt_wav': prompt_wav_file
            }
            
            response = requests.post(
                f"{self.service_url}/inference_zero_shot",
                data=data,
                files=files,
                timeout=self.timeout
            )
            
            response.raise_for_status()
            return response.json()
            
        except requests.exceptions.RequestException as e:
            logger.error(f"零样本推理请求失败: {e}")
            raise RuntimeError(f"CosyVoice服务请求失败: {e}")
    
    def inference_cross_lingual(self, tts_text: str, prompt_wav_file) -> Dict[str, Any]:
        """跨语言语音合成"""
        try:
            data = {
                'tts_text': tts_text
            }
            
            files = {
                'prompt_wav': prompt_wav_file
            }
            
            response = requests.post(
                f"{self.service_url}/inference_cross_lingual",
                data=data,
                files=files,
                timeout=self.timeout
            )
            
            response.raise_for_status()
            return response.json()
            
        except requests.exceptions.RequestException as e:
            logger.error(f"跨语言推理请求失败: {e}")
            raise RuntimeError(f"CosyVoice服务请求失败: {e}")
    
    def inference_instruct(self, tts_text: str, spk_id: str, instruct_text: str) -> Dict[str, Any]:
        """指令式语音合成"""
        try:
            data = {
                'tts_text': tts_text,
                'spk_id': spk_id,
                'instruct_text': instruct_text
            }
            
            response = requests.post(
                f"{self.service_url}/inference_instruct",
                data=data,
                timeout=self.timeout
            )
            
            response.raise_for_status()
            return response.json()
            
        except requests.exceptions.RequestException as e:
            logger.error(f"指令式推理请求失败: {e}")
            raise RuntimeError(f"CosyVoice服务请求失败: {e}")
    
    def inference_instruct2(self, tts_text: str, spk_id: str, instruct_text: str) -> Dict[str, Any]:
        """高级指令式语音合成"""
        try:
            data = {
                'tts_text': tts_text,
                'spk_id': spk_id,
                'instruct_text': instruct_text
            }
            
            response = requests.post(
                f"{self.service_url}/inference_instruct2",
                data=data,
                timeout=self.timeout
            )
            
            response.raise_for_status()
            return response.json()
            
        except requests.exceptions.RequestException as e:
            logger.error(f"高级指令式推理请求失败: {e}")
            raise RuntimeError(f"CosyVoice服务请求失败: {e}")
    
    def get_speakers(self) -> Dict[str, Any]:
        """获取说话人列表"""
        try:
            response = requests.get(
                f"{self.service_url}/speakers",
                timeout=self.timeout
            )
            
            response.raise_for_status()
            return response.json()
            
        except requests.exceptions.RequestException as e:
            logger.error(f"获取说话人列表失败: {e}")
            # 返回默认说话人列表
            return {
                'speakers': [
                    '中文女', '中文男', '英文女', '英文男', 
                    '日语女', '日语男', '韩语女', '韩语男',
                    '粤语女', '粤语男'
                ]
            }


# 全局客户端实例
cosyvoice_client = CosyVoiceClient()


def get_cosyvoice_client() -> CosyVoiceClient:
    """获取CosyVoice客户端实例"""
    return cosyvoice_client
