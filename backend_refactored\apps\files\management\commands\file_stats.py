"""
文件统计管理命令
"""
from django.core.management.base import BaseCommand
from django.db.models import Count, Sum
from apps.files.models import AudioFile, FileType, StorageType
from apps.files.services import FileService


class Command(BaseCommand):
    help = '显示文件系统统计信息'
    
    def add_arguments(self, parser):
        parser.add_argument(
            '--detailed',
            action='store_true',
            help='显示详细统计信息'
        )
        
        parser.add_argument(
            '--user',
            type=str,
            help='显示指定用户的统计信息'
        )
    
    def handle(self, *args, **options):
        detailed = options['detailed']
        username = options['user']
        
        self.stdout.write(self.style.SUCCESS("=== 文件系统统计信息 ==="))
        
        # 基础统计
        queryset = AudioFile.objects.filter(is_active=True)
        
        if username:
            from django.contrib.auth import get_user_model
            User = get_user_model()
            try:
                user = User.objects.get(username=username)
                queryset = queryset.filter(user=user)
                self.stdout.write(f"用户: {username}")
            except User.DoesNotExist:
                self.stdout.write(self.style.ERROR(f"用户 {username} 不存在"))
                return
        
        total_files = queryset.count()
        total_size = queryset.aggregate(total=Sum('file_size'))['total'] or 0
        
        self.stdout.write(f"总文件数: {total_files}")
        self.stdout.write(f"总大小: {self.format_size(total_size)}")
        
        if total_files == 0:
            return
        
        # 按类型统计
        self.stdout.write("\n=== 按文件类型统计 ===")
        type_stats = queryset.values('file_type').annotate(
            count=Count('id'),
            size=Sum('file_size')
        ).order_by('-count')
        
        for stat in type_stats:
            file_type = dict(FileType.choices)[stat['file_type']]
            count = stat['count']
            size = self.format_size(stat['size'] or 0)
            self.stdout.write(f"{file_type}: {count} 个文件, {size}")
        
        # 按存储类型统计
        self.stdout.write("\n=== 按存储类型统计 ===")
        storage_stats = queryset.values('storage_type').annotate(
            count=Count('id'),
            size=Sum('file_size')
        ).order_by('-count')
        
        for stat in storage_stats:
            storage_type = dict(StorageType.choices)[stat['storage_type']]
            count = stat['count']
            size = self.format_size(stat['size'] or 0)
            self.stdout.write(f"{storage_type}: {count} 个文件, {size}")
        
        if detailed:
            self.show_detailed_stats(queryset)
    
    def show_detailed_stats(self, queryset):
        """显示详细统计信息"""
        from django.utils import timezone
        from datetime import timedelta
        
        self.stdout.write("\n=== 详细统计信息 ===")
        
        # 最近上传统计
        recent_7d = queryset.filter(
            created_at__gte=timezone.now() - timedelta(days=7)
        ).count()
        recent_30d = queryset.filter(
            created_at__gte=timezone.now() - timedelta(days=30)
        ).count()
        
        self.stdout.write(f"最近7天上传: {recent_7d} 个文件")
        self.stdout.write(f"最近30天上传: {recent_30d} 个文件")
        
        # 过期文件统计
        expired_files = queryset.filter(
            expires_at__lt=timezone.now(),
            auto_delete=True
        ).count()
        
        self.stdout.write(f"过期文件: {expired_files} 个")
        
        # 公开文件统计
        public_files = queryset.filter(is_public=True).count()
        private_files = queryset.filter(is_public=False).count()
        
        self.stdout.write(f"公开文件: {public_files} 个")
        self.stdout.write(f"私有文件: {private_files} 个")
        
        # 访问统计
        total_access = queryset.aggregate(total=Sum('access_count'))['total'] or 0
        avg_access = total_access / queryset.count() if queryset.count() > 0 else 0
        
        self.stdout.write(f"总访问次数: {total_access}")
        self.stdout.write(f"平均访问次数: {avg_access:.1f}")
        
        # 最大文件
        largest_file = queryset.order_by('-file_size').first()
        if largest_file:
            self.stdout.write(f"最大文件: {largest_file.filename} ({self.format_size(largest_file.file_size)})")
        
        # 最长音频
        longest_audio = queryset.filter(duration__isnull=False).order_by('-duration').first()
        if longest_audio:
            duration_str = self.format_duration(longest_audio.duration)
            self.stdout.write(f"最长音频: {longest_audio.filename} ({duration_str})")
    
    def format_size(self, size_bytes):
        """格式化文件大小"""
        if size_bytes == 0:
            return "0 B"
        
        size_names = ["B", "KB", "MB", "GB", "TB"]
        i = 0
        while size_bytes >= 1024 and i < len(size_names) - 1:
            size_bytes /= 1024.0
            i += 1
        
        return f"{size_bytes:.1f} {size_names[i]}"
    
    def format_duration(self, duration_seconds):
        """格式化时长"""
        if not duration_seconds:
            return "未知"
        
        hours = int(duration_seconds // 3600)
        minutes = int((duration_seconds % 3600) // 60)
        seconds = int(duration_seconds % 60)
        
        if hours > 0:
            return f"{hours}:{minutes:02d}:{seconds:02d}"
        else:
            return f"{minutes}:{seconds:02d}"
