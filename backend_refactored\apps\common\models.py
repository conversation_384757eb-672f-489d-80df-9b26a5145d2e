"""
公共模型基类
"""
import uuid
from django.db import models
from django.utils import timezone


class BaseModel(models.Model):
    """基础模型类"""
    id = models.UUIDField(primary_key=True, default=uuid.uuid4, editable=False)
    created_at = models.DateTimeField('创建时间', auto_now_add=True)
    updated_at = models.DateTimeField('更新时间', auto_now=True)
    deleted_at = models.DateTimeField('删除时间', null=True, blank=True)
    is_active = models.BooleanField('是否激活', default=True)

    class Meta:
        abstract = True

    def soft_delete(self):
        """软删除"""
        self.deleted_at = timezone.now()
        self.is_active = False
        self.save()

    def restore(self):
        """恢复删除"""
        self.deleted_at = None
        self.is_active = True
        self.save()


class TimestampedModel(models.Model):
    """时间戳模型"""
    created_at = models.DateTimeField('创建时间', auto_now_add=True)
    updated_at = models.DateTimeField('更新时间', auto_now=True)

    class Meta:
        abstract = True
