#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
Common应用配置
"""

import os
import sys
from django.apps import AppConfig
from django.conf import settings


class CommonConfig(AppConfig):
    """Common应用配置"""

    default_auto_field = 'django.db.models.BigAutoField'
    name = 'apps.common'
    verbose_name = '通用功能'

    def ready(self):
        """应用就绪时的初始化"""
        # 首先初始化loguru日志系统
        from .logger_config import setup_loguru, logger
        setup_loguru()

        # 导入信号处理器
        from . import signals

        # 只在主进程中启动CosyVoice服务，避免在runserver的重载进程中重复启动
        if os.environ.get('RUN_MAIN') == 'true' or 'runserver' not in sys.argv:
            self._initialize_cosyvoice_service()
            self._initialize_cosyvoice_engine()

    def _initialize_cosyvoice_service(self):
        """初始化CosyVoice服务"""
        from .logger_config import logger

        try:
            # 检查是否启用自动启动
            if not getattr(settings, 'COSYVOICE_AUTO_START', True):
                logger.info("CosyVoice自动启动已禁用", category="SYSTEM")
                return

            # 导入服务管理器
            from .cosyvoice_service import get_cosyvoice_service

            service = get_cosyvoice_service()

            # 检查服务是否已运行
            if service.is_service_running():
                logger.info("CosyVoice服务已在运行", category="SYSTEM")
                return

            # 启动服务
            logger.info("正在启动CosyVoice服务...", category="SYSTEM")
            if service.start_service():
                logger.info("CosyVoice服务启动成功", category="SYSTEM")
            else:
                logger.warning("CosyVoice服务启动失败，TTS功能可能不可用", category="SYSTEM")

        except Exception as e:
            logger.error(f"初始化CosyVoice服务失败: {e}", category="SYSTEM")

    def _initialize_cosyvoice_engine(self):
        """初始化集成的CosyVoice引擎"""
        from .logger_config import logger

        try:
            # 检查是否启用自动加载
            auto_load = getattr(settings, 'COSYVOICE_AUTO_LOAD', True)

            if not auto_load:
                logger.info("CosyVoice引擎自动加载已禁用", category="SYSTEM")
                return

            logger.info("正在初始化集成的CosyVoice引擎...", category="SYSTEM")

            # 延迟导入避免循环依赖
            from .cosyvoice_engine import initialize_cosyvoice_engine

            # 在后台线程中初始化模型，避免阻塞Django启动
            import threading

            def init_model():
                try:
                    success = initialize_cosyvoice_engine()
                    if success:
                        logger.info("✅ 集成CosyVoice引擎初始化成功", category="SYSTEM")
                    else:
                        logger.warning("⚠️ 集成CosyVoice引擎初始化失败，将在首次使用时重试", category="SYSTEM")
                except Exception as e:
                    logger.error(f"❌ 集成CosyVoice引擎初始化异常: {e}", category="SYSTEM")

            # 启动后台初始化线程
            init_thread = threading.Thread(target=init_model, daemon=True)
            init_thread.start()

        except Exception as e:
            logger.error(f"初始化集成CosyVoice引擎失败: {e}", category="SYSTEM")
