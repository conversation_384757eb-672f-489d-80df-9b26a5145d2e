#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
CosyVoice模型服务管理器
负责启动、管理和与CosyVoice模型服务通信
"""

import os
import sys
import time
import signal
import subprocess
import threading
import requests
import logging
from pathlib import Path
from typing import Optional, Dict, Any
from django.conf import settings

logger = logging.getLogger(__name__)


class CosyVoiceServiceManager:
    """CosyVoice模型服务管理器"""
    
    def __init__(self):
        self.process: Optional[subprocess.Popen] = None
        self.service_port = getattr(settings, 'COSYVOICE_SERVICE_PORT', 8001)
        self.model_dir = getattr(settings, 'COSYVOICE_MODEL_DIR', 'pretrained_models/CosyVoice-300M')
        self.conda_env = getattr(settings, 'COSYVOICE_CONDA_ENV', 'cosyvoice')
        self.service_url = f"http://localhost:{self.service_port}"
        self.startup_timeout = 120  # 2分钟启动超时
        self.health_check_interval = 30  # 30秒健康检查间隔
        self._health_check_thread: Optional[threading.Thread] = None
        self._shutdown_event = threading.Event()
        
    def is_service_running(self) -> bool:
        """检查服务是否运行"""
        try:
            response = requests.get(f"{self.service_url}/health", timeout=5)
            return response.status_code == 200
        except:
            return False
    
    def start_service(self) -> bool:
        """启动CosyVoice服务"""
        if self.is_service_running():
            logger.info("CosyVoice服务已在运行")
            return True
        
        try:
            # 检查模型目录
            if not os.path.exists(self.model_dir):
                logger.error(f"模型目录不存在: {self.model_dir}")
                return False
            
            # 构建启动命令
            cmd = self._build_start_command()
            logger.info(f"启动CosyVoice服务: {' '.join(cmd)}")
            
            # 启动服务进程
            self.process = subprocess.Popen(
                cmd,
                stdout=subprocess.PIPE,
                stderr=subprocess.PIPE,
                text=True,
                bufsize=1,
                universal_newlines=True
            )
            
            # 等待服务启动
            if self._wait_for_service_ready():
                logger.info("CosyVoice服务启动成功")
                self._start_health_check()
                return True
            else:
                logger.error("CosyVoice服务启动失败")
                self.stop_service()
                return False
                
        except Exception as e:
            logger.error(f"启动CosyVoice服务失败: {e}")
            return False
    
    def stop_service(self):
        """停止CosyVoice服务"""
        self._shutdown_event.set()
        
        if self._health_check_thread and self._health_check_thread.is_alive():
            self._health_check_thread.join(timeout=5)
        
        if self.process:
            try:
                # 优雅关闭
                self.process.terminate()
                self.process.wait(timeout=10)
            except subprocess.TimeoutExpired:
                # 强制关闭
                self.process.kill()
                self.process.wait()
            finally:
                self.process = None
                logger.info("CosyVoice服务已停止")
    
    def restart_service(self) -> bool:
        """重启CosyVoice服务"""
        logger.info("重启CosyVoice服务")
        self.stop_service()
        time.sleep(2)
        return self.start_service()
    
    def _build_start_command(self) -> list:
        """构建启动命令"""
        # 检测操作系统
        if os.name == 'nt':  # Windows
            conda_activate = f"conda activate {self.conda_env} && "
            cmd = [
                "cmd", "/c",
                f"{conda_activate}python ../api_server.py --port {self.service_port} --model_dir {self.model_dir}"
            ]
        else:  # Linux/Mac
            conda_activate = f"source activate {self.conda_env} && "
            cmd = [
                "bash", "-c",
                f"{conda_activate}python ../api_server.py --port {self.service_port} --model_dir {self.model_dir}"
            ]
        
        return cmd
    
    def _wait_for_service_ready(self) -> bool:
        """等待服务就绪"""
        start_time = time.time()
        while time.time() - start_time < self.startup_timeout:
            if self.is_service_running():
                return True
            time.sleep(2)
        return False
    
    def _start_health_check(self):
        """启动健康检查线程"""
        if self._health_check_thread and self._health_check_thread.is_alive():
            return
        
        self._health_check_thread = threading.Thread(
            target=self._health_check_loop,
            daemon=True
        )
        self._health_check_thread.start()
    
    def _health_check_loop(self):
        """健康检查循环"""
        while not self._shutdown_event.is_set():
            try:
                if not self.is_service_running():
                    logger.warning("CosyVoice服务健康检查失败，尝试重启")
                    if not self.restart_service():
                        logger.error("CosyVoice服务重启失败")
                        break
                
                self._shutdown_event.wait(self.health_check_interval)
                
            except Exception as e:
                logger.error(f"健康检查异常: {e}")
                self._shutdown_event.wait(self.health_check_interval)
    
    def make_request(self, endpoint: str, method: str = 'POST', **kwargs) -> Dict[str, Any]:
        """向CosyVoice服务发送请求"""
        if not self.is_service_running():
            if not self.start_service():
                raise RuntimeError("CosyVoice服务启动失败")
        
        url = f"{self.service_url}{endpoint}"
        
        try:
            if method.upper() == 'POST':
                response = requests.post(url, timeout=30, **kwargs)
            elif method.upper() == 'GET':
                response = requests.get(url, timeout=30, **kwargs)
            else:
                raise ValueError(f"不支持的HTTP方法: {method}")
            
            response.raise_for_status()
            return response.json()
            
        except requests.exceptions.RequestException as e:
            logger.error(f"请求CosyVoice服务失败: {e}")
            raise


# 全局服务管理器实例
cosyvoice_service = CosyVoiceServiceManager()


def get_cosyvoice_service() -> CosyVoiceServiceManager:
    """获取CosyVoice服务管理器实例"""
    return cosyvoice_service


# 信号处理器，确保服务正确关闭
def signal_handler(signum, frame):
    """信号处理器"""
    logger.info("接收到关闭信号，正在停止CosyVoice服务...")
    cosyvoice_service.stop_service()
    sys.exit(0)


# 注册信号处理器
signal.signal(signal.SIGINT, signal_handler)
signal.signal(signal.SIGTERM, signal_handler)
