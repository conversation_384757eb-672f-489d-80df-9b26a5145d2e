"""
兼容性API视图 - 保持原有接口不变
"""
import os
import json
import time
from django.http import JsonResponse, FileResponse, Http404
from django.views.decorators.csrf import csrf_exempt
from django.views.decorators.http import require_http_methods
from django.conf import settings
from django.utils.decorators import method_decorator
from django.shortcuts import render
from django.contrib.admin.views.decorators import staff_member_required
from django.db.models import Count, Q
from django.utils import timezone
from datetime import datetime, timedelta
from rest_framework.decorators import api_view, permission_classes
from rest_framework.permissions import AllowAny
from rest_framework.response import Response
from drf_spectacular.utils import extend_schema, OpenApiParameter
from apps.tasks.models import Task, TaskType, TaskStatus
from apps.tasks.tasks import process_tts_task
from apps.files.models import AudioFile, FileType
from .decorators import enhanced_api_compatibility, rate_limit, require_quota, monitor_performance
from .cosyvoice_engine import get_cosyvoice_engine
from loguru import logger


@extend_schema(
    summary="根路径",
    description="API根路径，返回系统信息",
    tags=["兼容性API"]
)
@api_view(['GET'])
@permission_classes([AllowAny])
def root_view(request):
    """根路径"""
    return Response({
        'message': 'CosyVoice API任务管理系统',
        'version': '2.0.0',
        'compatible_version': '1.0.0',
        'status': 'running',
        'docs_url': '/docs/',
        'admin_url': '/admin/',
    })


@extend_schema(
    summary="SFT模式推理",
    description="SFT模式文本转语音推理，保持原有API完全兼容",
    tags=["兼容性API"],
    parameters=[
        OpenApiParameter(name='tts_text', description='要转换的文本', required=True, type=str),
        OpenApiParameter(name='spk_id', description='说话人ID', required=True, type=str),
    ]
)
@enhanced_api_compatibility
@rate_limit(max_requests=30, window_seconds=60)
@require_quota(quota_cost=1)
@monitor_performance(threshold_seconds=10.0)
@csrf_exempt
@require_http_methods(["POST"])
def inference_sft(request):
    """SFT模式推理 - 兼容原有API"""
    try:
        # 获取参数
        tts_text = request.POST.get('tts_text', '')
        spk_id = request.POST.get('spk_id', '中文女')
        
        if not tts_text:
            return JsonResponse({'error': '缺少必需参数: tts_text'}, status=400)
        
        # 创建任务
        task = Task.objects.create(
            user=request.user if request.user.is_authenticated else None,
            task_type=TaskType.SFT,
            request_data={
                'tts_text': tts_text,
                'spk_id': spk_id,
            },
            client_ip=get_client_ip(request),
            user_agent=request.META.get('HTTP_USER_AGENT', ''),
        )
        
        # 检查用户配额
        if request.user.is_authenticated:
            if not request.user.can_use_quota():
                return JsonResponse({
                    'error': '配额不足',
                    'quota_used': request.user.quota_used,
                    'quota_limit': request.user.quota_limit
                }, status=429)
            request.user.use_quota()
        
        # 提交任务到队列
        process_tts_task.delay(str(task.id))
        
        logger.info(f"SFT推理任务创建: {task.id}, 文本: '{tts_text}', 说话人: '{spk_id}'")
        
        # 返回兼容格式
        return JsonResponse({
            'task_id': str(task.id),
            'status': 'processing',
            'message': '任务已提交处理',
            'tts_text': tts_text,
            'spk_id': spk_id,
        })
        
    except Exception as e:
        logger.error(f"SFT推理失败: {e}")
        return JsonResponse({'error': str(e)}, status=500)


@extend_schema(
    summary="零样本语音克隆",
    description="零样本语音克隆推理，保持原有API完全兼容",
    tags=["兼容性API"]
)
@enhanced_api_compatibility
@rate_limit(max_requests=20, window_seconds=60)
@require_quota(quota_cost=2)
@monitor_performance(threshold_seconds=15.0)
@csrf_exempt
@require_http_methods(["POST"])
def inference_zero_shot(request):
    """零样本语音克隆 - 兼容原有API"""
    try:
        # 获取参数
        tts_text = request.POST.get('tts_text', '')
        prompt_text = request.POST.get('prompt_text', '')
        prompt_wav = request.FILES.get('prompt_wav')
        
        if not all([tts_text, prompt_text, prompt_wav]):
            return JsonResponse({'error': '缺少必需参数'}, status=400)
        
        # 保存上传的音频文件
        prompt_wav_path = save_uploaded_file(prompt_wav, 'prompt')
        
        # 创建任务
        task = Task.objects.create(
            user=request.user if request.user.is_authenticated else None,
            task_type=TaskType.ZERO_SHOT,
            request_data={
                'tts_text': tts_text,
                'prompt_text': prompt_text,
                'prompt_wav': prompt_wav_path,
            },
            client_ip=get_client_ip(request),
            user_agent=request.META.get('HTTP_USER_AGENT', ''),
        )
        
        # 检查用户配额
        if request.user.is_authenticated:
            if not request.user.can_use_quota():
                return JsonResponse({'error': '配额不足'}, status=429)
            request.user.use_quota()
        
        # 提交任务到队列
        process_tts_task.delay(str(task.id))
        
        logger.info(f"零样本推理任务创建: {task.id}")
        
        return JsonResponse({
            'task_id': str(task.id),
            'status': 'processing',
            'message': '任务已提交处理',
        })
        
    except Exception as e:
        logger.error(f"零样本推理失败: {e}")
        return JsonResponse({'error': str(e)}, status=500)


@extend_schema(
    summary="跨语种复刻",
    description="跨语种语音复刻推理，保持原有API完全兼容",
    tags=["兼容性API"]
)
@enhanced_api_compatibility
@rate_limit(max_requests=20, window_seconds=60)
@require_quota(quota_cost=2)
@monitor_performance(threshold_seconds=15.0)
@csrf_exempt
@require_http_methods(["POST"])
def inference_cross_lingual(request):
    """跨语种复刻 - 兼容原有API"""
    try:
        # 获取参数
        tts_text = request.POST.get('tts_text', '')
        prompt_wav = request.FILES.get('prompt_wav')
        
        if not all([tts_text, prompt_wav]):
            return JsonResponse({'error': '缺少必需参数'}, status=400)
        
        # 保存上传的音频文件
        prompt_wav_path = save_uploaded_file(prompt_wav, 'prompt')
        
        # 创建任务
        task = Task.objects.create(
            user=request.user if request.user.is_authenticated else None,
            task_type=TaskType.CROSS_LINGUAL,
            request_data={
                'tts_text': tts_text,
                'prompt_wav': prompt_wav_path,
            },
            client_ip=get_client_ip(request),
            user_agent=request.META.get('HTTP_USER_AGENT', ''),
        )
        
        # 检查用户配额
        if request.user.is_authenticated:
            if not request.user.can_use_quota():
                return JsonResponse({'error': '配额不足'}, status=429)
            request.user.use_quota()
        
        # 提交任务到队列
        process_tts_task.delay(str(task.id))
        
        logger.info(f"跨语种推理任务创建: {task.id}")
        
        return JsonResponse({
            'task_id': str(task.id),
            'status': 'processing',
            'message': '任务已提交处理',
        })
        
    except Exception as e:
        logger.error(f"跨语种推理失败: {e}")
        return JsonResponse({'error': str(e)}, status=500)


@extend_schema(
    summary="自然语言控制",
    description="自然语言控制推理，保持原有API完全兼容",
    tags=["兼容性API"]
)
@enhanced_api_compatibility
@rate_limit(max_requests=25, window_seconds=60)
@require_quota(quota_cost=1)
@monitor_performance(threshold_seconds=12.0)
@csrf_exempt
@require_http_methods(["POST"])
def inference_instruct(request):
    """自然语言控制 - 兼容原有API"""
    try:
        # 获取参数
        tts_text = request.POST.get('tts_text', '')
        spk_id = request.POST.get('spk_id', '中文女')
        instruct_text = request.POST.get('instruct_text', '')
        
        if not all([tts_text, instruct_text]):
            return JsonResponse({'error': '缺少必需参数'}, status=400)
        
        # 创建任务
        task = Task.objects.create(
            user=request.user if request.user.is_authenticated else None,
            task_type=TaskType.INSTRUCT,
            request_data={
                'tts_text': tts_text,
                'spk_id': spk_id,
                'instruct_text': instruct_text,
            },
            client_ip=get_client_ip(request),
            user_agent=request.META.get('HTTP_USER_AGENT', ''),
        )
        
        # 检查用户配额
        if request.user.is_authenticated:
            if not request.user.can_use_quota():
                return JsonResponse({'error': '配额不足'}, status=429)
            request.user.use_quota()
        
        # 提交任务到队列
        process_tts_task.delay(str(task.id))
        
        logger.info(f"指令推理任务创建: {task.id}")
        
        return JsonResponse({
            'task_id': str(task.id),
            'status': 'processing',
            'message': '任务已提交处理',
        })
        
    except Exception as e:
        logger.error(f"指令推理失败: {e}")
        return JsonResponse({'error': str(e)}, status=500)


@extend_schema(
    summary="高级自然语言控制",
    description="高级自然语言控制推理，保持原有API完全兼容",
    tags=["兼容性API"]
)
@enhanced_api_compatibility
@rate_limit(max_requests=20, window_seconds=60)
@require_quota(quota_cost=2)
@monitor_performance(threshold_seconds=15.0)
@csrf_exempt
@require_http_methods(["POST"])
def inference_instruct2(request):
    """高级自然语言控制 - 兼容原有API"""
    try:
        # 获取参数
        tts_text = request.POST.get('tts_text', '')
        spk_id = request.POST.get('spk_id', '中文女')
        instruct_text = request.POST.get('instruct_text', '')
        
        if not all([tts_text, instruct_text]):
            return JsonResponse({'error': '缺少必需参数'}, status=400)
        
        # 创建任务
        task = Task.objects.create(
            user=request.user if request.user.is_authenticated else None,
            task_type=TaskType.INSTRUCT2,
            request_data={
                'tts_text': tts_text,
                'spk_id': spk_id,
                'instruct_text': instruct_text,
            },
            client_ip=get_client_ip(request),
            user_agent=request.META.get('HTTP_USER_AGENT', ''),
        )
        
        # 检查用户配额
        if request.user.is_authenticated:
            if not request.user.can_use_quota():
                return JsonResponse({'error': '配额不足'}, status=429)
            request.user.use_quota()
        
        # 提交任务到队列
        process_tts_task.delay(str(task.id))
        
        logger.info(f"高级指令推理任务创建: {task.id}")
        
        return JsonResponse({
            'task_id': str(task.id),
            'status': 'processing',
            'message': '任务已提交处理',
        })
        
    except Exception as e:
        logger.error(f"高级指令推理失败: {e}")
        return JsonResponse({'error': str(e)}, status=500)


@extend_schema(
    summary="获取说话人列表",
    description="获取可用的说话人列表",
    tags=["兼容性API"]
)
@api_view(['GET'])
@permission_classes([AllowAny])
def list_speakers(request):
    """获取说话人列表 - 兼容原有API"""
    # 模拟说话人列表，实际应该从CosyVoice模型中获取
    speakers = [
        '中文女', '中文男', '英文女', '英文男', '日语女', '日语男',
        '韩语女', '韩语男', '粤语女', '粤语男'
    ]

    return Response({
        'speakers': speakers,
        'count': len(speakers)
    })


@extend_schema(
    summary="获取参考音频列表",
    description="获取可用的参考音频文件列表",
    tags=["兼容性API"]
)
@api_view(['GET'])
@permission_classes([AllowAny])
def reference_audios(request):
    """获取参考音频列表 - 兼容原有API"""
    try:
        # 获取参考音频文件
        reference_files = AudioFile.objects.filter(
            file_type=FileType.REFERENCE,
            is_public=True,
            is_active=True
        ).values('filename', 'duration', 'file_size')

        return Response({
            'reference_audios': list(reference_files),
            'count': len(reference_files)
        })

    except Exception as e:
        logger.error(f"获取参考音频列表失败: {e}")
        return Response({'error': str(e)}, status=500)


@extend_schema(
    summary="获取生成音频列表",
    description="获取已生成的音频文件列表",
    tags=["兼容性API"]
)
@api_view(['GET'])
@permission_classes([AllowAny])
def list_generated_audio(request):
    """获取生成音频列表 - 兼容原有API"""
    try:
        # 获取生成的音频文件
        queryset = AudioFile.objects.filter(
            file_type=FileType.AUDIO_OUTPUT,
            is_active=True
        )

        # 如果用户已认证，只显示用户的文件
        if request.user.is_authenticated:
            queryset = queryset.filter(user=request.user)
        else:
            # 匿名用户显示最近的公开文件
            queryset = queryset.filter(is_public=True)[:20]

        audio_files = queryset.values(
            'filename', 'duration', 'file_size', 'created_at'
        ).order_by('-created_at')

        return Response({
            'generated_audios': list(audio_files),
            'count': len(audio_files)
        })

    except Exception as e:
        logger.error(f"获取生成音频列表失败: {e}")
        return Response({'error': str(e)}, status=500)


@extend_schema(
    summary="下载音频文件",
    description="下载指定的音频文件",
    tags=["兼容性API"]
)
@api_view(['GET'])
@permission_classes([AllowAny])
def download_audio(request, filename):
    """下载音频文件 - 兼容原有API"""
    try:
        # 查找音频文件
        audio_file = AudioFile.objects.get(
            filename=filename,
            is_active=True
        )

        # 检查访问权限
        if not audio_file.is_public:
            if not request.user.is_authenticated or audio_file.user != request.user:
                return JsonResponse({'error': '无权限访问此文件'}, status=403)

        # 记录访问
        audio_file.record_access()

        # 构建文件路径
        file_path = os.path.join(settings.MEDIA_ROOT, audio_file.file_path)

        if not os.path.exists(file_path):
            raise Http404('文件不存在')

        # 返回文件
        response = FileResponse(
            open(file_path, 'rb'),
            content_type=audio_file.mime_type,
            as_attachment=True,
            filename=audio_file.original_filename or audio_file.filename
        )

        logger.info(f"下载音频文件: {filename}")
        return response

    except AudioFile.DoesNotExist:
        raise Http404('文件不存在')
    except Exception as e:
        logger.error(f"下载音频文件失败: {filename}, 错误: {e}")
        return JsonResponse({'error': str(e)}, status=500)


@extend_schema(
    summary="删除音频文件",
    description="删除指定的音频文件",
    tags=["兼容性API"]
)
@csrf_exempt
@require_http_methods(["POST", "DELETE"])
def delete_audio(request, filename):
    """删除音频文件 - 兼容原有API"""
    try:
        # 查找音频文件
        audio_file = AudioFile.objects.get(
            filename=filename,
            is_active=True
        )

        # 检查删除权限
        if request.user.is_authenticated:
            if audio_file.user != request.user and not request.user.is_superuser:
                return JsonResponse({'error': '无权限删除此文件'}, status=403)
        else:
            return JsonResponse({'error': '需要登录才能删除文件'}, status=401)

        # 删除物理文件
        audio_file.delete_file()

        # 软删除数据库记录
        audio_file.soft_delete()

        logger.info(f"删除音频文件: {filename}")

        return JsonResponse({
            'message': '文件删除成功',
            'filename': filename
        })

    except AudioFile.DoesNotExist:
        return JsonResponse({'error': '文件不存在'}, status=404)
    except Exception as e:
        logger.error(f"删除音频文件失败: {filename}, 错误: {e}")
        return JsonResponse({'error': str(e)}, status=500)


@extend_schema(
    summary="清空生成音频",
    description="清空所有生成的音频文件",
    tags=["兼容性API"]
)
@csrf_exempt
@require_http_methods(["POST"])
def clear_generated_audio(request):
    """清空生成音频 - 兼容原有API"""
    try:
        # 检查权限
        if not request.user.is_authenticated:
            return JsonResponse({'error': '需要登录才能执行此操作'}, status=401)

        # 获取用户的生成音频文件
        audio_files = AudioFile.objects.filter(
            user=request.user,
            file_type=FileType.AUDIO_OUTPUT,
            is_active=True
        )

        deleted_count = 0
        for audio_file in audio_files:
            try:
                audio_file.delete_file()
                audio_file.soft_delete()
                deleted_count += 1
            except Exception as e:
                logger.error(f"删除文件失败: {audio_file.filename}, 错误: {e}")

        logger.info(f"用户清空生成音频: {request.user.username}, 删除 {deleted_count} 个文件")

        return JsonResponse({
            'message': f'成功删除 {deleted_count} 个音频文件',
            'deleted_count': deleted_count
        })

    except Exception as e:
        logger.error(f"清空生成音频失败: {e}")
        return JsonResponse({'error': str(e)}, status=500)


def get_client_ip(request):
    """获取客户端IP地址"""
    x_forwarded_for = request.META.get('HTTP_X_FORWARDED_FOR')
    if x_forwarded_for:
        ip = x_forwarded_for.split(',')[0]
    else:
        ip = request.META.get('REMOTE_ADDR')
    return ip


def save_uploaded_file(uploaded_file, prefix='upload'):
    """保存上传的文件"""
    import uuid
    from django.core.files.storage import default_storage

    # 生成唯一文件名
    file_extension = os.path.splitext(uploaded_file.name)[1]
    unique_filename = f"{prefix}_{uuid.uuid4().hex}{file_extension}"

    # 保存文件
    file_path = default_storage.save(
        f"uploads/{unique_filename}",
        uploaded_file
    )

    return file_path


@extend_schema(
    summary="查询任务状态",
    description="查询指定任务的当前状态",
    tags=["兼容性API"]
)
@api_view(['GET'])
@permission_classes([AllowAny])
def task_status(request, task_id):
    """查询任务状态 - 兼容原有API"""
    try:
        task = Task.objects.get(id=task_id)

        # 检查访问权限
        if task.user and request.user.is_authenticated:
            if task.user != request.user and not request.user.is_superuser:
                return JsonResponse({'error': '无权限访问此任务'}, status=403)

        return JsonResponse({
            'task_id': str(task.id),
            'status': task.status,
            'status_display': task.get_status_display(),
            'task_type': task.task_type,
            'created_at': task.created_at.isoformat(),
            'started_at': task.started_at.isoformat() if task.started_at else None,
            'completed_at': task.completed_at.isoformat() if task.completed_at else None,
            'processing_time': task.processing_time,
            'queue_time': task.queue_time,
            'retry_count': task.retry_count,
            'error_message': task.error_message if task.status == TaskStatus.FAILED else None,
        })

    except Task.DoesNotExist:
        return JsonResponse({'error': '任务不存在'}, status=404)
    except Exception as e:
        logger.error(f"查询任务状态失败: {task_id}, 错误: {e}")
        return JsonResponse({'error': str(e)}, status=500)


@extend_schema(
    summary="获取任务结果",
    description="获取已完成任务的结果",
    tags=["兼容性API"]
)
@api_view(['GET'])
@permission_classes([AllowAny])
def task_result(request, task_id):
    """获取任务结果 - 兼容原有API"""
    try:
        task = Task.objects.get(id=task_id)

        # 检查访问权限
        if task.user and request.user.is_authenticated:
            if task.user != request.user and not request.user.is_superuser:
                return JsonResponse({'error': '无权限访问此任务'}, status=403)

        if task.status != TaskStatus.COMPLETED:
            return JsonResponse({
                'error': '任务未完成',
                'status': task.status,
                'message': '任务尚未完成，请稍后查询'
            }, status=400)

        result = {
            'task_id': str(task.id),
            'status': task.status,
            'task_type': task.task_type,
            'completed_at': task.completed_at.isoformat(),
            'processing_time': task.processing_time,
            'response_data': task.response_data or {},
        }

        # 如果有音频文件，添加下载链接
        if task.audio_file:
            result['audio_file'] = {
                'filename': task.audio_file.filename,
                'duration': task.audio_file.duration,
                'file_size': task.audio_file.file_size,
                'download_url': f"/download/{task.audio_file.filename}",
            }

        return JsonResponse(result)

    except Task.DoesNotExist:
        return JsonResponse({'error': '任务不存在'}, status=404)
    except Exception as e:
        logger.error(f"获取任务结果失败: {task_id}, 错误: {e}")
        return JsonResponse({'error': str(e)}, status=500)


# ================================
# 管理后台仪表板相关视图
# ================================

@staff_member_required
def admin_dashboard(request):
    """管理后台仪表板"""
    try:
        # 获取统计数据
        stats = get_dashboard_stats()

        # 获取最近活动
        recent_activities = get_recent_activities()

        context = {
            'stats': stats,
            'recent_activities': recent_activities,
            'title': 'CosyVoice 管理后台',
        }

        return render(request, 'admin/dashboard.html', context)

    except Exception as e:
        logger.error(f"管理后台仪表板加载失败: {e}")
        return render(request, 'admin/dashboard.html', {
            'stats': get_default_stats(),
            'recent_activities': [],
            'title': 'CosyVoice 管理后台',
        })


def get_dashboard_stats():
    """获取仪表板统计数据"""
    try:
        from apps.users.models import User

        # 获取今天的日期范围
        today = timezone.now().date()
        yesterday = today - timedelta(days=1)
        today_start = timezone.make_aware(datetime.combine(today, datetime.min.time()))
        yesterday_start = timezone.make_aware(datetime.combine(yesterday, datetime.min.time()))

        # 今日生成数量
        today_generated = Task.objects.filter(
            created_at__gte=today_start,
            status=TaskStatus.COMPLETED
        ).count()

        # 昨日生成数量
        yesterday_generated = Task.objects.filter(
            created_at__gte=yesterday_start,
            created_at__lt=today_start,
            status=TaskStatus.COMPLETED
        ).count()

        # 计算增长率
        today_growth = 0
        if yesterday_generated > 0:
            today_growth = round(((today_generated - yesterday_generated) / yesterday_generated) * 100, 1)
        elif today_generated > 0:
            today_growth = 100

        # 总用户数
        total_users = User.objects.count()

        # 活跃用户数（最近7天有登录的用户）
        week_ago = timezone.now() - timedelta(days=7)
        active_users = User.objects.filter(last_login__gte=week_ago).count()

        # 成功率
        total_tasks = Task.objects.count()
        completed_tasks = Task.objects.filter(status=TaskStatus.COMPLETED).count()
        success_rate = 0
        if total_tasks > 0:
            success_rate = round((completed_tasks / total_tasks) * 100, 1)

        # 音频文件总数
        total_files = AudioFile.objects.count()

        # 昨日文件数
        yesterday_files = AudioFile.objects.filter(
            created_at__gte=yesterday_start,
            created_at__lt=today_start
        ).count()

        # 今日文件数
        today_files = AudioFile.objects.filter(
            created_at__gte=today_start
        ).count()

        # 文件增长率
        files_growth = 0
        if yesterday_files > 0:
            files_growth = round(((today_files - yesterday_files) / yesterday_files) * 100, 1)
        elif today_files > 0:
            files_growth = 100

        return {
            'today_generated': today_generated,
            'today_growth': max(0, today_growth),
            'total_users': total_users,
            'active_users': active_users,
            'success_rate': success_rate,
            'success_growth': 2.1,  # 模拟数据
            'total_files': total_files,
            'files_growth': max(0, files_growth),
        }

    except Exception as e:
        logger.error(f"获取仪表板统计数据失败: {e}")
        return get_default_stats()


def get_default_stats():
    """获取默认统计数据"""
    return {
        'today_generated': 0,
        'today_growth': 0,
        'total_users': 0,
        'active_users': 0,
        'success_rate': 0,
        'success_growth': 0,
        'total_files': 0,
        'files_growth': 0,
    }


def get_recent_activities():
    """获取最近活动"""
    try:
        from apps.users.models import User

        activities = []

        # 最近的任务
        recent_tasks = Task.objects.filter(
            status=TaskStatus.COMPLETED
        ).order_by('-completed_at')[:3]

        for task in recent_tasks:
            time_diff = timezone.now() - (task.completed_at or task.created_at)
            if time_diff.days > 0:
                time_str = f"{time_diff.days}天前"
            elif time_diff.seconds > 3600:
                time_str = f"{time_diff.seconds // 3600}小时前"
            elif time_diff.seconds > 60:
                time_str = f"{time_diff.seconds // 60}分钟前"
            else:
                time_str = "刚刚"

            # 根据任务类型设置图标和颜色
            if task.task_type == TaskType.SFT:
                icon, bg_color, color = "🎵", "#e6f7ff", "#1890ff"
                title = "完成了SFT语音合成"
            elif task.task_type == TaskType.ZERO_SHOT:
                icon, bg_color, color = "🎯", "#f6ffed", "#52c41a"
                title = "完成了零样本语音克隆"
            elif task.task_type == TaskType.CROSS_LINGUAL:
                icon, bg_color, color = "🌍", "#fff2e8", "#fa8c16"
                title = "完成了跨语种复刻"
            else:
                icon, bg_color, color = "🎵", "#e6f7ff", "#1890ff"
                title = "完成了语音合成任务"

            activities.append({
                'icon': icon,
                'bg_color': bg_color,
                'color': color,
                'title': title,
                'time': time_str
            })

        # 最近的用户注册
        recent_users = User.objects.order_by('-created_at')[:2]
        for user in recent_users:
            time_diff = timezone.now() - user.created_at
            if time_diff.days > 0:
                time_str = f"{time_diff.days}天前"
            elif time_diff.seconds > 3600:
                time_str = f"{time_diff.seconds // 3600}小时前"
            elif time_diff.seconds > 60:
                time_str = f"{time_diff.seconds // 60}分钟前"
            else:
                time_str = "刚刚"

            activities.append({
                'icon': "👤",
                'bg_color': "#fff2e8",
                'color': "#fa8c16",
                'title': f"新用户 {user.username} 注册",
                'time': time_str
            })

        # 最近的文件上传
        recent_files = AudioFile.objects.order_by('-created_at')[:2]
        for file in recent_files:
            time_diff = timezone.now() - file.created_at
            if time_diff.days > 0:
                time_str = f"{time_diff.days}天前"
            elif time_diff.seconds > 3600:
                time_str = f"{time_diff.seconds // 3600}小时前"
            elif time_diff.seconds > 60:
                time_str = f"{time_diff.seconds // 60}分钟前"
            else:
                time_str = "刚刚"

            activities.append({
                'icon': "📁",
                'bg_color': "#f6ffed",
                'color': "#52c41a",
                'title': f"生成了音频文件 {file.filename[:20]}...",
                'time': time_str
            })

        # 按时间排序并限制数量
        activities.sort(key=lambda x: x['time'])
        return activities[:5]

    except Exception as e:
        logger.error(f"获取最近活动失败: {e}")
        return []


@staff_member_required
def dashboard_api(request):
    """仪表板API接口"""
    try:
        stats = get_dashboard_stats()
        activities = get_recent_activities()

        return JsonResponse({
            'success': True,
            'stats': stats,
            'activities': activities
        })
    except Exception as e:
        logger.error(f"仪表板API调用失败: {e}")
        return JsonResponse({
            'success': False,
            'error': str(e)
        })
