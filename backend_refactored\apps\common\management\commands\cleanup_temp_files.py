"""
清理临时文件的管理命令
"""
import os
import time
from datetime import datetime, timedelta
from django.core.management.base import BaseCommand
from django.conf import settings
from loguru import logger


class Command(BaseCommand):
    help = '清理临时文件目录中的过期文件'

    def add_arguments(self, parser):
        parser.add_argument(
            '--max-age',
            type=int,
            default=24,
            help='删除超过指定小时数的临时文件 (默认: 24小时)'
        )
        parser.add_argument(
            '--dry-run',
            action='store_true',
            help='只显示将要删除的文件，不实际删除'
        )

    def handle(self, *args, **options):
        max_age_hours = options['max_age']
        dry_run = options['dry_run']
        
        # 临时文件目录
        temp_dir = os.path.join(settings.BASE_DIR, settings.COSYVOICE_OUTPUT_DIR, 'temp')
        
        if not os.path.exists(temp_dir):
            self.stdout.write(
                self.style.WARNING(f'临时文件目录不存在: {temp_dir}')
            )
            return
        
        # 计算过期时间
        cutoff_time = time.time() - (max_age_hours * 3600)
        cutoff_datetime = datetime.fromtimestamp(cutoff_time)
        
        self.stdout.write(
            f'清理 {cutoff_datetime.strftime("%Y-%m-%d %H:%M:%S")} 之前的临时文件...'
        )
        
        deleted_count = 0
        deleted_size = 0
        failed_count = 0
        
        try:
            for filename in os.listdir(temp_dir):
                if not filename.startswith('temp_'):
                    continue
                
                file_path = os.path.join(temp_dir, filename)
                
                try:
                    # 检查文件修改时间
                    file_mtime = os.path.getmtime(file_path)
                    
                    if file_mtime < cutoff_time:
                        file_size = os.path.getsize(file_path)
                        
                        if dry_run:
                            self.stdout.write(
                                f'[DRY RUN] 将删除: {filename} '
                                f'({file_size} bytes, '
                                f'{datetime.fromtimestamp(file_mtime).strftime("%Y-%m-%d %H:%M:%S")})'
                            )
                        else:
                            try:
                                os.unlink(file_path)
                                deleted_count += 1
                                deleted_size += file_size
                                self.stdout.write(
                                    f'已删除: {filename} ({file_size} bytes)'
                                )
                            except Exception as e:
                                failed_count += 1
                                self.stdout.write(
                                    self.style.ERROR(f'删除失败: {filename} - {e}')
                                )
                                
                except Exception as e:
                    self.stdout.write(
                        self.style.ERROR(f'处理文件失败: {filename} - {e}')
                    )
                    
        except Exception as e:
            self.stdout.write(
                self.style.ERROR(f'扫描目录失败: {e}')
            )
            return
        
        if dry_run:
            self.stdout.write(
                self.style.SUCCESS(
                    f'[DRY RUN] 完成扫描，找到 {deleted_count} 个过期文件'
                )
            )
        else:
            self.stdout.write(
                self.style.SUCCESS(
                    f'清理完成: 删除 {deleted_count} 个文件, '
                    f'释放 {deleted_size / 1024 / 1024:.2f} MB 空间'
                )
            )
            
            if failed_count > 0:
                self.stdout.write(
                    self.style.WARNING(f'有 {failed_count} 个文件删除失败')
                )
        
        logger.info(
            f'临时文件清理完成: 删除 {deleted_count} 个文件, '
            f'失败 {failed_count} 个, 释放 {deleted_size} bytes'
        )
