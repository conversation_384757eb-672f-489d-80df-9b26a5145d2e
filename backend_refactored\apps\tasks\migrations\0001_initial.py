# Generated by Django 4.2.7 on 2025-07-24 03:28

from django.db import migrations, models
import django.db.models.deletion
import uuid


class Migration(migrations.Migration):
    initial = True

    dependencies = [
        ("files", "0001_initial"),
    ]

    operations = [
        migrations.CreateModel(
            name="Task",
            fields=[
                (
                    "id",
                    models.UUIDField(
                        default=uuid.uuid4,
                        editable=False,
                        primary_key=True,
                        serialize=False,
                    ),
                ),
                (
                    "created_at",
                    models.DateTimeField(auto_now_add=True, verbose_name="创建时间"),
                ),
                (
                    "updated_at",
                    models.DateTimeField(auto_now=True, verbose_name="更新时间"),
                ),
                (
                    "deleted_at",
                    models.DateTimeField(blank=True, null=True, verbose_name="删除时间"),
                ),
                ("is_active", models.BooleanField(default=True, verbose_name="是否激活")),
                (
                    "task_type",
                    models.CharField(
                        choices=[
                            ("sft", "SFT模式推理"),
                            ("zero_shot", "零样本语音克隆"),
                            ("cross_lingual", "跨语种复刻"),
                            ("instruct", "自然语言控制"),
                            ("instruct2", "高级自然语言控制"),
                        ],
                        max_length=20,
                        verbose_name="任务类型",
                    ),
                ),
                (
                    "status",
                    models.CharField(
                        choices=[
                            ("pending", "等待中"),
                            ("processing", "处理中"),
                            ("completed", "已完成"),
                            ("failed", "失败"),
                            ("cancelled", "已取消"),
                        ],
                        default="pending",
                        max_length=20,
                        verbose_name="任务状态",
                    ),
                ),
                (
                    "priority",
                    models.CharField(
                        choices=[
                            ("low", "低"),
                            ("normal", "普通"),
                            ("high", "高"),
                            ("urgent", "紧急"),
                        ],
                        default="normal",
                        max_length=10,
                        verbose_name="优先级",
                    ),
                ),
                ("request_data", models.JSONField(verbose_name="请求数据")),
                (
                    "response_data",
                    models.JSONField(blank=True, null=True, verbose_name="响应数据"),
                ),
                (
                    "started_at",
                    models.DateTimeField(blank=True, null=True, verbose_name="开始时间"),
                ),
                (
                    "completed_at",
                    models.DateTimeField(blank=True, null=True, verbose_name="完成时间"),
                ),
                ("error_message", models.TextField(blank=True, verbose_name="错误信息")),
                ("retry_count", models.IntegerField(default=0, verbose_name="重试次数")),
                ("max_retries", models.IntegerField(default=3, verbose_name="最大重试次数")),
                (
                    "processing_time",
                    models.FloatField(blank=True, null=True, verbose_name="处理时间(秒)"),
                ),
                (
                    "queue_time",
                    models.FloatField(blank=True, null=True, verbose_name="队列等待时间(秒)"),
                ),
                (
                    "client_ip",
                    models.GenericIPAddressField(
                        blank=True, null=True, verbose_name="客户端IP"
                    ),
                ),
                ("user_agent", models.TextField(blank=True, verbose_name="用户代理")),
                (
                    "audio_file",
                    models.ForeignKey(
                        blank=True,
                        null=True,
                        on_delete=django.db.models.deletion.SET_NULL,
                        related_name="tasks",
                        to="files.audiofile",
                    ),
                ),
            ],
            options={
                "verbose_name": "任务",
                "verbose_name_plural": "任务",
                "db_table": "tasks",
                "ordering": ["-created_at"],
            },
        ),
        migrations.CreateModel(
            name="TaskLog",
            fields=[
                (
                    "id",
                    models.UUIDField(
                        default=uuid.uuid4,
                        editable=False,
                        primary_key=True,
                        serialize=False,
                    ),
                ),
                (
                    "created_at",
                    models.DateTimeField(auto_now_add=True, verbose_name="创建时间"),
                ),
                (
                    "updated_at",
                    models.DateTimeField(auto_now=True, verbose_name="更新时间"),
                ),
                (
                    "deleted_at",
                    models.DateTimeField(blank=True, null=True, verbose_name="删除时间"),
                ),
                ("is_active", models.BooleanField(default=True, verbose_name="是否激活")),
                (
                    "level",
                    models.CharField(
                        choices=[
                            ("DEBUG", "DEBUG"),
                            ("INFO", "INFO"),
                            ("WARNING", "WARNING"),
                            ("ERROR", "ERROR"),
                        ],
                        max_length=10,
                        verbose_name="日志级别",
                    ),
                ),
                ("message", models.TextField(verbose_name="日志消息")),
                (
                    "extra_data",
                    models.JSONField(blank=True, null=True, verbose_name="额外数据"),
                ),
                (
                    "task",
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.CASCADE,
                        related_name="logs",
                        to="tasks.task",
                    ),
                ),
            ],
            options={
                "verbose_name": "任务日志",
                "verbose_name_plural": "任务日志",
                "db_table": "task_logs",
                "ordering": ["-created_at"],
            },
        ),
    ]
