"""
文件迁移到OSS管理命令
"""
from django.core.management.base import BaseCommand
from apps.files.models import AudioFile, StorageType
from apps.files.services import FileService
from loguru import logger


class Command(BaseCommand):
    help = '将本地文件迁移到阿里云OSS'
    
    def add_arguments(self, parser):
        parser.add_argument(
            '--batch-size',
            type=int,
            default=10,
            help='每批处理的文件数量'
        )
        
        parser.add_argument(
            '--file-type',
            choices=['audio_input', 'audio_output', 'reference', 'temp'],
            help='只迁移指定类型的文件'
        )
        
        parser.add_argument(
            '--dry-run',
            action='store_true',
            help='只显示将要迁移的文件，不实际迁移'
        )
    
    def handle(self, *args, **options):
        batch_size = options['batch_size']
        file_type = options['file_type']
        dry_run = options['dry_run']
        
        # 查询需要迁移的文件
        queryset = AudioFile.objects.filter(
            storage_type=StorageType.LOCAL,
            is_active=True
        )
        
        if file_type:
            queryset = queryset.filter(file_type=file_type)
        
        total_files = queryset.count()
        
        if total_files == 0:
            self.stdout.write("没有找到需要迁移的文件")
            return
        
        self.stdout.write(f"找到 {total_files} 个文件需要迁移到OSS")
        
        if dry_run:
            self.stdout.write("模拟运行模式，显示前10个文件:")
            for audio_file in queryset[:10]:
                self.stdout.write(f"  - {audio_file.filename} ({audio_file.file_size} bytes)")
            if total_files > 10:
                self.stdout.write(f"  ... 还有 {total_files - 10} 个文件")
            return
        
        # 确认迁移
        confirm = input(f"确定要将这 {total_files} 个文件迁移到OSS吗? (y/N): ")
        if confirm.lower() != 'y':
            self.stdout.write("操作已取消")
            return
        
        file_service = FileService()
        success_count = 0
        error_count = 0
        
        # 分批处理
        for i in range(0, total_files, batch_size):
            batch_files = queryset[i:i + batch_size]
            
            self.stdout.write(f"处理第 {i//batch_size + 1} 批 ({len(batch_files)} 个文件)...")
            
            for audio_file in batch_files:
                try:
                    if file_service.move_to_oss(audio_file):
                        success_count += 1
                        self.stdout.write(f"  ✓ {audio_file.filename}")
                    else:
                        error_count += 1
                        self.stdout.write(
                            self.style.ERROR(f"  ✗ {audio_file.filename} - 迁移失败")
                        )
                except Exception as e:
                    error_count += 1
                    self.stdout.write(
                        self.style.ERROR(f"  ✗ {audio_file.filename} - 错误: {e}")
                    )
        
        self.stdout.write(
            self.style.SUCCESS(
                f"迁移完成: 成功 {success_count} 个，失败 {error_count} 个"
            )
        )
