<!DOCTYPE html>
<html>
<head>
    <meta charset="utf-8">
    <title>系统告警 - {{ site_name }}</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            line-height: 1.6;
            color: #333;
            max-width: 600px;
            margin: 0 auto;
            padding: 20px;
        }
        .header {
            background-color: {% if alert.level == 'critical' %}#dc3545{% elif alert.level == 'warning' %}#ffc107{% else %}#17a2b8{% endif %};
            color: white;
            padding: 20px;
            text-align: center;
            border-radius: 5px;
            margin-bottom: 20px;
        }
        .content {
            padding: 20px;
            background-color: #fff;
            border: 1px solid #ddd;
            border-radius: 5px;
        }
        .alert-info {
            background-color: #f8f9fa;
            padding: 15px;
            border-radius: 5px;
            margin: 15px 0;
        }
        .alert-info table {
            width: 100%;
            border-collapse: collapse;
        }
        .alert-info td {
            padding: 8px;
            border-bottom: 1px solid #dee2e6;
        }
        .alert-info td:first-child {
            font-weight: bold;
            width: 30%;
        }
        .level-badge {
            display: inline-block;
            padding: 4px 8px;
            border-radius: 3px;
            color: white;
            font-size: 12px;
            font-weight: bold;
            text-transform: uppercase;
        }
        .level-critical { background-color: #dc3545; }
        .level-warning { background-color: #ffc107; color: #212529; }
        .level-info { background-color: #17a2b8; }
        .footer {
            margin-top: 20px;
            padding: 20px;
            background-color: #f8f9fa;
            border-radius: 5px;
            font-size: 14px;
            color: #666;
        }
        .actions {
            margin: 20px 0;
            text-align: center;
        }
        .button {
            display: inline-block;
            padding: 12px 24px;
            background-color: #007bff;
            color: white;
            text-decoration: none;
            border-radius: 5px;
            margin: 0 10px;
        }
    </style>
</head>
<body>
    <div class="header">
        <h1>🚨 系统告警</h1>
        <h2>{{ site_name }}</h2>
    </div>
    
    <div class="content">
        <h3>告警详情</h3>
        
        <div class="alert-info">
            <table>
                <tr>
                    <td>告警级别</td>
                    <td>
                        <span class="level-badge level-{{ alert.level }}">{{ alert.level }}</span>
                    </td>
                </tr>
                <tr>
                    <td>监控指标</td>
                    <td>{{ alert.metric }}</td>
                </tr>
                <tr>
                    <td>当前值</td>
                    <td>{{ alert.value }}</td>
                </tr>
                <tr>
                    <td>告警阈值</td>
                    <td>{{ alert.threshold }}</td>
                </tr>
                <tr>
                    <td>告警时间</td>
                    <td>{{ alert.timestamp }}</td>
                </tr>
                <tr>
                    <td>告警ID</td>
                    <td>{{ alert.id }}</td>
                </tr>
            </table>
        </div>
        
        <h4>告警描述</h4>
        <p>{{ alert.message }}</p>
        
        {% if alert.level == 'critical' %}
        <div style="background-color: #f8d7da; border: 1px solid #f5c6cb; color: #721c24; padding: 15px; border-radius: 5px; margin: 15px 0;">
            <strong>⚠️ 严重告警</strong><br>
            此告警级别为严重，可能影响系统正常运行，请立即处理！
        </div>
        {% elif alert.level == 'warning' %}
        <div style="background-color: #fff3cd; border: 1px solid #ffeaa7; color: #856404; padding: 15px; border-radius: 5px; margin: 15px 0;">
            <strong>⚠️ 警告</strong><br>
            此告警需要关注，建议及时处理以避免问题恶化。
        </div>
        {% endif %}
        
        <h4>建议处理措施</h4>
        <ul>
            {% if 'cpu' in alert.metric %}
            <li>检查系统进程，找出CPU占用高的进程</li>
            <li>考虑增加服务器资源或优化代码性能</li>
            <li>检查是否有异常的任务或循环</li>
            {% elif 'memory' in alert.metric %}
            <li>检查内存使用情况，找出内存泄漏的进程</li>
            <li>重启相关服务释放内存</li>
            <li>考虑增加服务器内存</li>
            {% elif 'disk' in alert.metric %}
            <li>清理不必要的文件和日志</li>
            <li>检查是否有大文件占用磁盘空间</li>
            <li>考虑扩容磁盘空间</li>
            {% elif 'database' in alert.metric %}
            <li>检查数据库连接数和慢查询</li>
            <li>优化数据库查询性能</li>
            <li>检查数据库服务器资源</li>
            {% elif 'redis' in alert.metric %}
            <li>检查Redis服务状态</li>
            <li>检查Redis内存使用情况</li>
            <li>检查网络连接</li>
            {% elif 'tasks' in alert.metric %}
            <li>检查任务队列状态</li>
            <li>检查Celery工作进程</li>
            <li>分析任务失败原因</li>
            {% endif %}
            <li>查看系统监控面板获取更多信息</li>
            <li>检查相关日志文件</li>
        </ul>
        
        <div class="actions">
            <a href="{{ dashboard_url }}" class="button">查看监控面板</a>
            <a href="{{ logs_url }}" class="button">查看系统日志</a>
        </div>
    </div>
    
    <div class="footer">
        <p><strong>注意事项：</strong></p>
        <ul>
            <li>此邮件由系统自动发送，请勿回复</li>
            <li>如果问题已解决，系统会自动发送解决通知</li>
            <li>如需帮助，请联系系统管理员</li>
        </ul>
        
        <p>{{ site_name }} 监控系统</p>
        <p>发送时间: {{ alert.timestamp }}</p>
    </div>
</body>
</html>
