"""
WebSocket消费者
"""
import json
from channels.generic.websocket import AsyncWebsocketConsumer
from channels.db import database_sync_to_async
from django.contrib.auth import get_user_model
from rest_framework_simplejwt.tokens import UntypedToken
from rest_framework_simplejwt.exceptions import InvalidToken, TokenError
from loguru import logger

User = get_user_model()


class TaskConsumer(AsyncWebsocketConsumer):
    """任务状态WebSocket消费者"""
    
    async def connect(self):
        """连接处理"""
        # 从查询参数获取token
        token = self.scope['query_string'].decode().split('token=')[-1]
        
        # 验证JWT token
        user = await self.get_user_from_token(token)
        if not user:
            await self.close()
            return
        
        self.user = user
        self.group_name = f"user_{user.id}_tasks"
        
        # 加入组
        await self.channel_layer.group_add(
            self.group_name,
            self.channel_name
        )
        
        await self.accept()
        logger.info(f"用户 {user.username} 连接到任务WebSocket")
    
    async def disconnect(self, close_code):
        """断开连接处理"""
        if hasattr(self, 'group_name'):
            await self.channel_layer.group_discard(
                self.group_name,
                self.channel_name
            )
            logger.info(f"用户 {self.user.username} 断开任务WebSocket连接")
    
    async def receive(self, text_data):
        """接收消息处理"""
        try:
            data = json.loads(text_data)
            message_type = data.get('type')
            
            if message_type == 'subscribe_task':
                # 订阅特定任务
                task_id = data.get('task_id')
                if task_id:
                    await self.subscribe_task(task_id)
            elif message_type == 'unsubscribe_task':
                # 取消订阅特定任务
                task_id = data.get('task_id')
                if task_id:
                    await self.unsubscribe_task(task_id)
                    
        except json.JSONDecodeError:
            await self.send(text_data=json.dumps({
                'error': '无效的JSON格式'
            }))
    
    async def task_status_update(self, event):
        """任务状态更新"""
        await self.send(text_data=json.dumps({
            'type': 'task_status_update',
            'task_id': event['task_id'],
            'status': event['status'],
            'message': event.get('message', ''),
            'data': event.get('data', {})
        }))
    
    async def task_completed(self, event):
        """任务完成"""
        await self.send(text_data=json.dumps({
            'type': 'task_completed',
            'task_id': event['task_id'],
            'result': event['result']
        }))
    
    async def task_failed(self, event):
        """任务失败"""
        await self.send(text_data=json.dumps({
            'type': 'task_failed',
            'task_id': event['task_id'],
            'error': event['error']
        }))
    
    @database_sync_to_async
    def get_user_from_token(self, token):
        """从token获取用户"""
        try:
            UntypedToken(token)
            from rest_framework_simplejwt.authentication import JWTAuthentication
            jwt_auth = JWTAuthentication()
            validated_token = jwt_auth.get_validated_token(token)
            user = jwt_auth.get_user(validated_token)
            return user if user.is_active else None
        except (InvalidToken, TokenError):
            return None
    
    async def subscribe_task(self, task_id):
        """订阅特定任务"""
        task_group = f"task_{task_id}"
        await self.channel_layer.group_add(
            task_group,
            self.channel_name
        )
        
        await self.send(text_data=json.dumps({
            'type': 'subscribed',
            'task_id': task_id
        }))
    
    async def unsubscribe_task(self, task_id):
        """取消订阅特定任务"""
        task_group = f"task_{task_id}"
        await self.channel_layer.group_discard(
            task_group,
            self.channel_name
        )
        
        await self.send(text_data=json.dumps({
            'type': 'unsubscribed',
            'task_id': task_id
        }))


class NotificationConsumer(AsyncWebsocketConsumer):
    """通知WebSocket消费者"""
    
    async def connect(self):
        """连接处理"""
        # 从查询参数获取token
        token = self.scope['query_string'].decode().split('token=')[-1]
        
        # 验证JWT token
        user = await self.get_user_from_token(token)
        if not user:
            await self.close()
            return
        
        self.user = user
        self.group_name = f"user_{user.id}_notifications"
        
        # 加入组
        await self.channel_layer.group_add(
            self.group_name,
            self.channel_name
        )
        
        await self.accept()
        logger.info(f"用户 {user.username} 连接到通知WebSocket")
    
    async def disconnect(self, close_code):
        """断开连接处理"""
        if hasattr(self, 'group_name'):
            await self.channel_layer.group_discard(
                self.group_name,
                self.channel_name
            )
            logger.info(f"用户 {self.user.username} 断开通知WebSocket连接")
    
    async def notification(self, event):
        """发送通知"""
        await self.send(text_data=json.dumps({
            'type': 'notification',
            'title': event['title'],
            'message': event['message'],
            'level': event.get('level', 'info'),
            'timestamp': event.get('timestamp')
        }))
    
    async def system_message(self, event):
        """系统消息"""
        await self.send(text_data=json.dumps({
            'type': 'system_message',
            'message': event['message'],
            'timestamp': event.get('timestamp')
        }))
    
    @database_sync_to_async
    def get_user_from_token(self, token):
        """从token获取用户"""
        try:
            UntypedToken(token)
            from rest_framework_simplejwt.authentication import JWTAuthentication
            jwt_auth = JWTAuthentication()
            validated_token = jwt_auth.get_validated_token(token)
            user = jwt_auth.get_user(validated_token)
            return user if user.is_active else None
        except (InvalidToken, TokenError):
            return None
