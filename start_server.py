#!/usr/bin/env python3
"""
启动Django服务器的脚本
"""
import os
import sys
import subprocess
import time

def start_django_server():
    """启动Django开发服务器"""
    
    # 设置工作目录
    backend_dir = r"C:\Users\<USER>\Desktop\CosyVoice-main\backend_refactored"
    os.chdir(backend_dir)
    
    print(f"🚀 启动Django服务器...")
    print(f"📁 工作目录: {backend_dir}")
    
    # 检查manage.py是否存在
    if not os.path.exists("manage.py"):
        print("❌ 错误: manage.py 文件不存在")
        return False
    
    try:
        # 启动Django服务器
        cmd = [sys.executable, "manage.py", "runserver", "0.0.0.0:8000"]
        print(f"🔧 执行命令: {' '.join(cmd)}")
        
        process = subprocess.Popen(
            cmd,
            stdout=subprocess.PIPE,
            stderr=subprocess.STDOUT,
            universal_newlines=True,
            bufsize=1
        )
        
        print("⏳ 等待服务器启动...")
        
        # 读取输出
        for line in iter(process.stdout.readline, ''):
            print(line.rstrip())
            
            # 检查是否启动成功
            if "Starting development server at" in line:
                print("✅ Django服务器启动成功!")
                break
            elif "Error" in line or "Exception" in line:
                print("❌ 服务器启动失败!")
                break
        
        return True
        
    except Exception as e:
        print(f"❌ 启动失败: {e}")
        return False

def test_server():
    """测试服务器是否响应"""
    import requests
    
    try:
        print("🧪 测试服务器连接...")
        response = requests.get("http://localhost:8000", timeout=5)
        print(f"✅ 服务器响应: {response.status_code}")
        return True
    except requests.exceptions.ConnectionError:
        print("❌ 无法连接到服务器")
        return False
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        return False

if __name__ == "__main__":
    print("🎯 CosyVoice Django服务器启动脚本")
    print("=" * 50)
    
    # 启动服务器
    if start_django_server():
        # 等待几秒钟
        time.sleep(3)
        
        # 测试连接
        test_server()
    else:
        print("💥 服务器启动失败!")
