# Generated by Django 4.2.7 on 2025-07-24 03:28

from django.db import migrations, models
import uuid


class Migration(migrations.Migration):
    initial = True

    dependencies = []

    operations = [
        migrations.CreateModel(
            name="APIKey",
            fields=[
                (
                    "id",
                    models.UUIDField(
                        default=uuid.uuid4,
                        editable=False,
                        primary_key=True,
                        serialize=False,
                    ),
                ),
                (
                    "created_at",
                    models.DateTimeField(auto_now_add=True, verbose_name="创建时间"),
                ),
                (
                    "updated_at",
                    models.DateTimeField(auto_now=True, verbose_name="更新时间"),
                ),
                (
                    "deleted_at",
                    models.DateTimeField(blank=True, null=True, verbose_name="删除时间"),
                ),
                ("is_active", models.BooleanField(default=True, verbose_name="是否激活")),
                ("name", models.CharField(max_length=100, verbose_name="密钥名称")),
                (
                    "key_hash",
                    models.CharField(max_length=255, unique=True, verbose_name="密钥哈希"),
                ),
                ("key_prefix", models.CharField(max_length=10, verbose_name="密钥前缀")),
                (
                    "permissions",
                    models.JSONField(blank=True, default=list, verbose_name="权限列表"),
                ),
                (
                    "expires_at",
                    models.DateTimeField(blank=True, null=True, verbose_name="过期时间"),
                ),
                (
                    "last_used",
                    models.DateTimeField(blank=True, null=True, verbose_name="最后使用时间"),
                ),
                ("usage_count", models.IntegerField(default=0, verbose_name="使用次数")),
            ],
            options={
                "verbose_name": "API密钥",
                "verbose_name_plural": "API密钥",
                "db_table": "api_keys",
            },
        ),
        migrations.CreateModel(
            name="LoginLog",
            fields=[
                (
                    "id",
                    models.UUIDField(
                        default=uuid.uuid4,
                        editable=False,
                        primary_key=True,
                        serialize=False,
                    ),
                ),
                (
                    "created_at",
                    models.DateTimeField(auto_now_add=True, verbose_name="创建时间"),
                ),
                (
                    "updated_at",
                    models.DateTimeField(auto_now=True, verbose_name="更新时间"),
                ),
                (
                    "deleted_at",
                    models.DateTimeField(blank=True, null=True, verbose_name="删除时间"),
                ),
                ("is_active", models.BooleanField(default=True, verbose_name="是否激活")),
                ("ip_address", models.GenericIPAddressField(verbose_name="IP地址")),
                ("user_agent", models.TextField(blank=True, verbose_name="用户代理")),
                (
                    "login_type",
                    models.CharField(
                        choices=[
                            ("password", "密码登录"),
                            ("api_key", "API密钥"),
                            ("token", "Token刷新"),
                        ],
                        max_length=20,
                        verbose_name="登录类型",
                    ),
                ),
                ("success", models.BooleanField(default=True, verbose_name="是否成功")),
                (
                    "failure_reason",
                    models.CharField(blank=True, max_length=200, verbose_name="失败原因"),
                ),
            ],
            options={
                "verbose_name": "登录日志",
                "verbose_name_plural": "登录日志",
                "db_table": "login_logs",
            },
        ),
        migrations.CreateModel(
            name="PasswordResetToken",
            fields=[
                (
                    "id",
                    models.UUIDField(
                        default=uuid.uuid4,
                        editable=False,
                        primary_key=True,
                        serialize=False,
                    ),
                ),
                (
                    "created_at",
                    models.DateTimeField(auto_now_add=True, verbose_name="创建时间"),
                ),
                (
                    "updated_at",
                    models.DateTimeField(auto_now=True, verbose_name="更新时间"),
                ),
                (
                    "deleted_at",
                    models.DateTimeField(blank=True, null=True, verbose_name="删除时间"),
                ),
                ("is_active", models.BooleanField(default=True, verbose_name="是否激活")),
                (
                    "token",
                    models.CharField(max_length=255, unique=True, verbose_name="令牌"),
                ),
                ("expires_at", models.DateTimeField(verbose_name="过期时间")),
                ("used", models.BooleanField(default=False, verbose_name="是否已使用")),
            ],
            options={
                "verbose_name": "密码重置令牌",
                "verbose_name_plural": "密码重置令牌",
                "db_table": "password_reset_tokens",
            },
        ),
    ]
