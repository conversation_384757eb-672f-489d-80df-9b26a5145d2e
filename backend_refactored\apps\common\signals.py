"""
Django信号处理器
用于自动权限分配和其他系统级操作
"""
from django.db.models.signals import post_migrate, post_save
from django.dispatch import receiver
from django.contrib.auth.models import Permission
from django.contrib.contenttypes.models import ContentType
from django.apps import apps
from .logger_config import logger


@receiver(post_migrate)
def assign_all_permissions_to_superusers(sender, **kwargs):
    """
    迁移后自动为所有超级用户分配所有权限
    确保admin用户始终拥有完整的系统权限
    """
    try:
        # 获取用户模型
        User = apps.get_model('users', 'User')
        
        # 获取所有权限
        all_permissions = Permission.objects.all()
        
        # 获取所有超级用户
        superusers = User.objects.filter(is_superuser=True)
        
        for user in superusers:
            # 为超级用户分配所有权限
            user.user_permissions.set(all_permissions)
            logger.info(f"为超级用户 {user.username} 分配了 {all_permissions.count()} 个权限")
        
        logger.info(f"权限分配完成，共处理 {superusers.count()} 个超级用户")
        
    except Exception as e:
        logger.error(f"自动权限分配失败: {e}")


@receiver(post_save, sender=Permission)
def assign_new_permission_to_superusers(sender, instance, created, **kwargs):
    """
    新权限创建后自动分配给所有超级用户
    """
    if created:
        try:
            # 获取用户模型
            User = apps.get_model('users', 'User')
            
            # 获取所有超级用户
            superusers = User.objects.filter(is_superuser=True)
            
            for user in superusers:
                user.user_permissions.add(instance)
            
            logger.info(f"新权限 '{instance.name}' 已自动分配给 {superusers.count()} 个超级用户")
            
        except Exception as e:
            logger.error(f"新权限自动分配失败: {e}")


@receiver(post_save)
def auto_assign_superuser_permissions(sender, instance, created, **kwargs):
    """
    当用户被设置为超级用户时，自动分配所有权限
    """
    # 检查是否是用户模型
    if sender._meta.label == 'users.User' and hasattr(instance, 'is_superuser'):
        if instance.is_superuser:
            try:
                # 获取所有权限
                all_permissions = Permission.objects.all()
                
                # 为新的超级用户分配所有权限
                instance.user_permissions.set(all_permissions)
                
                logger.info(f"为新超级用户 {instance.username} 自动分配了所有权限")
                
            except Exception as e:
                logger.error(f"超级用户权限自动分配失败: {e}")


def create_default_groups():
    """
    创建默认用户组和权限分配
    """
    try:
        from django.contrib.auth.models import Group
        
        # 定义默认组和对应权限
        default_groups = {
            '音频管理员': [
                'files.add_audiofile',
                'files.change_audiofile', 
                'files.delete_audiofile',
                'files.view_audiofile',
                'files.add_fileshare',
                'files.change_fileshare',
                'files.delete_fileshare',
                'files.view_fileshare',
                'files.view_fileoperation',
            ],
            '任务管理员': [
                'tasks.add_task',
                'tasks.change_task',
                'tasks.delete_task',
                'tasks.view_task',
                'tasks.add_tasklog',
                'tasks.change_tasklog',
                'tasks.delete_tasklog',
                'tasks.view_tasklog',
            ],
            '用户管理员': [
                'users.add_user',
                'users.change_user',
                'users.delete_user',
                'users.view_user',
                'users.add_userprofile',
                'users.change_userprofile',
                'users.delete_userprofile',
                'users.view_userprofile',
            ],
            '普通用户': [
                'files.view_audiofile',
                'tasks.view_task',
                'users.view_user',
            ]
        }
        
        for group_name, permission_codenames in default_groups.items():
            group, created = Group.objects.get_or_create(name=group_name)
            
            if created:
                logger.info(f"创建用户组: {group_name}")
            
            # 分配权限
            permissions = []
            for codename in permission_codenames:
                try:
                    app_label, perm_codename = codename.split('.')
                    permission = Permission.objects.get(
                        content_type__app_label=app_label,
                        codename=perm_codename
                    )
                    permissions.append(permission)
                except Permission.DoesNotExist:
                    logger.warning(f"权限不存在: {codename}")
                except ValueError:
                    logger.warning(f"权限格式错误: {codename}")
            
            group.permissions.set(permissions)
            logger.info(f"为用户组 {group_name} 分配了 {len(permissions)} 个权限")
        
        logger.info("默认用户组创建完成")
        
    except Exception as e:
        logger.error(f"创建默认用户组失败: {e}")


@receiver(post_migrate)
def setup_default_permissions(sender, **kwargs):
    """
    迁移后设置默认权限和用户组
    """
    # 只在最后一个应用迁移完成后执行
    if sender.name == 'apps.common':
        try:
            # 创建默认用户组
            create_default_groups()
            
            # 为超级用户分配权限
            assign_all_permissions_to_superusers(sender, **kwargs)
            
            logger.info("默认权限设置完成")
            
        except Exception as e:
            logger.error(f"默认权限设置失败: {e}")


def ensure_admin_permissions():
    """
    确保admin用户拥有所有权限的工具函数
    可以在管理命令中调用
    """
    try:
        from django.db import models
        User = apps.get_model('users', 'User')

        # 获取所有admin用户
        admin_users = User.objects.filter(
            models.Q(is_superuser=True) |
            models.Q(username='admin') |
            models.Q(email__icontains='admin')
        )
        
        # 获取所有权限
        all_permissions = Permission.objects.all()
        
        for user in admin_users:
            user.user_permissions.set(all_permissions)
            user.is_superuser = True
            user.is_staff = True
            user.save()
            
            logger.info(f"确保用户 {user.username} 拥有完整权限")
        
        return admin_users.count()
        
    except Exception as e:
        logger.error(f"确保admin权限失败: {e}")
        return 0
