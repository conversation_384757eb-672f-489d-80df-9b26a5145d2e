@echo off
echo ===================================
echo    CosyVoice系统启动脚本 (Windows)
echo ===================================

REM 检查conda是否安装
where conda >nul 2>nul
if %errorlevel% neq 0 (
    echo 错误: 未找到conda命令，请确保Anaconda/Miniconda已安装并添加到PATH
    pause
    exit /b 1
)

REM 设置环境变量
set COSYVOICE_CONDA_ENV=cosyvoice
set DJANGO_PORT=8000
set COSYVOICE_SERVICE_PORT=8001
set COSYVOICE_MODEL_DIR=pretrained_models/CosyVoice-300M

echo 正在检查conda环境: %COSYVOICE_CONDA_ENV%
conda env list | findstr %COSYVOICE_CONDA_ENV% >nul
if %errorlevel% neq 0 (
    echo 错误: conda环境 '%COSYVOICE_CONDA_ENV%' 不存在
    echo 请先创建并配置cosyvoice环境
    pause
    exit /b 1
)

echo 正在检查模型目录: %COSYVOICE_MODEL_DIR%
if not exist "%COSYVOICE_MODEL_DIR%" (
    echo 错误: 模型目录不存在: %COSYVOICE_MODEL_DIR%
    echo 请确保已下载CosyVoice模型
    pause
    exit /b 1
)

echo.
echo 启动CosyVoice系统...
echo Django服务将在端口 %DJANGO_PORT% 启动
echo CosyVoice服务将在端口 %COSYVOICE_SERVICE_PORT% 启动
echo.

REM 激活conda环境并启动系统
call conda activate %COSYVOICE_CONDA_ENV%
if %errorlevel% neq 0 (
    echo 错误: 无法激活conda环境
    pause
    exit /b 1
)

REM 启动Python系统管理器
python start_cosyvoice_system.py

pause
