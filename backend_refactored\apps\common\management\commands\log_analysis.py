"""
日志分析管理命令
"""
import os
import re
import json
from datetime import datetime, timedelta
from collections import defaultdict, Counter
from django.core.management.base import BaseCommand
from django.conf import settings


class Command(BaseCommand):
    help = '分析系统日志文件'
    
    def add_arguments(self, parser):
        parser.add_argument(
            '--log-type',
            choices=['app', 'api', 'error', 'tasks', 'security', 'performance'],
            default='app',
            help='要分析的日志类型'
        )
        
        parser.add_argument(
            '--hours',
            type=int,
            default=24,
            help='分析最近多少小时的日志，默认24小时'
        )
        
        parser.add_argument(
            '--output',
            choices=['console', 'json', 'csv'],
            default='console',
            help='输出格式'
        )
        
        parser.add_argument(
            '--save-to',
            type=str,
            help='保存分析结果到文件'
        )
    
    def handle(self, *args, **options):
        log_type = options['log_type']
        hours = options['hours']
        output_format = options['output']
        save_to = options['save_to']
        
        self.stdout.write(f"分析 {log_type} 日志，时间范围: 最近 {hours} 小时")
        
        # 获取日志文件路径
        log_file = self.get_log_file_path(log_type)
        if not os.path.exists(log_file):
            self.stdout.write(self.style.ERROR(f"日志文件不存在: {log_file}"))
            return
        
        # 分析日志
        analysis_result = self.analyze_log_file(log_file, hours, log_type)
        
        # 输出结果
        if output_format == 'console':
            self.output_console(analysis_result, log_type)
        elif output_format == 'json':
            self.output_json(analysis_result, save_to)
        elif output_format == 'csv':
            self.output_csv(analysis_result, save_to)
    
    def get_log_file_path(self, log_type):
        """获取日志文件路径"""
        log_dir = os.path.join(settings.BASE_DIR, 'logs')
        return os.path.join(log_dir, f"{log_type}.log")
    
    def analyze_log_file(self, log_file, hours, log_type):
        """分析日志文件"""
        cutoff_time = datetime.now() - timedelta(hours=hours)
        
        analysis = {
            'total_lines': 0,
            'time_range': {
                'start': cutoff_time.isoformat(),
                'end': datetime.now().isoformat()
            },
            'levels': defaultdict(int),
            'hourly_stats': defaultdict(int),
            'top_messages': Counter(),
            'errors': [],
            'warnings': [],
        }
        
        # 根据日志类型添加特定分析
        if log_type == 'api':
            analysis.update({
                'methods': defaultdict(int),
                'status_codes': defaultdict(int),
                'response_times': [],
                'top_endpoints': Counter(),
                'top_users': Counter(),
            })
        elif log_type == 'tasks':
            analysis.update({
                'task_events': defaultdict(int),
                'task_types': defaultdict(int),
                'task_durations': [],
            })
        elif log_type == 'security':
            analysis.update({
                'event_types': defaultdict(int),
                'top_ips': Counter(),
                'failed_logins': [],
            })
        elif log_type == 'performance':
            analysis.update({
                'metrics': defaultdict(list),
                'slow_operations': [],
            })
        
        # 读取和分析日志
        with open(log_file, 'r', encoding='utf-8') as f:
            for line in f:
                line = line.strip()
                if not line:
                    continue
                
                analysis['total_lines'] += 1
                
                # 解析日志行
                log_entry = self.parse_log_line(line)
                if not log_entry:
                    continue
                
                # 检查时间范围
                if log_entry['timestamp'] < cutoff_time:
                    continue
                
                # 基础统计
                analysis['levels'][log_entry['level']] += 1
                hour_key = log_entry['timestamp'].strftime('%Y-%m-%d %H:00')
                analysis['hourly_stats'][hour_key] += 1
                
                # 收集错误和警告
                if log_entry['level'] == 'ERROR':
                    analysis['errors'].append({
                        'timestamp': log_entry['timestamp'].isoformat(),
                        'message': log_entry['message']
                    })
                elif log_entry['level'] == 'WARNING':
                    analysis['warnings'].append({
                        'timestamp': log_entry['timestamp'].isoformat(),
                        'message': log_entry['message']
                    })
                
                # 消息统计
                analysis['top_messages'][log_entry['message'][:100]] += 1
                
                # 特定类型分析
                self.analyze_specific_log_type(log_entry, analysis, log_type)
        
        # 处理统计数据
        analysis['top_messages'] = dict(analysis['top_messages'].most_common(10))
        
        return analysis
    
    def parse_log_line(self, line):
        """解析日志行"""
        # 匹配Loguru日志格式
        pattern = r'(\d{4}-\d{2}-\d{2} \d{2}:\d{2}:\d{2}) \| (\w+)\s+\| (.+)'
        match = re.match(pattern, line)
        
        if not match:
            return None
        
        timestamp_str, level, message = match.groups()
        
        try:
            timestamp = datetime.strptime(timestamp_str, '%Y-%m-%d %H:%M:%S')
        except ValueError:
            return None
        
        return {
            'timestamp': timestamp,
            'level': level,
            'message': message
        }
    
    def analyze_specific_log_type(self, log_entry, analysis, log_type):
        """分析特定类型的日志"""
        message = log_entry['message']
        
        if log_type == 'api':
            # 解析API日志
            if 'API请求:' in message:
                # 提取API信息
                try:
                    # 简单的正则匹配，实际可能需要更复杂的解析
                    if "'method':" in message:
                        method_match = re.search(r"'method': '(\w+)'", message)
                        if method_match:
                            analysis['methods'][method_match.group(1)] += 1
                    
                    if "'status_code':" in message:
                        status_match = re.search(r"'status_code': (\d+)", message)
                        if status_match:
                            analysis['status_codes'][status_match.group(1)] += 1
                    
                    if "'path':" in message:
                        path_match = re.search(r"'path': '([^']+)'", message)
                        if path_match:
                            analysis['top_endpoints'][path_match.group(1)] += 1
                    
                    if "'duration':" in message:
                        duration_match = re.search(r"'duration': '([\d.]+)s'", message)
                        if duration_match:
                            analysis['response_times'].append(float(duration_match.group(1)))
                            
                except Exception:
                    pass
        
        elif log_type == 'tasks':
            # 解析任务日志
            if '任务事件:' in message:
                if "'event':" in message:
                    event_match = re.search(r"'event': '([^']+)'", message)
                    if event_match:
                        analysis['task_events'][event_match.group(1)] += 1
        
        elif log_type == 'security':
            # 解析安全日志
            if '安全事件:' in message:
                if "'event_type':" in message:
                    event_match = re.search(r"'event_type': '([^']+)'", message)
                    if event_match:
                        analysis['event_types'][event_match.group(1)] += 1
        
        elif log_type == 'performance':
            # 解析性能日志
            if '性能指标:' in message:
                if "'metric':" in message and "'value':" in message:
                    metric_match = re.search(r"'metric': '([^']+)'", message)
                    value_match = re.search(r"'value': ([\d.]+)", message)
                    if metric_match and value_match:
                        metric_name = metric_match.group(1)
                        value = float(value_match.group(1))
                        analysis['metrics'][metric_name].append(value)
    
    def output_console(self, analysis, log_type):
        """控制台输出"""
        self.stdout.write(self.style.SUCCESS(f"\n=== {log_type.upper()} 日志分析报告 ==="))
        
        # 基础统计
        self.stdout.write(f"总日志行数: {analysis['total_lines']}")
        self.stdout.write(f"时间范围: {analysis['time_range']['start']} 到 {analysis['time_range']['end']}")
        
        # 日志级别统计
        self.stdout.write("\n--- 日志级别统计 ---")
        for level, count in sorted(analysis['levels'].items()):
            self.stdout.write(f"{level}: {count}")
        
        # 错误和警告
        if analysis['errors']:
            self.stdout.write(f"\n--- 错误日志 (最近10条) ---")
            for error in analysis['errors'][-10:]:
                self.stdout.write(f"{error['timestamp']}: {error['message'][:100]}")
        
        if analysis['warnings']:
            self.stdout.write(f"\n--- 警告日志 (最近10条) ---")
            for warning in analysis['warnings'][-10:]:
                self.stdout.write(f"{warning['timestamp']}: {warning['message'][:100]}")
        
        # 特定类型统计
        if log_type == 'api' and 'methods' in analysis:
            self.stdout.write("\n--- HTTP方法统计 ---")
            for method, count in analysis['methods'].items():
                self.stdout.write(f"{method}: {count}")
            
            if analysis['response_times']:
                avg_time = sum(analysis['response_times']) / len(analysis['response_times'])
                self.stdout.write(f"\n平均响应时间: {avg_time:.3f}s")
    
    def output_json(self, analysis, save_to):
        """JSON输出"""
        # 转换不可序列化的对象
        json_analysis = self.prepare_for_json(analysis)
        
        if save_to:
            with open(save_to, 'w', encoding='utf-8') as f:
                json.dump(json_analysis, f, indent=2, ensure_ascii=False)
            self.stdout.write(f"分析结果已保存到: {save_to}")
        else:
            self.stdout.write(json.dumps(json_analysis, indent=2, ensure_ascii=False))
    
    def output_csv(self, analysis, save_to):
        """CSV输出"""
        import csv
        
        if not save_to:
            save_to = f"log_analysis_{datetime.now().strftime('%Y%m%d_%H%M%S')}.csv"
        
        with open(save_to, 'w', newline='', encoding='utf-8') as f:
            writer = csv.writer(f)
            
            # 写入基础统计
            writer.writerow(['指标', '值'])
            writer.writerow(['总日志行数', analysis['total_lines']])
            
            # 写入级别统计
            writer.writerow([])
            writer.writerow(['日志级别', '数量'])
            for level, count in analysis['levels'].items():
                writer.writerow([level, count])
        
        self.stdout.write(f"分析结果已保存到: {save_to}")
    
    def prepare_for_json(self, obj):
        """准备JSON序列化"""
        if isinstance(obj, defaultdict):
            return dict(obj)
        elif isinstance(obj, Counter):
            return dict(obj)
        elif isinstance(obj, dict):
            return {k: self.prepare_for_json(v) for k, v in obj.items()}
        elif isinstance(obj, list):
            return [self.prepare_for_json(item) for item in obj]
        else:
            return obj
