"""
文件管理视图
"""
import os
from django.db import models
from django.conf import settings
from rest_framework import generics, permissions, status
from rest_framework.decorators import api_view, permission_classes
from rest_framework.response import Response
from django.http import Http404, FileResponse
from django.shortcuts import get_object_or_404
from django.utils import timezone
from datetime import timed<PERSON><PERSON>
from drf_spectacular.utils import extend_schema
from .models import AudioFile, FileShare, FileOperation, FileType
from .serializers import (
    AudioFileSerializer, AudioFileListSerializer, AudioFileUploadSerializer,
    FileShareSerializer, FileOperationSerializer, FileStatsSerializer,
    FileBatchOperationSerializer, FileCleanupSerializer
)
from .services import FileService
from apps.users.models import UserRole
from loguru import logger


class AudioFileListView(generics.ListAPIView):
    """音频文件列表"""
    serializer_class = AudioFileListSerializer
    permission_classes = [permissions.IsAuthenticated]
    
    def get_queryset(self):
        queryset = AudioFile.objects.filter(is_active=True)
        
        # 普通用户只能看到自己的文件
        if self.request.user.role != UserRole.ADMIN:
            queryset = queryset.filter(user=self.request.user)
        
        # 过滤参数
        file_type = self.request.query_params.get('file_type')
        if file_type:
            queryset = queryset.filter(file_type=file_type)
        
        is_public = self.request.query_params.get('is_public')
        if is_public is not None:
            queryset = queryset.filter(is_public=is_public.lower() == 'true')
        
        return queryset.order_by('-created_at')
    
    @extend_schema(
        summary="获取音频文件列表",
        description="获取音频文件列表，支持按类型和公开状态过滤",
        tags=["文件管理"]
    )
    def get(self, request, *args, **kwargs):
        return super().get(request, *args, **kwargs)


class AudioFileDetailView(generics.RetrieveUpdateDestroyAPIView):
    """音频文件详情"""
    serializer_class = AudioFileSerializer
    permission_classes = [permissions.IsAuthenticated]
    
    def get_queryset(self):
        queryset = AudioFile.objects.filter(is_active=True)
        
        # 普通用户只能访问自己的文件
        if self.request.user.role != UserRole.ADMIN:
            queryset = queryset.filter(user=self.request.user)
        
        return queryset
    
    @extend_schema(
        summary="获取音频文件详情",
        tags=["文件管理"]
    )
    def get(self, request, *args, **kwargs):
        return super().get(request, *args, **kwargs)
    
    @extend_schema(
        summary="更新音频文件",
        tags=["文件管理"]
    )
    def put(self, request, *args, **kwargs):
        return super().put(request, *args, **kwargs)
    
    @extend_schema(
        summary="删除音频文件",
        tags=["文件管理"]
    )
    def delete(self, request, *args, **kwargs):
        audio_file = self.get_object()
        
        # 删除物理文件
        file_service = FileService()
        file_service.delete_file(audio_file)
        
        # 记录操作
        FileOperation.objects.create(
            user=request.user,
            audio_file=audio_file,
            operation='delete',
            ip_address=self.get_client_ip(request),
            user_agent=request.META.get('HTTP_USER_AGENT', '')
        )
        
        logger.info(f"用户删除文件: {request.user.username} - {audio_file.filename}")
        
        return Response({'message': '文件删除成功'})
    
    def get_client_ip(self, request):
        """获取客户端IP"""
        x_forwarded_for = request.META.get('HTTP_X_FORWARDED_FOR')
        if x_forwarded_for:
            ip = x_forwarded_for.split(',')[0]
        else:
            ip = request.META.get('REMOTE_ADDR')
        return ip


class AudioFileUploadView(generics.CreateAPIView):
    """音频文件上传"""
    serializer_class = AudioFileUploadSerializer
    permission_classes = [permissions.IsAuthenticated]
    
    @extend_schema(
        summary="上传音频文件",
        description="上传音频文件到系统",
        tags=["文件管理"]
    )
    def post(self, request, *args, **kwargs):
        # 检查用户存储配额
        user = request.user
        current_usage = AudioFile.objects.filter(
            user=user, is_active=True
        ).aggregate(total=models.Sum('file_size'))['total'] or 0
        
        # 假设每个用户有1GB存储配额
        max_storage = 1024 * 1024 * 1024  # 1GB
        if current_usage >= max_storage:
            return Response({
                'error': '存储配额已满',
                'current_usage': current_usage,
                'max_storage': max_storage
            }, status=status.HTTP_413_REQUEST_ENTITY_TOO_LARGE)
        
        response = super().post(request, *args, **kwargs)
        
        if response.status_code == status.HTTP_201_CREATED:
            logger.info(f"用户上传文件: {user.username}")
        
        return response


class FileShareListCreateView(generics.ListCreateAPIView):
    """文件分享列表和创建"""
    serializer_class = FileShareSerializer
    permission_classes = [permissions.IsAuthenticated]
    
    def get_queryset(self):
        return FileShare.objects.filter(
            audio_file__user=self.request.user,
            is_active=True
        ).order_by('-created_at')
    
    @extend_schema(
        summary="获取文件分享列表",
        tags=["文件管理"]
    )
    def get(self, request, *args, **kwargs):
        return super().get(request, *args, **kwargs)
    
    @extend_schema(
        summary="创建文件分享",
        tags=["文件管理"]
    )
    def post(self, request, *args, **kwargs):
        return super().post(request, *args, **kwargs)


class FileShareDetailView(generics.RetrieveUpdateDestroyAPIView):
    """文件分享详情"""
    serializer_class = FileShareSerializer
    permission_classes = [permissions.IsAuthenticated]
    
    def get_queryset(self):
        return FileShare.objects.filter(
            audio_file__user=self.request.user,
            is_active=True
        )
    
    @extend_schema(
        summary="获取文件分享详情",
        tags=["文件管理"]
    )
    def get(self, request, *args, **kwargs):
        return super().get(request, *args, **kwargs)
    
    @extend_schema(
        summary="更新文件分享",
        tags=["文件管理"]
    )
    def put(self, request, *args, **kwargs):
        return super().put(request, *args, **kwargs)
    
    @extend_schema(
        summary="删除文件分享",
        tags=["文件管理"]
    )
    def delete(self, request, *args, **kwargs):
        file_share = self.get_object()
        file_share.soft_delete()
        return Response({'message': '分享已删除'})


@extend_schema(
    summary="通过分享链接下载文件",
    description="通过分享令牌下载文件",
    tags=["文件管理"]
)
@api_view(['GET'])
@permission_classes([permissions.AllowAny])
def download_shared_file(request, share_token):
    """通过分享链接下载文件"""
    try:
        file_share = get_object_or_404(FileShare, share_token=share_token, is_active=True)
        
        # 检查分享是否有效
        if not file_share.is_valid:
            return Response({'error': '分享链接已失效'}, status=status.HTTP_410_GONE)
        
        # 检查密码
        password = request.GET.get('password')
        if file_share.password and password != file_share.password:
            return Response({'error': '密码错误'}, status=status.HTTP_401_UNAUTHORIZED)
        
        # 记录下载
        file_share.record_download()
        
        # 记录操作
        FileOperation.objects.create(
            audio_file=file_share.audio_file,
            operation='download',
            ip_address=get_client_ip(request),
            user_agent=request.META.get('HTTP_USER_AGENT', ''),
            extra_data={'share_token': share_token}
        )
        
        # 返回文件
        audio_file = file_share.audio_file
        file_path = os.path.join(settings.MEDIA_ROOT, audio_file.file_path)
        
        if not os.path.exists(file_path):
            raise Http404('文件不存在')
        
        response = FileResponse(
            open(file_path, 'rb'),
            content_type=audio_file.mime_type,
            as_attachment=True,
            filename=audio_file.original_filename or audio_file.filename
        )
        
        return response
        
    except Exception as e:
        logger.error(f"分享文件下载失败: {share_token}, 错误: {e}")
        return Response({'error': str(e)}, status=status.HTTP_500_INTERNAL_SERVER_ERROR)


@extend_schema(
    summary="获取文件统计",
    description="获取文件使用统计信息",
    responses=FileStatsSerializer,
    tags=["文件管理"]
)
@api_view(['GET'])
@permission_classes([permissions.IsAuthenticated])
def file_stats(request):
    """文件统计"""
    file_service = FileService()
    
    # 普通用户只能看到自己的统计
    user = None if request.user.role == UserRole.ADMIN else request.user
    
    stats = file_service.get_file_stats(user)
    serializer = FileStatsSerializer(stats)
    
    return Response(serializer.data)


@extend_schema(
    summary="批量操作文件",
    description="对多个文件执行批量操作",
    request=FileBatchOperationSerializer,
    tags=["文件管理"]
)
@api_view(['POST'])
@permission_classes([permissions.IsAuthenticated])
def batch_file_operation(request):
    """批量文件操作"""
    serializer = FileBatchOperationSerializer(data=request.data)
    
    if serializer.is_valid():
        file_ids = serializer.validated_data['file_ids']
        operation = serializer.validated_data['operation']
        
        # 获取用户的文件
        queryset = AudioFile.objects.filter(
            id__in=file_ids,
            is_active=True
        )
        
        if request.user.role != UserRole.ADMIN:
            queryset = queryset.filter(user=request.user)
        
        files = list(queryset)
        if len(files) != len(file_ids):
            return Response({'error': '部分文件不存在或无权限访问'}, 
                          status=status.HTTP_400_BAD_REQUEST)
        
        # 执行操作
        success_count = 0
        
        if operation == 'delete':
            file_service = FileService()
            for audio_file in files:
                if file_service.delete_file(audio_file):
                    success_count += 1
        
        elif operation == 'make_public':
            for audio_file in files:
                audio_file.is_public = True
                audio_file.save(update_fields=['is_public'])
                success_count += 1
        
        elif operation == 'make_private':
            for audio_file in files:
                audio_file.is_public = False
                audio_file.save(update_fields=['is_public'])
                success_count += 1
        
        elif operation == 'extend_expiry':
            extend_days = serializer.validated_data['extend_days']
            for audio_file in files:
                if audio_file.expires_at:
                    audio_file.expires_at += timedelta(days=extend_days)
                else:
                    audio_file.expires_at = timezone.now() + timedelta(days=extend_days)
                audio_file.save(update_fields=['expires_at'])
                success_count += 1
        
        logger.info(f"批量文件操作: {request.user.username} - {operation} - {success_count}个文件")
        
        return Response({
            'message': f'成功处理 {success_count} 个文件',
            'success_count': success_count,
            'total_count': len(files)
        })
    
    return Response(serializer.errors, status=status.HTTP_400_BAD_REQUEST)


def get_client_ip(request):
    """获取客户端IP"""
    x_forwarded_for = request.META.get('HTTP_X_FORWARDED_FOR')
    if x_forwarded_for:
        ip = x_forwarded_for.split(',')[0]
    else:
        ip = request.META.get('REMOTE_ADDR')
    return ip
