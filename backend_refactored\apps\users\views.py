"""
用户相关视图
"""
from rest_framework import generics, permissions, status
from rest_framework.decorators import api_view, permission_classes
from rest_framework.response import Response
from django.contrib.auth import get_user_model
from django.db.models import Count, Sum, Q
from drf_spectacular.utils import extend_schema
from .serializers import (
    UserSerializer, UserUpdateSerializer, UserListSerializer,
    UserStatsSerializer, QuotaUpdateSerializer
)
from .models import UserRole
from apps.tasks.models import Task, TaskStatus
from apps.files.models import AudioFile
from loguru import logger

User = get_user_model()


class UserProfileView(generics.RetrieveUpdateAPIView):
    """用户资料"""
    permission_classes = [permissions.IsAuthenticated]
    
    def get_serializer_class(self):
        if self.request.method == 'GET':
            return UserSerializer
        return UserUpdateSerializer
    
    def get_object(self):
        return self.request.user
    
    @extend_schema(
        summary="获取用户资料",
        description="获取当前用户的详细资料",
        tags=["用户管理"]
    )
    def get(self, request, *args, **kwargs):
        return super().get(request, *args, **kwargs)
    
    @extend_schema(
        summary="更新用户资料",
        description="更新当前用户的资料信息",
        tags=["用户管理"]
    )
    def put(self, request, *args, **kwargs):
        response = super().put(request, *args, **kwargs)
        if response.status_code == status.HTTP_200_OK:
            logger.info(f"用户更新资料: {request.user.username}")
        return response


class UserListView(generics.ListAPIView):
    """用户列表（管理员）"""
    serializer_class = UserListSerializer
    permission_classes = [permissions.IsAuthenticated]
    
    def get_queryset(self):
        # 只有管理员可以查看所有用户
        if self.request.user.role == UserRole.ADMIN:
            return User.objects.filter(is_active=True).order_by('-created_at')
        else:
            # 普通用户只能看到自己
            return User.objects.filter(id=self.request.user.id)
    
    @extend_schema(
        summary="获取用户列表",
        description="获取用户列表（管理员可查看所有用户，普通用户只能查看自己）",
        tags=["用户管理"]
    )
    def get(self, request, *args, **kwargs):
        return super().get(request, *args, **kwargs)


class UserDetailView(generics.RetrieveAPIView):
    """用户详情（管理员）"""
    serializer_class = UserSerializer
    permission_classes = [permissions.IsAuthenticated]
    
    def get_queryset(self):
        if self.request.user.role == UserRole.ADMIN:
            return User.objects.filter(is_active=True)
        else:
            return User.objects.filter(id=self.request.user.id)
    
    @extend_schema(
        summary="获取用户详情",
        description="获取指定用户的详细信息",
        tags=["用户管理"]
    )
    def get(self, request, *args, **kwargs):
        return super().get(request, *args, **kwargs)


@extend_schema(
    summary="获取用户统计",
    description="获取当前用户的使用统计信息",
    responses=UserStatsSerializer,
    tags=["用户管理"]
)
@api_view(['GET'])
@permission_classes([permissions.IsAuthenticated])
def user_stats(request):
    """用户统计"""
    user = request.user
    
    # 任务统计
    task_stats = Task.objects.filter(user=user).aggregate(
        total_tasks=Count('id'),
        completed_tasks=Count('id', filter=Q(status=TaskStatus.COMPLETED)),
        failed_tasks=Count('id', filter=Q(status=TaskStatus.FAILED)),
    )
    
    # 文件统计
    file_stats = AudioFile.objects.filter(user=user, is_active=True).aggregate(
        total_files=Count('id'),
        total_file_size=Sum('file_size') or 0,
    )
    
    # 最后活动时间
    last_activity = user.last_login or user.created_at
    
    stats_data = {
        'total_tasks': task_stats['total_tasks'] or 0,
        'completed_tasks': task_stats['completed_tasks'] or 0,
        'failed_tasks': task_stats['failed_tasks'] or 0,
        'total_files': file_stats['total_files'] or 0,
        'total_file_size': file_stats['total_file_size'],
        'quota_used': user.quota_used,
        'quota_limit': user.quota_limit,
        'quota_usage_percentage': user.quota_usage_percentage,
        'last_activity': last_activity,
    }
    
    serializer = UserStatsSerializer(stats_data)
    return Response(serializer.data)


@extend_schema(
    summary="更新用户配额",
    description="更新指定用户的配额限制（仅管理员）",
    request=QuotaUpdateSerializer,
    tags=["用户管理"]
)
@api_view(['POST'])
@permission_classes([permissions.IsAuthenticated])
def update_user_quota(request, user_id):
    """更新用户配额（管理员）"""
    if request.user.role != UserRole.ADMIN:
        return Response({'error': '权限不足'}, status=status.HTTP_403_FORBIDDEN)
    
    try:
        user = User.objects.get(id=user_id, is_active=True)
    except User.DoesNotExist:
        return Response({'error': '用户不存在'}, status=status.HTTP_404_NOT_FOUND)
    
    serializer = QuotaUpdateSerializer(data=request.data, context={'user': user})
    
    if serializer.is_valid():
        quota_limit = serializer.validated_data['quota_limit']
        user.quota_limit = quota_limit
        user.save(update_fields=['quota_limit'])
        
        logger.info(f"管理员更新用户配额: {request.user.username} -> {user.username} ({quota_limit})")
        
        return Response({
            'message': '配额更新成功',
            'user': {
                'id': str(user.id),
                'username': user.username,
                'quota_limit': user.quota_limit,
                'quota_used': user.quota_used,
                'quota_remaining': user.quota_remaining,
            }
        })
    
    return Response(serializer.errors, status=status.HTTP_400_BAD_REQUEST)


@extend_schema(
    summary="重置用户配额使用量",
    description="重置指定用户的配额使用量为0（仅管理员）",
    tags=["用户管理"]
)
@api_view(['POST'])
@permission_classes([permissions.IsAuthenticated])
def reset_user_quota(request, user_id):
    """重置用户配额使用量（管理员）"""
    if request.user.role != UserRole.ADMIN:
        return Response({'error': '权限不足'}, status=status.HTTP_403_FORBIDDEN)
    
    try:
        user = User.objects.get(id=user_id, is_active=True)
    except User.DoesNotExist:
        return Response({'error': '用户不存在'}, status=status.HTTP_404_NOT_FOUND)
    
    user.quota_used = 0
    user.save(update_fields=['quota_used'])
    
    logger.info(f"管理员重置用户配额: {request.user.username} -> {user.username}")
    
    return Response({
        'message': '配额使用量重置成功',
        'user': {
            'id': str(user.id),
            'username': user.username,
            'quota_limit': user.quota_limit,
            'quota_used': user.quota_used,
            'quota_remaining': user.quota_remaining,
        }
    })


@extend_schema(
    summary="禁用/启用用户",
    description="禁用或启用指定用户账户（仅管理员）",
    tags=["用户管理"]
)
@api_view(['POST'])
@permission_classes([permissions.IsAuthenticated])
def toggle_user_status(request, user_id):
    """禁用/启用用户（管理员）"""
    if request.user.role != UserRole.ADMIN:
        return Response({'error': '权限不足'}, status=status.HTTP_403_FORBIDDEN)
    
    try:
        user = User.objects.get(id=user_id)
    except User.DoesNotExist:
        return Response({'error': '用户不存在'}, status=status.HTTP_404_NOT_FOUND)
    
    if user.role == UserRole.ADMIN and user != request.user:
        return Response({'error': '不能禁用其他管理员'}, status=status.HTTP_400_BAD_REQUEST)
    
    user.is_active = not user.is_active
    user.save(update_fields=['is_active'])
    
    action = '启用' if user.is_active else '禁用'
    logger.info(f"管理员{action}用户: {request.user.username} -> {user.username}")
    
    return Response({
        'message': f'用户已{action}',
        'user': {
            'id': str(user.id),
            'username': user.username,
            'is_active': user.is_active,
        }
    })
