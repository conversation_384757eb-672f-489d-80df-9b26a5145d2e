"""
任务管理模型
"""
from django.db import models
from django.contrib.auth import get_user_model
from apps.common.models import BaseModel

User = get_user_model()


class TaskType(models.TextChoices):
    """任务类型"""
    SFT = 'sft', 'SFT模式推理'
    ZERO_SHOT = 'zero_shot', '零样本语音克隆'
    CROSS_LINGUAL = 'cross_lingual', '跨语种复刻'
    INSTRUCT = 'instruct', '自然语言控制'
    INSTRUCT2 = 'instruct2', '高级自然语言控制'


class TaskStatus(models.TextChoices):
    """任务状态"""
    PENDING = 'pending', '等待中'
    PROCESSING = 'processing', '处理中'
    COMPLETED = 'completed', '已完成'
    FAILED = 'failed', '失败'
    CANCELLED = 'cancelled', '已取消'


class TaskPriority(models.TextChoices):
    """任务优先级"""
    LOW = 'low', '低'
    NORMAL = 'normal', '普通'
    HIGH = 'high', '高'
    URGENT = 'urgent', '紧急'


class Task(BaseModel):
    """任务模型"""
    user = models.ForeignKey(User, on_delete=models.CASCADE, related_name='tasks', null=True, blank=True)
    task_type = models.CharField('任务类型', max_length=20, choices=TaskType.choices)
    status = models.CharField('任务状态', max_length=20, choices=TaskStatus.choices, default=TaskStatus.PENDING)
    priority = models.CharField('优先级', max_length=10, choices=TaskPriority.choices, default=TaskPriority.NORMAL)
    
    # 请求数据
    request_data = models.JSONField('请求数据')
    
    # 响应数据
    response_data = models.JSONField('响应数据', null=True, blank=True)
    
    # 关联的音频文件
    audio_file = models.ForeignKey('files.AudioFile', on_delete=models.SET_NULL, null=True, blank=True, related_name='tasks')
    
    # 时间记录
    started_at = models.DateTimeField('开始时间', null=True, blank=True)
    completed_at = models.DateTimeField('完成时间', null=True, blank=True)
    
    # 错误信息
    error_message = models.TextField('错误信息', blank=True)
    retry_count = models.IntegerField('重试次数', default=0)
    max_retries = models.IntegerField('最大重试次数', default=3)
    
    # 性能指标
    processing_time = models.FloatField('处理时间(秒)', null=True, blank=True)
    queue_time = models.FloatField('队列等待时间(秒)', null=True, blank=True)
    
    # 客户端信息
    client_ip = models.GenericIPAddressField('客户端IP', null=True, blank=True)
    user_agent = models.TextField('用户代理', blank=True)
    
    class Meta:
        db_table = 'tasks'
        verbose_name = '任务'
        verbose_name_plural = '任务'
        indexes = [
            models.Index(fields=['user', 'created_at']),
            models.Index(fields=['status']),
            models.Index(fields=['task_type', 'status']),
            models.Index(fields=['priority', 'created_at']),
            models.Index(fields=['created_at']),
        ]
        ordering = ['-created_at']

    def __str__(self):
        user_info = f"{self.user.username}" if self.user else "匿名用户"
        return f"{user_info} - {self.get_task_type_display()} - {self.get_status_display()}"

    @property
    def can_retry(self):
        """是否可以重试"""
        return self.status == TaskStatus.FAILED and self.retry_count < self.max_retries

    def mark_processing(self):
        """标记为处理中"""
        from django.utils import timezone
        self.status = TaskStatus.PROCESSING
        self.started_at = timezone.now()
        if self.created_at:
            self.queue_time = (self.started_at - self.created_at).total_seconds()
        self.save(update_fields=['status', 'started_at', 'queue_time'])

        # 发送状态更新通知
        from .utils import notify_task_status_update
        notify_task_status_update(self, '任务开始处理')

    def mark_completed(self, response_data=None, audio_file=None):
        """标记为完成"""
        from django.utils import timezone
        self.status = TaskStatus.COMPLETED
        self.completed_at = timezone.now()
        if self.started_at:
            self.processing_time = (self.completed_at - self.started_at).total_seconds()
        if response_data:
            self.response_data = response_data
        if audio_file:
            self.audio_file = audio_file
        self.save(update_fields=['status', 'completed_at', 'processing_time', 'response_data', 'audio_file'])

        # 发送完成通知
        from .utils import notify_task_completed
        notify_task_completed(self, response_data)

    def mark_failed(self, error_message):
        """标记为失败"""
        from django.utils import timezone
        self.status = TaskStatus.FAILED
        self.error_message = error_message
        self.completed_at = timezone.now()
        if self.started_at:
            self.processing_time = (self.completed_at - self.started_at).total_seconds()
        self.save(update_fields=['status', 'error_message', 'completed_at', 'processing_time'])

        # 发送失败通知
        from .utils import notify_task_failed
        notify_task_failed(self, error_message)

    def increment_retry(self):
        """增加重试次数"""
        self.retry_count += 1
        self.status = TaskStatus.PENDING
        self.error_message = ''
        self.started_at = None
        self.completed_at = None
        self.save(update_fields=['retry_count', 'status', 'error_message', 'started_at', 'completed_at'])


class TaskLog(BaseModel):
    """任务日志"""
    task = models.ForeignKey(Task, on_delete=models.CASCADE, related_name='logs')
    level = models.CharField('日志级别', max_length=10, choices=[
        ('DEBUG', 'DEBUG'),
        ('INFO', 'INFO'),
        ('WARNING', 'WARNING'),
        ('ERROR', 'ERROR'),
    ])
    message = models.TextField('日志消息')
    extra_data = models.JSONField('额外数据', null=True, blank=True)
    
    class Meta:
        db_table = 'task_logs'
        verbose_name = '任务日志'
        verbose_name_plural = '任务日志'
        indexes = [
            models.Index(fields=['task', 'created_at']),
            models.Index(fields=['level']),
        ]
        ordering = ['-created_at']

    def __str__(self):
        return f"{self.task} - {self.level} - {self.message[:50]}"
