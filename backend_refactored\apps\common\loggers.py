#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
专门的日志记录器模块
提供各种类型的日志记录功能
"""

import logging
import time
import json
from functools import wraps
from typing import Any, Dict, Optional
from django.utils import timezone
from django.http import HttpRequest

# 获取各种专门的日志记录器
api_logger = logging.getLogger('api_logger')
performance_logger = logging.getLogger('performance_logger')
security_logger = logging.getLogger('security_logger')
tasks_logger = logging.getLogger('tasks_logger')
app_logger = logging.getLogger('apps.common')

class APILogger:
    """API调用日志记录器"""
    
    @staticmethod
    def log_request(request: HttpRequest, extra_data: Optional[Dict] = None):
        """记录API请求"""
        data = {
            'method': request.method,
            'path': request.path,
            'query_params': dict(request.GET),
            'client_ip': get_client_ip(request),
            'user_agent': request.META.get('HTTP_USER_AGENT', ''),
            'user': str(request.user) if hasattr(request, 'user') and request.user.is_authenticated else 'Anonymous',
            'timestamp': timezone.now().isoformat(),
        }
        
        if extra_data:
            data.update(extra_data)
        
        api_logger.info(f"API请求: {json.dumps(data, ensure_ascii=False)}")
        return data
    
    @staticmethod
    def log_response(request: HttpRequest, response, duration: float, extra_data: Optional[Dict] = None):
        """记录API响应"""
        data = {
            'method': request.method,
            'path': request.path,
            'status_code': response.status_code,
            'duration_ms': round(duration * 1000, 2),
            'client_ip': get_client_ip(request),
            'user': str(request.user) if hasattr(request, 'user') and request.user.is_authenticated else 'Anonymous',
            'timestamp': timezone.now().isoformat(),
        }
        
        if extra_data:
            data.update(extra_data)
        
        level = logging.ERROR if response.status_code >= 400 else logging.INFO
        api_logger.log(level, f"API响应: {json.dumps(data, ensure_ascii=False)}")
        return data
    
    @staticmethod
    def log_error(request: HttpRequest, error: Exception, extra_data: Optional[Dict] = None):
        """记录API错误"""
        data = {
            'method': request.method,
            'path': request.path,
            'error_type': type(error).__name__,
            'error_message': str(error),
            'client_ip': get_client_ip(request),
            'user': str(request.user) if hasattr(request, 'user') and request.user.is_authenticated else 'Anonymous',
            'timestamp': timezone.now().isoformat(),
        }
        
        if extra_data:
            data.update(extra_data)
        
        api_logger.error(f"API错误: {json.dumps(data, ensure_ascii=False)}")
        return data

class PerformanceLogger:
    """性能日志记录器"""
    
    @staticmethod
    def log_operation(operation_name: str, duration: float, extra_data: Optional[Dict] = None):
        """记录操作性能"""
        data = {
            'operation': operation_name,
            'duration_ms': round(duration * 1000, 2),
            'timestamp': timezone.now().isoformat(),
        }
        
        if extra_data:
            data.update(extra_data)
        
        level = logging.WARNING if duration > 5.0 else logging.INFO
        performance_logger.log(level, f"性能监控: {json.dumps(data, ensure_ascii=False)}")
        return data
    
    @staticmethod
    def log_model_inference(model_type: str, text_length: int, duration: float, audio_duration: float):
        """记录模型推理性能"""
        rtf = duration / audio_duration if audio_duration > 0 else 0
        data = {
            'model_type': model_type,
            'text_length': text_length,
            'inference_duration_s': round(duration, 3),
            'audio_duration_s': round(audio_duration, 3),
            'rtf': round(rtf, 3),
            'timestamp': timezone.now().isoformat(),
        }
        
        level = logging.WARNING if rtf > 1.0 else logging.INFO
        performance_logger.log(level, f"模型推理性能: {json.dumps(data, ensure_ascii=False)}")
        return data

class SecurityLogger:
    """安全日志记录器"""
    
    @staticmethod
    def log_login_attempt(request: HttpRequest, email: str, success: bool, extra_data: Optional[Dict] = None):
        """记录登录尝试"""
        data = {
            'event': 'login_attempt',
            'email': email,
            'success': success,
            'client_ip': get_client_ip(request),
            'user_agent': request.META.get('HTTP_USER_AGENT', ''),
            'timestamp': timezone.now().isoformat(),
        }
        
        if extra_data:
            data.update(extra_data)
        
        level = logging.INFO if success else logging.WARNING
        security_logger.log(level, f"登录尝试: {json.dumps(data, ensure_ascii=False)}")
        return data
    
    @staticmethod
    def log_permission_denied(request: HttpRequest, resource: str, extra_data: Optional[Dict] = None):
        """记录权限拒绝"""
        data = {
            'event': 'permission_denied',
            'resource': resource,
            'user': str(request.user) if hasattr(request, 'user') and request.user.is_authenticated else 'Anonymous',
            'client_ip': get_client_ip(request),
            'timestamp': timezone.now().isoformat(),
        }
        
        if extra_data:
            data.update(extra_data)
        
        security_logger.warning(f"权限拒绝: {json.dumps(data, ensure_ascii=False)}")
        return data
    
    @staticmethod
    def log_rate_limit_exceeded(request: HttpRequest, limit_type: str, extra_data: Optional[Dict] = None):
        """记录速率限制超出"""
        data = {
            'event': 'rate_limit_exceeded',
            'limit_type': limit_type,
            'user': str(request.user) if hasattr(request, 'user') and request.user.is_authenticated else 'Anonymous',
            'client_ip': get_client_ip(request),
            'timestamp': timezone.now().isoformat(),
        }
        
        if extra_data:
            data.update(extra_data)
        
        security_logger.warning(f"速率限制超出: {json.dumps(data, ensure_ascii=False)}")
        return data

class TasksLogger:
    """任务日志记录器"""
    
    @staticmethod
    def log_task_created(task_id: str, task_type: str, user: str, extra_data: Optional[Dict] = None):
        """记录任务创建"""
        data = {
            'event': 'task_created',
            'task_id': task_id,
            'task_type': task_type,
            'user': user,
            'timestamp': timezone.now().isoformat(),
        }
        
        if extra_data:
            data.update(extra_data)
        
        tasks_logger.info(f"任务创建: {json.dumps(data, ensure_ascii=False)}")
        return data
    
    @staticmethod
    def log_task_status_change(task_id: str, old_status: str, new_status: str, extra_data: Optional[Dict] = None):
        """记录任务状态变更"""
        data = {
            'event': 'task_status_change',
            'task_id': task_id,
            'old_status': old_status,
            'new_status': new_status,
            'timestamp': timezone.now().isoformat(),
        }
        
        if extra_data:
            data.update(extra_data)
        
        tasks_logger.info(f"任务状态变更: {json.dumps(data, ensure_ascii=False)}")
        return data
    
    @staticmethod
    def log_task_completed(task_id: str, duration: float, extra_data: Optional[Dict] = None):
        """记录任务完成"""
        data = {
            'event': 'task_completed',
            'task_id': task_id,
            'duration_s': round(duration, 3),
            'timestamp': timezone.now().isoformat(),
        }
        
        if extra_data:
            data.update(extra_data)
        
        tasks_logger.info(f"任务完成: {json.dumps(data, ensure_ascii=False)}")
        return data

def get_client_ip(request: HttpRequest) -> str:
    """获取客户端IP地址"""
    x_forwarded_for = request.META.get('HTTP_X_FORWARDED_FOR')
    if x_forwarded_for:
        ip = x_forwarded_for.split(',')[0]
    else:
        ip = request.META.get('REMOTE_ADDR')
    return ip

def log_function_call(logger_name: str = 'app_logger'):
    """装饰器：记录函数调用"""
    def decorator(func):
        @wraps(func)
        def wrapper(*args, **kwargs):
            logger = logging.getLogger(logger_name)
            start_time = time.time()
            
            logger.debug(f"函数调用开始: {func.__module__}.{func.__name__}")
            
            try:
                result = func(*args, **kwargs)
                duration = time.time() - start_time
                logger.debug(f"函数调用成功: {func.__module__}.{func.__name__} (耗时: {duration:.3f}s)")
                return result
            except Exception as e:
                duration = time.time() - start_time
                logger.error(f"函数调用失败: {func.__module__}.{func.__name__} (耗时: {duration:.3f}s) - 错误: {e}")
                raise
        
        return wrapper
    return decorator

# 导出主要的日志记录器实例
__all__ = [
    'APILogger',
    'PerformanceLogger', 
    'SecurityLogger',
    'TasksLogger',
    'api_logger',
    'performance_logger',
    'security_logger',
    'tasks_logger',
    'app_logger',
    'log_function_call',
    'get_client_ip',
]
