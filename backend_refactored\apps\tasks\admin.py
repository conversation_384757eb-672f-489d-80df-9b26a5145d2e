"""
任务管理Admin配置
"""
from django.contrib import admin
from django.utils.html import format_html
from django.urls import reverse
from django.utils import timezone
from django.db.models import Count, Avg, Q
from .models import Task, TaskLog, TaskType, TaskStatus, TaskPriority


class TaskLogInline(admin.TabularInline):
    """任务日志内联编辑"""
    model = TaskLog
    extra = 0
    readonly_fields = ('level', 'message', 'extra_data', 'created_at')
    can_delete = False
    
    def has_add_permission(self, request, obj=None):
        return False


@admin.register(Task)
class TaskAdmin(admin.ModelAdmin):
    """任务管理"""
    inlines = [TaskLogInline]
    
    list_display = ('id_short', 'user_display', 'task_type_display', 'status_display', 
                   'priority_display', 'processing_time_display', 'audio_file_link', 
                   'retry_info', 'created_at')
    list_filter = ('task_type', 'status', 'priority', 'created_at', 'completed_at')
    search_fields = ('id', 'user__username', 'user__email', 'error_message', 'client_ip')
    readonly_fields = ('id', 'created_at', 'updated_at', 'started_at', 'completed_at',
                      'processing_time', 'queue_time', 'performance_summary', 'request_summary')
    
    fieldsets = (
        ('基本信息', {
            'fields': ('id', 'user', 'task_type', 'status', 'priority')
        }),
        ('请求数据', {
            'fields': ('request_summary', 'request_data'),
            'classes': ('collapse',)
        }),
        ('响应数据', {
            'fields': ('response_data', 'audio_file'),
            'classes': ('collapse',)
        }),
        ('时间信息', {
            'fields': ('created_at', 'started_at', 'completed_at', 'updated_at')
        }),
        ('性能指标', {
            'fields': ('performance_summary', 'processing_time', 'queue_time')
        }),
        ('错误处理', {
            'fields': ('error_message', 'retry_count', 'max_retries')
        }),
        ('客户端信息', {
            'fields': ('client_ip', 'user_agent'),
            'classes': ('collapse',)
        })
    )
    
    def id_short(self, obj):
        """短ID显示"""
        return str(obj.id)[:8] + '...'
    id_short.short_description = 'ID'
    
    def user_display(self, obj):
        """用户显示"""
        if obj.user:
            return format_html(
                '<a href="{}">{}</a>',
                reverse('admin:users_user_change', args=[obj.user.id]),
                f"{obj.user.username} ({obj.user.email})"
            )
        return "匿名用户"
    user_display.short_description = '用户'
    
    def task_type_display(self, obj):
        """任务类型显示"""
        colors = {
            TaskType.SFT: '#007bff',
            TaskType.ZERO_SHOT: '#28a745',
            TaskType.CROSS_LINGUAL: '#ffc107',
            TaskType.INSTRUCT: '#17a2b8',
            TaskType.INSTRUCT2: '#6f42c1'
        }
        color = colors.get(obj.task_type, '#6c757d')
        return format_html(
            '<span style="color: {}; font-weight: bold;">{}</span>',
            color, obj.get_task_type_display()
        )
    task_type_display.short_description = '任务类型'
    
    def status_display(self, obj):
        """状态显示"""
        colors = {
            TaskStatus.PENDING: '#6c757d',
            TaskStatus.PROCESSING: '#007bff',
            TaskStatus.COMPLETED: '#28a745',
            TaskStatus.FAILED: '#dc3545',
            TaskStatus.CANCELLED: '#ffc107'
        }
        color = colors.get(obj.status, '#6c757d')
        icon = {
            TaskStatus.PENDING: '⏳',
            TaskStatus.PROCESSING: '🔄',
            TaskStatus.COMPLETED: '✅',
            TaskStatus.FAILED: '❌',
            TaskStatus.CANCELLED: '⚠️'
        }.get(obj.status, '❓')
        
        return format_html(
            '<span style="color: {}; font-weight: bold;">{} {}</span>',
            color, icon, obj.get_status_display()
        )
    status_display.short_description = '状态'
    
    def priority_display(self, obj):
        """优先级显示"""
        colors = {
            TaskPriority.LOW: '#6c757d',
            TaskPriority.NORMAL: '#007bff',
            TaskPriority.HIGH: '#ffc107',
            TaskPriority.URGENT: '#dc3545'
        }
        color = colors.get(obj.priority, '#6c757d')
        return format_html(
            '<span style="color: {}; font-weight: bold;">{}</span>',
            color, obj.get_priority_display()
        )
    priority_display.short_description = '优先级'
    
    def processing_time_display(self, obj):
        """处理时间显示"""
        if obj.processing_time:
            return f"{obj.processing_time:.2f}秒"
        elif obj.status == TaskStatus.PROCESSING and obj.started_at:
            current_time = (timezone.now() - obj.started_at).total_seconds()
            return format_html('<span style="color: #007bff;">进行中 {:.1f}秒</span>', current_time)
        return "-"
    processing_time_display.short_description = '处理时间'
    
    def audio_file_link(self, obj):
        """音频文件链接"""
        if obj.audio_file:
            return format_html(
                '<a href="{}" target="_blank">{}</a>',
                reverse('admin:files_audiofile_change', args=[obj.audio_file.id]),
                obj.audio_file.filename
            )
        return "-"
    audio_file_link.short_description = '音频文件'
    
    def retry_info(self, obj):
        """重试信息"""
        if obj.retry_count > 0:
            return format_html(
                '<span style="color: #ffc107;">{}/{}</span>',
                obj.retry_count, obj.max_retries
            )
        return f"0/{obj.max_retries}"
    retry_info.short_description = '重试次数'
    
    def performance_summary(self, obj):
        """性能摘要"""
        summary = []
        if obj.queue_time:
            summary.append(f"队列等待: {obj.queue_time:.2f}秒")
        if obj.processing_time:
            summary.append(f"处理时间: {obj.processing_time:.2f}秒")
        if obj.started_at and obj.completed_at:
            total_time = (obj.completed_at - obj.created_at).total_seconds()
            summary.append(f"总耗时: {total_time:.2f}秒")
        return "<br>".join(summary) if summary else "-"
    performance_summary.short_description = '性能摘要'
    performance_summary.allow_tags = True
    
    def request_summary(self, obj):
        """请求摘要"""
        if not obj.request_data:
            return "-"
        
        summary = []
        data = obj.request_data
        
        if 'tts_text' in data:
            text = data['tts_text'][:50] + '...' if len(data['tts_text']) > 50 else data['tts_text']
            summary.append(f"文本: {text}")
        
        if 'spk_id' in data:
            summary.append(f"说话人: {data['spk_id']}")
        
        if 'speed' in data:
            summary.append(f"语速: {data['speed']}")
            
        return "<br>".join(summary) if summary else "-"
    request_summary.short_description = '请求摘要'
    request_summary.allow_tags = True
    
    actions = ['retry_failed_tasks', 'cancel_pending_tasks', 'mark_as_completed', 'delete_old_tasks']
    
    def retry_failed_tasks(self, request, queryset):
        """重试失败任务"""
        failed_tasks = queryset.filter(status=TaskStatus.FAILED)
        retried_count = 0
        
        for task in failed_tasks:
            if task.can_retry:
                task.increment_retry()
                retried_count += 1
        
        self.message_user(request, f'成功重试 {retried_count} 个失败任务')
    retry_failed_tasks.short_description = '重试失败任务'
    
    def cancel_pending_tasks(self, request, queryset):
        """取消等待任务"""
        updated = queryset.filter(status=TaskStatus.PENDING).update(status=TaskStatus.CANCELLED)
        self.message_user(request, f'成功取消 {updated} 个等待任务')
    cancel_pending_tasks.short_description = '取消等待任务'
    
    def mark_as_completed(self, request, queryset):
        """标记为已完成"""
        updated = queryset.filter(
            status__in=[TaskStatus.PENDING, TaskStatus.PROCESSING]
        ).update(
            status=TaskStatus.COMPLETED,
            completed_at=timezone.now()
        )
        self.message_user(request, f'成功标记 {updated} 个任务为已完成')
    mark_as_completed.short_description = '标记为已完成'
    
    def delete_old_tasks(self, request, queryset):
        """删除旧任务"""
        from datetime import timedelta
        cutoff_date = timezone.now() - timedelta(days=30)
        old_tasks = queryset.filter(
            created_at__lt=cutoff_date,
            status__in=[TaskStatus.COMPLETED, TaskStatus.FAILED, TaskStatus.CANCELLED]
        )
        deleted_count = old_tasks.count()
        old_tasks.delete()
        self.message_user(request, f'成功删除 {deleted_count} 个30天前的旧任务')
    delete_old_tasks.short_description = '删除30天前的旧任务'


@admin.register(TaskLog)
class TaskLogAdmin(admin.ModelAdmin):
    """任务日志管理"""
    list_display = ('task_short', 'level_display', 'message_short', 'created_at')
    list_filter = ('level', 'created_at')
    search_fields = ('task__id', 'message')
    readonly_fields = ('id', 'task', 'level', 'message', 'extra_data', 'created_at', 'updated_at')
    
    def task_short(self, obj):
        """任务短ID"""
        return format_html(
            '<a href="{}">{}</a>',
            reverse('admin:tasks_task_change', args=[obj.task.id]),
            str(obj.task.id)[:8] + '...'
        )
    task_short.short_description = '任务'
    
    def level_display(self, obj):
        """日志级别显示"""
        colors = {
            'DEBUG': '#6c757d',
            'INFO': '#007bff',
            'WARNING': '#ffc107',
            'ERROR': '#dc3545'
        }
        color = colors.get(obj.level, '#6c757d')
        return format_html(
            '<span style="color: {}; font-weight: bold;">{}</span>',
            color, obj.level
        )
    level_display.short_description = '级别'
    
    def message_short(self, obj):
        """消息简短显示"""
        return obj.message[:100] + '...' if len(obj.message) > 100 else obj.message
    message_short.short_description = '消息'
    
    def has_add_permission(self, request):
        return False
    
    def has_change_permission(self, request, obj=None):
        return False
