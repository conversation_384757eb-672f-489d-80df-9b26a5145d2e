"""
文件管理服务
"""
import os
import uuid
import hashlib
import mimetypes
from datetime import datetime, timedelta
from django.conf import settings
from django.core.files.storage import default_storage
from django.utils import timezone
from .models import AudioFile, FileType, StorageType, FileOperation
from loguru import logger


class FileService:
    """文件管理服务"""
    
    def save_uploaded_file(self, file, user=None, file_type=FileType.AUDIO_INPUT, **kwargs):
        """保存上传的文件"""
        try:
            # 生成唯一文件名
            file_extension = os.path.splitext(file.name)[1]
            unique_filename = f"{uuid.uuid4().hex}{file_extension}"
            
            # 根据文件类型确定存储路径
            if file_type == FileType.AUDIO_INPUT:
                storage_path = f"uploads/{unique_filename}"
            elif file_type == FileType.REFERENCE:
                storage_path = f"reference/{unique_filename}"
            else:
                storage_path = f"temp/{unique_filename}"
            
            # 保存文件
            file_path = default_storage.save(storage_path, file)
            full_path = os.path.join(settings.MEDIA_ROOT, file_path)
            
            # 获取文件信息
            file_size = file.size
            mime_type = file.content_type or mimetypes.guess_type(file.name)[0]
            
            # 获取音频信息
            duration, sample_rate, channels = self.get_audio_info(full_path)
            
            # 创建数据库记录
            audio_file = AudioFile.objects.create(
                user=user,
                filename=unique_filename,
                original_filename=file.name,
                file_type=file_type,
                storage_type=StorageType.LOCAL,
                file_path=file_path,
                file_size=file_size,
                mime_type=mime_type,
                duration=duration,
                sample_rate=sample_rate,
                channels=channels,
                **kwargs
            )
            
            # 记录操作
            if user:
                FileOperation.objects.create(
                    user=user,
                    audio_file=audio_file,
                    operation='upload',
                    extra_data={'original_filename': file.name}
                )
            
            logger.info(f"文件上传成功: {unique_filename}")
            return audio_file
            
        except Exception as e:
            logger.error(f"文件上传失败: {e}")
            raise
    
    def get_audio_info(self, file_path):
        """获取音频文件信息"""
        try:
            import wave
            import librosa
            
            # 尝试使用wave库读取基本信息
            try:
                with wave.open(file_path, 'rb') as wav_file:
                    duration = wav_file.getnframes() / wav_file.getframerate()
                    sample_rate = wav_file.getframerate()
                    channels = wav_file.getnchannels()
                    return duration, sample_rate, channels
            except:
                pass
            
            # 使用librosa读取音频信息
            try:
                y, sr = librosa.load(file_path, sr=None)
                duration = len(y) / sr
                sample_rate = sr
                channels = 1 if len(y.shape) == 1 else y.shape[0]
                return duration, sample_rate, channels
            except:
                pass
            
            # 如果都失败，返回默认值
            return None, None, 1
            
        except Exception as e:
            logger.warning(f"获取音频信息失败: {file_path}, 错误: {e}")
            return None, None, 1
    
    def delete_file(self, audio_file):
        """删除文件"""
        try:
            # 删除物理文件
            if audio_file.storage_type == StorageType.LOCAL:
                file_path = os.path.join(settings.MEDIA_ROOT, audio_file.file_path)
                if os.path.exists(file_path):
                    os.remove(file_path)
            elif audio_file.storage_type == StorageType.OSS:
                oss_service = OSSService()
                oss_service.delete_file(audio_file.file_path)
            
            # 软删除数据库记录
            audio_file.soft_delete()
            
            logger.info(f"文件删除成功: {audio_file.filename}")
            return True
            
        except Exception as e:
            logger.error(f"文件删除失败: {audio_file.filename}, 错误: {e}")
            return False
    
    def move_to_oss(self, audio_file):
        """将文件迁移到OSS"""
        try:
            if audio_file.storage_type != StorageType.LOCAL:
                return False
            
            oss_service = OSSService()
            
            # 读取本地文件
            local_path = os.path.join(settings.MEDIA_ROOT, audio_file.file_path)
            if not os.path.exists(local_path):
                logger.error(f"本地文件不存在: {local_path}")
                return False
            
            # 上传到OSS
            oss_path = f"audio/{audio_file.filename}"
            success = oss_service.upload_file(local_path, oss_path)
            
            if success:
                # 更新数据库记录
                audio_file.storage_type = StorageType.OSS
                audio_file.file_path = oss_path
                audio_file.file_url = oss_service.get_file_url(oss_path)
                audio_file.save(update_fields=['storage_type', 'file_path', 'file_url'])
                
                # 删除本地文件
                os.remove(local_path)
                
                logger.info(f"文件迁移到OSS成功: {audio_file.filename}")
                return True
            
            return False
            
        except Exception as e:
            logger.error(f"文件迁移到OSS失败: {audio_file.filename}, 错误: {e}")
            return False
    
    def cleanup_expired_files(self):
        """清理过期文件"""
        expired_files = AudioFile.objects.filter(
            expires_at__lt=timezone.now(),
            auto_delete=True,
            is_active=True
        )
        
        deleted_count = 0
        for audio_file in expired_files:
            if self.delete_file(audio_file):
                deleted_count += 1
        
        logger.info(f"清理过期文件完成，共删除 {deleted_count} 个文件")
        return deleted_count
    
    def cleanup_orphaned_files(self):
        """清理孤儿文件（数据库中不存在的物理文件）"""
        media_root = settings.MEDIA_ROOT
        deleted_count = 0
        
        # 扫描各个目录
        for directory in ['uploads', 'generated_audio', 'temp']:
            dir_path = os.path.join(media_root, directory)
            if not os.path.exists(dir_path):
                continue
            
            for filename in os.listdir(dir_path):
                file_path = os.path.join(dir_path, filename)
                if not os.path.isfile(file_path):
                    continue
                
                # 检查数据库中是否存在
                if not AudioFile.objects.filter(filename=filename, is_active=True).exists():
                    try:
                        os.remove(file_path)
                        deleted_count += 1
                        logger.info(f"删除孤儿文件: {filename}")
                    except Exception as e:
                        logger.error(f"删除孤儿文件失败: {filename}, 错误: {e}")
        
        logger.info(f"清理孤儿文件完成，共删除 {deleted_count} 个文件")
        return deleted_count
    
    def get_file_stats(self, user=None):
        """获取文件统计信息"""
        from django.db.models import Count, Sum
        
        queryset = AudioFile.objects.filter(is_active=True)
        if user:
            queryset = queryset.filter(user=user)
        
        # 基础统计
        total_files = queryset.count()
        total_size = queryset.aggregate(total=Sum('file_size'))['total'] or 0
        
        # 按类型统计
        by_type = dict(queryset.values('file_type').annotate(count=Count('id')))
        
        # 按存储类型统计
        by_storage = dict(queryset.values('storage_type').annotate(count=Count('id')))
        
        # 最近上传
        recent_uploads = queryset.filter(
            created_at__gte=timezone.now() - timedelta(days=7)
        ).count()
        
        # 过期文件
        expired_files = queryset.filter(
            expires_at__lt=timezone.now(),
            auto_delete=True
        ).count()
        
        return {
            'total_files': total_files,
            'total_size': total_size,
            'total_size_human': self.format_file_size(total_size),
            'by_type': by_type,
            'by_storage': by_storage,
            'recent_uploads': recent_uploads,
            'expired_files': expired_files,
        }
    
    def format_file_size(self, size_bytes):
        """格式化文件大小"""
        if size_bytes == 0:
            return "0 B"
        
        size_names = ["B", "KB", "MB", "GB", "TB"]
        i = 0
        while size_bytes >= 1024 and i < len(size_names) - 1:
            size_bytes /= 1024.0
            i += 1
        
        return f"{size_bytes:.1f} {size_names[i]}"


class OSSService:
    """阿里云OSS服务"""
    
    def __init__(self):
        self.access_key_id = settings.OSS_ACCESS_KEY_ID
        self.access_key_secret = settings.OSS_ACCESS_KEY_SECRET
        self.endpoint = settings.OSS_ENDPOINT
        self.bucket_name = settings.OSS_BUCKET_NAME
        self.cdn_domain = settings.OSS_CDN_DOMAIN
        
        if not all([self.access_key_id, self.access_key_secret, self.endpoint, self.bucket_name]):
            logger.warning("OSS配置不完整，OSS功能将不可用")
            self.enabled = False
        else:
            self.enabled = True
    
    def get_bucket(self):
        """获取OSS bucket"""
        if not self.enabled:
            return None
        
        try:
            import oss2
            auth = oss2.Auth(self.access_key_id, self.access_key_secret)
            bucket = oss2.Bucket(auth, self.endpoint, self.bucket_name)
            return bucket
        except ImportError:
            logger.error("oss2库未安装，请运行: pip install oss2")
            return None
        except Exception as e:
            logger.error(f"连接OSS失败: {e}")
            return None
    
    def upload_file(self, local_path, oss_path):
        """上传文件到OSS"""
        bucket = self.get_bucket()
        if not bucket:
            return False
        
        try:
            bucket.put_object_from_file(oss_path, local_path)
            logger.info(f"文件上传到OSS成功: {oss_path}")
            return True
        except Exception as e:
            logger.error(f"文件上传到OSS失败: {oss_path}, 错误: {e}")
            return False
    
    def delete_file(self, oss_path):
        """从OSS删除文件"""
        bucket = self.get_bucket()
        if not bucket:
            return False
        
        try:
            bucket.delete_object(oss_path)
            logger.info(f"从OSS删除文件成功: {oss_path}")
            return True
        except Exception as e:
            logger.error(f"从OSS删除文件失败: {oss_path}, 错误: {e}")
            return False
    
    def get_file_url(self, oss_path):
        """获取文件URL"""
        if self.cdn_domain:
            return f"https://{self.cdn_domain}/{oss_path}"
        else:
            return f"https://{self.bucket_name}.{self.endpoint}/{oss_path}"
    
    def get_download_url(self, oss_path, expires=3600):
        """获取临时下载URL"""
        bucket = self.get_bucket()
        if not bucket:
            return None
        
        try:
            url = bucket.sign_url('GET', oss_path, expires)
            return url
        except Exception as e:
            logger.error(f"生成OSS下载URL失败: {oss_path}, 错误: {e}")
            return None
