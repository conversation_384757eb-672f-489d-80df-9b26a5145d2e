# CosyVoice API 路径返回功能使用指南

## 🎯 功能概述

修改后的CosyVoice API现在返回生成音频的文件路径，而不是直接返回音频流。这样可以：

- ✅ 获取生成音频的完整文件路径
- ✅ 管理和查看所有生成的音频文件
- ✅ 支持文件下载和删除
- ✅ 获取详细的音频文件信息

## 📁 输出目录

所有生成的音频文件都保存在 `generated_audio/` 目录中，文件名格式：
- SFT推理: `sft_YYYYMMDD_HHMMSS_mmm.wav`
- 零样本推理: `zero_shot_YYYYMMDD_HHMMSS_mmm.wav`
- 跨语种推理: `cross_lingual_YYYYMMDD_HHMMSS_mmm.wav`
- 自然语言控制: `instruct_YYYYMMDD_HHMMSS_mmm.wav`
- 高级自然语言控制: `instruct2_YYYYMMDD_HHMMSS_mmm.wav`

## 🔧 API接口变更

### 1. SFT推理接口 `/inference_sft`

**请求**:
```python
import requests

data = {
    'tts_text': '你好，这是测试文本。',
    'spk_id': '中文女'
}

response = requests.post('http://127.0.0.1:8000/inference_sft', data=data)
```

**响应**:
```json
{
    "success": true,
    "message": "SFT推理成功",
    "audio_info": {
        "file_path": "D:\\Xhchen\\Ai\\cv\\CosyVoice\\generated_audio\\sft_20250723_143022_123.wav",
        "filename": "sft_20250723_143022_123.wav",
        "file_size": 98348,
        "duration": 2.23,
        "sample_rate": 22050,
        "channels": 1,
        "relative_path": "generated_audio/sft_20250723_143022_123.wav"
    },
    "text": "你好，这是测试文本。",
    "speaker": "中文女"
}
```

### 2. 零样本推理接口 `/inference_zero_shot`

**请求**:
```python
data = {
    'tts_text': '这是零样本推理测试。',
    'prompt_text': '参考音频对应的文本'
}

with open('reference_audio.wav', 'rb') as f:
    files = {'prompt_wav': f}
    response = requests.post('http://127.0.0.1:8000/inference_zero_shot', 
                           data=data, files=files)
```

**响应**:
```json
{
    "success": true,
    "message": "零样本推理成功",
    "audio_info": {
        "file_path": "D:\\Xhchen\\Ai\\cv\\CosyVoice\\generated_audio\\zero_shot_20250723_143025_456.wav",
        "filename": "zero_shot_20250723_143025_456.wav",
        "file_size": 111660,
        "duration": 2.53,
        "sample_rate": 22050,
        "channels": 1,
        "relative_path": "generated_audio/zero_shot_20250723_143025_456.wav"
    },
    "text": "这是零样本推理测试。",
    "prompt_text": "参考音频对应的文本",
    "reference_audio": "reference_audio.wav"
}
```

### 3. 自然语言控制推理接口 `/inference_instruct`

**请求**:
```python
data = {
    'tts_text': '今天天气很好。',
    'spk_id': '中文女',
    'instruct_text': '用温柔的语气说'
}

response = requests.post('http://127.0.0.1:8000/inference_instruct', data=data)
```

**响应**:
```json
{
    "success": true,
    "message": "自然语言控制推理成功",
    "audio_info": {
        "file_path": "D:\\Xhchen\\Ai\\cv\\CosyVoice\\generated_audio\\instruct_20250723_143028_789.wav",
        "filename": "instruct_20250723_143028_789.wav",
        "file_size": 105420,
        "duration": 2.39,
        "sample_rate": 22050,
        "channels": 1,
        "relative_path": "generated_audio/instruct_20250723_143028_789.wav"
    },
    "text": "今天天气很好。",
    "speaker": "中文女",
    "instruct_text": "用温柔的语气说"
}
```

**回退模式响应**（当模型不支持instruct时）:
```json
{
    "success": true,
    "message": "自然语言控制推理成功",
    "audio_info": { ... },
    "text": "今天天气很好。",
    "speaker": "中文女",
    "instruct_text": "用温柔的语气说",
    "fallback_mode": "SFT",
    "warning": "模型不支持instruct模式，使用SFT模式作为回退"
}
```

## 🗂️ 文件管理接口

### 1. 获取生成的音频文件列表 `/list_generated_audio`

```python
response = requests.get('http://127.0.0.1:8000/list_generated_audio')
```

**响应**:
```json
{
    "audio_files": [
        {
            "filename": "sft_20250723_143022_123.wav",
            "file_size": 98348,
            "duration": 2.23,
            "sample_rate": 22050,
            "channels": 1,
            "modified_time": "2025-07-23 14:30:22",
            "download_url": "/download/sft_20250723_143022_123.wav",
            "file_path": "D:\\Xhchen\\Ai\\cv\\CosyVoice\\generated_audio\\sft_20250723_143022_123.wav"
        }
    ],
    "total_count": 1,
    "output_directory": "D:\\Xhchen\\Ai\\cv\\CosyVoice\\generated_audio"
}
```

### 2. 下载音频文件 `/download/{filename}`

```python
response = requests.get('http://127.0.0.1:8000/download/sft_20250723_143022_123.wav')

if response.status_code == 200:
    with open('downloaded_audio.wav', 'wb') as f:
        f.write(response.content)
```

### 3. 删除音频文件 `/delete_audio/{filename}`

```python
response = requests.delete('http://127.0.0.1:8000/delete_audio/sft_20250723_143022_123.wav')
```

**响应**:
```json
{
    "success": true,
    "message": "文件 sft_20250723_143022_123.wav 已删除",
    "deleted_file": "sft_20250723_143022_123.wav"
}
```

### 4. 清空所有生成的音频文件 `/clear_generated_audio`

```python
response = requests.post('http://127.0.0.1:8000/clear_generated_audio')
```

**响应**:
```json
{
    "success": true,
    "message": "已清空 5 个音频文件",
    "deleted_count": 5
}
```

## 💡 使用示例

### 完整的音频生成和管理流程

```python
import requests
import os

# 1. 生成音频
def generate_audio(text, speaker="中文女"):
    data = {'tts_text': text, 'spk_id': speaker}
    response = requests.post('http://127.0.0.1:8000/inference_sft', data=data)
    
    if response.status_code == 200:
        result = response.json()
        return result['audio_info']['file_path']
    return None

# 2. 获取文件列表
def get_audio_files():
    response = requests.get('http://127.0.0.1:8000/list_generated_audio')
    if response.status_code == 200:
        return response.json()['audio_files']
    return []

# 3. 下载文件
def download_audio(filename, save_path):
    response = requests.get(f'http://127.0.0.1:8000/download/{filename}')
    if response.status_code == 200:
        with open(save_path, 'wb') as f:
            f.write(response.content)
        return True
    return False

# 使用示例
if __name__ == '__main__':
    # 生成音频
    audio_path = generate_audio("你好，世界！")
    if audio_path:
        print(f"音频已生成: {audio_path}")
        
        # 获取文件名
        filename = os.path.basename(audio_path)
        
        # 下载到指定位置
        if download_audio(filename, f"my_audio_{filename}"):
            print("音频下载成功")
        
        # 查看所有生成的文件
        files = get_audio_files()
        print(f"总共生成了 {len(files)} 个音频文件")
```

## 🎯 优势

1. **文件路径返回**: 直接获取生成音频的完整路径
2. **文件管理**: 可以查看、下载、删除生成的音频文件
3. **详细信息**: 获取音频文件的大小、时长、采样率等信息
4. **批量操作**: 支持批量查看和清空音频文件
5. **向后兼容**: 保持原有的API接口结构，只是返回格式改变

## 🚀 启动服务器

```bash
python api_server.py --port 8000 --model_dir pretrained_models/CosyVoice-300M-Instruct
```

## 📖 API文档

启动服务器后，可以访问：
- **Swagger UI**: http://127.0.0.1:8000/docs
- **ReDoc**: http://127.0.0.1:8000/redoc

---

**现在您的CosyVoice API已经支持返回音频文件路径，方便进行文件管理和后续处理！**
