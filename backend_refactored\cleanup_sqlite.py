#!/usr/bin/env python
"""
SQLite文件清理脚本
在Django启动时自动删除可能生成的SQLite文件
"""
import os
import glob
import sys
from pathlib import Path

def cleanup_sqlite_files():
    """清理SQLite文件"""
    base_dir = Path(__file__).resolve().parent
    
    # 要清理的SQLite文件模式
    sqlite_patterns = [
        'db.sqlite3',
        '*.sqlite3',
        '*.db'
    ]
    
    deleted_files = []
    
    for pattern in sqlite_patterns:
        files = glob.glob(str(base_dir / pattern))
        for file_path in files:
            try:
                if os.path.exists(file_path):
                    os.remove(file_path)
                    deleted_files.append(file_path)
                    print(f"已删除SQLite文件: {file_path}")
            except Exception as e:
                print(f"删除SQLite文件失败: {file_path}, 错误: {e}")
    
    if deleted_files:
        print(f"总共删除了 {len(deleted_files)} 个SQLite文件")
    else:
        print("没有找到需要删除的SQLite文件")
    
    return len(deleted_files)

if __name__ == "__main__":
    cleanup_sqlite_files()
