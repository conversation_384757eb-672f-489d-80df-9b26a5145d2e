# CosyVoice界面实施代码示例

**文档版本**: v1.0  
**创建日期**: 2025-01-23  
**说明**: 提供具体的代码实现示例，指导前端开发

---

## 1. 现有Gradio界面增强示例

### 1.1 增强版webui.py
```python
import gradio as gr
import os
import sys
import argparse
import numpy as np
import torch
import torchaudio
import random
import librosa

# 现有导入保持不变...
ROOT_DIR = os.path.dirname(os.path.abspath(__file__))
sys.path.append('{}/third_party/Matcha-TTS'.format(ROOT_DIR))
from cosyvoice.cli.cosyvoice import CosyVoice, CosyVoice2
from cosyvoice.utils.file_utils import load_wav, logging
from cosyvoice.utils.common import set_all_random_seed

# 保持现有配置
inference_mode_list = ['预训练音色', '3s极速复刻', '跨语种复刻', '自然语言控制']
instruct_dict = {
    '预训练音色': '''
    📝 **操作步骤**:
    1. 选择预训练音色
    2. 点击生成音频按钮
    
    💡 **提示**: 登录后可查看历史记录和使用统计
    ''',
    '3s极速复刻': '''
    📝 **操作步骤**:
    1. 选择prompt音频文件，或录入prompt音频（不超过30s）
    2. 输入prompt文本（与音频内容一致）
    3. 点击生成音频按钮
    
    💡 **提示**: 音频质量越好，克隆效果越佳
    ''',
    '跨语种复刻': '''
    📝 **操作步骤**:
    1. 选择prompt音频文件，或录入prompt音频（不超过30s）
    2. 点击生成音频按钮
    
    💡 **提示**: 确保合成文本和prompt文本为不同语言
    ''',
    '自然语言控制': '''
    📝 **操作步骤**:
    1. 选择预训练音色
    2. 输入instruct文本（如"用温柔的语气说"）
    3. 点击生成音频按钮
    
    💡 **提示**: 需要使用CosyVoice-300M-Instruct模型
    '''
}

# 新增用户登录功能
def user_login(username, password):
    """用户登录功能"""
    if username and password:
        # 这里应该调用实际的认证API
        return f"欢迎回来，{username}！", True
    return "请输入用户名和密码", False

def get_user_history():
    """获取用户历史记录"""
    # 这里应该调用实际的API获取历史记录
    return [
        ["2025-01-23 10:30", "我是通义实验室语音团队...", "预训练音色", "中文女", "播放"],
        ["2025-01-23 10:25", "Hello, this is a test...", "3s极速复刻", "自定义", "下载"],
        ["2025-01-23 10:20", "こんにちは", "跨语种复刻", "自定义", "播放"]
    ]

def enhanced_main():
    """增强版主界面"""
    with gr.Blocks(
        title="CosyVoice TTS 生成器",
        theme=gr.themes.Soft(),
        css="""
        .upgrade-banner {
            background: linear-gradient(90deg, #e6f7ff 0%, #f0f9ff 100%);
            border: 1px solid #91d5ff;
            border-radius: 8px;
            padding: 16px;
            margin-bottom: 20px;
        }
        .feature-highlight {
            background: #f6ffed;
            border-left: 4px solid #52c41a;
            padding: 12px;
            margin: 8px 0;
        }
        .audio-enhanced {
            border: 2px solid #722ed1;
            border-radius: 8px;
            padding: 16px;
        }
        """
    ) as demo:
        
        # 升级提示横幅
        gr.HTML("""
        <div class="upgrade-banner">
            <div style="display: flex; align-items: center; justify-content: space-between;">
                <div>
                    <strong>🎉 新功能上线！</strong>
                    <span>体验全新的管理界面，支持任务历史、文件管理等功能</span>
                </div>
                <div>
                    <a href="/admin" target="_blank" style="
                        background: #1890ff; 
                        color: white; 
                        padding: 8px 16px; 
                        border-radius: 6px; 
                        text-decoration: none;
                        margin-left: 12px;
                    ">立即体验</a>
                </div>
            </div>
        </div>
        """)
        
        gr.Markdown("### 代码库 [CosyVoice](https://github.com/FunAudioLLM/CosyVoice)")
        gr.Markdown("#### 请输入需要合成的文本，选择推理模式，并按照提示步骤进行操作")

        # 可选用户登录区域
        with gr.Accordion("用户登录 (可选，登录后可查看历史记录)", open=False):
            with gr.Row():
                username = gr.Textbox(label="用户名", placeholder="可选，登录后可查看历史记录")
                password = gr.Textbox(label="密码", type="password")
                login_btn = gr.Button("登录", variant="primary")
                login_status = gr.Textbox(label="登录状态", interactive=False)
                
        # 主要TTS生成区域
        tts_text = gr.Textbox(
            label="输入合成文本", 
            lines=2, 
            value="我是通义实验室语音团队全新推出的生成式语音大模型，提供舒适自然的语音合成能力。",
            placeholder="请输入要合成的文本..."
        )
        
        with gr.Row():
            mode_checkbox_group = gr.Radio(
                choices=inference_mode_list, 
                label='选择推理模式', 
                value=inference_mode_list[0]
            )
            instruction_text = gr.HTML(
                value=f'<div class="feature-highlight">{instruct_dict[inference_mode_list[0]]}</div>'
            )
            
        with gr.Row():
            sft_dropdown = gr.Dropdown(choices=sft_spk, label='选择预训练音色', value=sft_spk[0])
            stream = gr.Radio(choices=[('否', False), ('是', True)], label='是否流式推理', value=False)
            speed = gr.Number(value=1, label="语速调节", minimum=0.5, maximum=2.0, step=0.1)
            
        with gr.Row():
            with gr.Column(scale=0.8):
                seed = gr.Number(value=0, label="随机推理种子")
            with gr.Column(scale=0.2):
                seed_button = gr.Button("🎲 随机", size="sm")

        with gr.Row():
            prompt_wav_upload = gr.Audio(
                sources='upload', 
                type='filepath', 
                label='选择prompt音频文件，注意采样率不低于16khz'
            )
            prompt_wav_record = gr.Audio(
                sources='microphone', 
                type='filepath', 
                label='录制prompt音频文件'
            )
            
        prompt_text = gr.Textbox(
            label="输入prompt文本", 
            lines=1, 
            placeholder="请输入prompt文本，需与prompt音频内容一致...", 
            value=''
        )
        instruct_text = gr.Textbox(
            label="输入instruct文本", 
            lines=1, 
            placeholder="请输入instruct文本，如'用温柔的语气说'", 
            value=''
        )

        generate_button = gr.Button("🎵 生成音频", variant="primary", size="lg")

        # 增强的音频输出区域
        with gr.Tabs():
            with gr.Tab("音频输出"):
                audio_output = gr.Audio(
                    label="合成音频", 
                    autoplay=True, 
                    streaming=True,
                    elem_classes=["audio-enhanced"]
                )
                
            with gr.Tab("历史记录"):
                history_list = gr.Dataframe(
                    headers=["时间", "文本", "模式", "说话人", "操作"],
                    label="最近生成的音频",
                    value=get_user_history(),
                    interactive=False
                )
                refresh_history = gr.Button("🔄 刷新历史")
                
            with gr.Tab("使用统计"):
                gr.HTML("""
                <div style="display: grid; grid-template-columns: repeat(3, 1fr); gap: 16px; margin: 16px 0;">
                    <div style="background: #f0f9ff; padding: 16px; border-radius: 8px; text-align: center;">
                        <h3 style="margin: 0; color: #1890ff;">今日生成</h3>
                        <div style="font-size: 24px; font-weight: bold; color: #1890ff;">23</div>
                    </div>
                    <div style="background: #f6ffed; padding: 16px; border-radius: 8px; text-align: center;">
                        <h3 style="margin: 0; color: #52c41a;">配额使用</h3>
                        <div style="font-size: 24px; font-weight: bold; color: #52c41a;">756/1000</div>
                    </div>
                    <div style="background: #fff2e8; padding: 16px; border-radius: 8px; text-align: center;">
                        <h3 style="margin: 0; color: #fa8c16;">成功率</h3>
                        <div style="font-size: 24px; font-weight: bold; color: #fa8c16;">98.5%</div>
                    </div>
                </div>
                """)

        # 事件绑定
        def change_instruction(mode):
            return f'<div class="feature-highlight">{instruct_dict[mode]}</div>'
            
        def generate_seed():
            return random.randint(1, 100000000)

        # 绑定事件
        login_btn.click(user_login, inputs=[username, password], outputs=[login_status])
        seed_button.click(generate_seed, outputs=[seed])
        mode_checkbox_group.change(
            fn=change_instruction, 
            inputs=[mode_checkbox_group], 
            outputs=[instruction_text]
        )
        generate_button.click(
            generate_audio,
            inputs=[
                tts_text, mode_checkbox_group, sft_dropdown, prompt_text, 
                prompt_wav_upload, prompt_wav_record, instruct_text,
                seed, stream, speed
            ],
            outputs=[audio_output]
        )
        refresh_history.click(get_user_history, outputs=[history_list])

    demo.queue(max_size=4, default_concurrency_limit=2)
    return demo

# 保持原有的generate_audio函数不变
def generate_audio(tts_text, mode_checkbox_group, sft_dropdown, prompt_text, 
                  prompt_wav_upload, prompt_wav_record, instruct_text,
                  seed, stream, speed):
    # 原有逻辑保持不变...
    pass

if __name__ == '__main__':
    parser = argparse.ArgumentParser()
    parser.add_argument('--port', type=int, default=8000)
    parser.add_argument('--model_dir', type=str, default='pretrained_models/CosyVoice2-0.5B')
    args = parser.parse_args()
    
    # 初始化模型（保持不变）
    try:
        cosyvoice = CosyVoice(args.model_dir)
    except Exception:
        try:
            cosyvoice = CosyVoice2(args.model_dir)
        except Exception:
            raise TypeError('no valid model_type!')

    sft_spk = cosyvoice.list_available_spks()
    if len(sft_spk) == 0:
        sft_spk = ['']
    prompt_sr = 16000
    default_data = np.zeros(cosyvoice.sample_rate)
    
    # 启动增强版界面
    demo = enhanced_main()
    demo.launch(server_name='0.0.0.0', server_port=args.port)
```

---

## 2. 新版管理后台界面示例

### 2.1 React组件结构
```jsx
// src/components/Layout/AdminLayout.jsx
import React, { useState } from 'react';
import { Layout, Menu, Avatar, Dropdown, Button } from 'antd';
import {
  DashboardOutlined,
  AudioOutlined,
  UnorderedListOutlined,
  FolderOutlined,
  UserOutlined,
  SettingOutlined,
  BarChartOutlined,
  MenuFoldOutlined,
  MenuUnfoldOutlined
} from '@ant-design/icons';

const { Header, Sider, Content } = Layout;

const AdminLayout = ({ children }) => {
  const [collapsed, setCollapsed] = useState(false);

  const menuItems = [
    {
      key: 'dashboard',
      icon: <DashboardOutlined />,
      label: '仪表板',
      path: '/admin/dashboard'
    },
    {
      key: 'tts',
      icon: <AudioOutlined />,
      label: 'TTS生成',
      path: '/admin/tts'
    },
    {
      key: 'tasks',
      icon: <UnorderedListOutlined />,
      label: '任务管理',
      path: '/admin/tasks'
    },
    {
      key: 'files',
      icon: <FolderOutlined />,
      label: '文件管理',
      path: '/admin/files'
    },
    {
      key: 'users',
      icon: <UserOutlined />,
      label: '用户管理',
      path: '/admin/users'
    },
    {
      key: 'settings',
      icon: <SettingOutlined />,
      label: '系统设置',
      path: '/admin/settings'
    },
    {
      key: 'analytics',
      icon: <BarChartOutlined />,
      label: '统计分析',
      path: '/admin/analytics'
    }
  ];

  const userMenu = (
    <Menu>
      <Menu.Item key="profile">个人资料</Menu.Item>
      <Menu.Item key="settings">账户设置</Menu.Item>
      <Menu.Divider />
      <Menu.Item key="logout">退出登录</Menu.Item>
    </Menu>
  );

  return (
    <Layout style={{ minHeight: '100vh' }}>
      <Sider 
        trigger={null} 
        collapsible 
        collapsed={collapsed}
        style={{
          background: '#fff',
          borderRight: '1px solid #f0f0f0'
        }}
      >
        <div style={{ 
          height: 64, 
          display: 'flex', 
          alignItems: 'center', 
          justifyContent: 'center',
          borderBottom: '1px solid #f0f0f0'
        }}>
          <img 
            src="/logo.png" 
            alt="CosyVoice" 
            style={{ height: 32 }}
          />
          {!collapsed && (
            <span style={{ 
              marginLeft: 12, 
              fontSize: 18, 
              fontWeight: 'bold',
              color: '#1890ff'
            }}>
              CosyVoice
            </span>
          )}
        </div>
        <Menu
          mode="inline"
          defaultSelectedKeys={['dashboard']}
          style={{ borderRight: 0, marginTop: 16 }}
          items={menuItems}
        />
      </Sider>
      
      <Layout>
        <Header style={{ 
          background: '#fff', 
          padding: '0 24px',
          borderBottom: '1px solid #f0f0f0',
          display: 'flex',
          alignItems: 'center',
          justifyContent: 'space-between'
        }}>
          <Button
            type="text"
            icon={collapsed ? <MenuUnfoldOutlined /> : <MenuFoldOutlined />}
            onClick={() => setCollapsed(!collapsed)}
            style={{ fontSize: 16 }}
          />
          
          <div style={{ display: 'flex', alignItems: 'center', gap: 16 }}>
            <Button type="link" href="/" target="_blank">
              返回经典界面
            </Button>
            <Dropdown overlay={userMenu} placement="bottomRight">
              <div style={{ 
                display: 'flex', 
                alignItems: 'center', 
                cursor: 'pointer',
                padding: '4px 8px',
                borderRadius: 6
              }}>
                <Avatar size="small" icon={<UserOutlined />} />
                <span style={{ marginLeft: 8 }}>用户名</span>
              </div>
            </Dropdown>
          </div>
        </Header>
        
        <Content style={{ 
          margin: 24, 
          padding: 24, 
          background: '#f5f5f5',
          borderRadius: 8,
          minHeight: 280 
        }}>
          {children}
        </Content>
      </Layout>
    </Layout>
  );
};

export default AdminLayout;
```

### 2.2 TTS生成页面组件
```jsx
// src/components/TTS/TTSGenerator.jsx
import React, { useState } from 'react';
import { 
  Card, 
  Tabs, 
  Input, 
  Select, 
  Button, 
  Upload, 
  Slider,
  Row,
  Col,
  message,
  Progress
} from 'antd';
import { 
  PlayCircleOutlined, 
  UploadOutlined,
  SettingOutlined,
  DownloadOutlined
} from '@ant-design/icons';

const { TextArea } = Input;
const { Option } = Select;
const { TabPane } = Tabs;

const TTSGenerator = () => {
  const [activeMode, setActiveMode] = useState('sft');
  const [ttsText, setTtsText] = useState('我是通义实验室语音团队全新推出的生成式语音大模型');
  const [speaker, setSpeaker] = useState('中文女');
  const [speed, setSpeed] = useState(1.0);
  const [isGenerating, setIsGenerating] = useState(false);
  const [progress, setProgress] = useState(0);

  const modes = [
    { key: 'sft', label: '预训练音色', icon: '🎭' },
    { key: 'zero_shot', label: '3s极速复刻', icon: '⚡' },
    { key: 'cross_lingual', label: '跨语种复刻', icon: '🌍' },
    { key: 'instruct', label: '自然语言控制', icon: '🎯' }
  ];

  const handleGenerate = async () => {
    if (!ttsText.trim()) {
      message.error('请输入要合成的文本');
      return;
    }

    setIsGenerating(true);
    setProgress(0);

    // 模拟生成过程
    const interval = setInterval(() => {
      setProgress(prev => {
        if (prev >= 100) {
          clearInterval(interval);
          setIsGenerating(false);
          message.success('音频生成成功！');
          return 100;
        }
        return prev + 10;
      });
    }, 200);
  };

  const renderModeParams = () => {
    switch (activeMode) {
      case 'sft':
        return (
          <Row gutter={16}>
            <Col span={12}>
              <label>选择说话人</label>
              <Select 
                value={speaker} 
                onChange={setSpeaker}
                style={{ width: '100%', marginTop: 8 }}
              >
                <Option value="中文女">中文女</Option>
                <Option value="中文男">中文男</Option>
                <Option value="英文女">英文女</Option>
                <Option value="英文男">英文男</Option>
              </Select>
            </Col>
          </Row>
        );
      
      case 'zero_shot':
        return (
          <Row gutter={16}>
            <Col span={12}>
              <label>参考音频</label>
              <Upload style={{ marginTop: 8 }}>
                <Button icon={<UploadOutlined />}>上传音频文件</Button>
              </Upload>
            </Col>
            <Col span={12}>
              <label>参考文本</label>
              <TextArea 
                rows={3} 
                placeholder="请输入参考音频对应的文本"
                style={{ marginTop: 8 }}
              />
            </Col>
          </Row>
        );
      
      case 'instruct':
        return (
          <Row gutter={16}>
            <Col span={12}>
              <label>基础说话人</label>
              <Select 
                value={speaker} 
                onChange={setSpeaker}
                style={{ width: '100%', marginTop: 8 }}
              >
                <Option value="中文女">中文女</Option>
                <Option value="中文男">中文男</Option>
              </Select>
            </Col>
            <Col span={12}>
              <label>控制指令</label>
              <Input 
                placeholder="如：用温柔的语气说"
                style={{ marginTop: 8 }}
              />
            </Col>
          </Row>
        );
      
      default:
        return null;
    }
  };

  return (
    <div style={{ maxWidth: 1200, margin: '0 auto' }}>
      <Row gutter={24}>
        <Col span={16}>
          <Card title="TTS 音频生成" style={{ marginBottom: 24 }}>
            {/* 模式选择 */}
            <div style={{ marginBottom: 24 }}>
              <label style={{ display: 'block', marginBottom: 8, fontWeight: 500 }}>
                推理模式
              </label>
              <Tabs 
                activeKey={activeMode} 
                onChange={setActiveMode}
                type="card"
              >
                {modes.map(mode => (
                  <TabPane 
                    tab={
                      <span>
                        <span style={{ marginRight: 8 }}>{mode.icon}</span>
                        {mode.label}
                      </span>
                    } 
                    key={mode.key}
                  />
                ))}
              </Tabs>
            </div>

            {/* 文本输入 */}
            <div style={{ marginBottom: 24 }}>
              <label style={{ display: 'block', marginBottom: 8, fontWeight: 500 }}>
                合成文本
              </label>
              <TextArea
                value={ttsText}
                onChange={(e) => setTtsText(e.target.value)}
                rows={4}
                placeholder="请输入要合成的文本..."
                showCount
                maxLength={500}
              />
              <div style={{ 
                marginTop: 8, 
                fontSize: 12, 
                color: '#8c8c8c',
                textAlign: 'right'
              }}>
                预计时长: {Math.ceil(ttsText.length / 10)}秒
              </div>
            </div>

            {/* 动态参数 */}
            <div style={{ marginBottom: 24 }}>
              <label style={{ display: 'block', marginBottom: 16, fontWeight: 500 }}>
                参数设置
              </label>
              {renderModeParams()}
            </div>

            {/* 高级设置 */}
            <Card 
              size="small" 
              title={
                <span>
                  <SettingOutlined style={{ marginRight: 8 }} />
                  高级设置
                </span>
              }
              style={{ marginBottom: 24 }}
            >
              <Row gutter={16}>
                <Col span={12}>
                  <label>语速调节</label>
                  <Slider
                    min={0.5}
                    max={2.0}
                    step={0.1}
                    value={speed}
                    onChange={setSpeed}
                    marks={{
                      0.5: '0.5x',
                      1.0: '1.0x',
                      2.0: '2.0x'
                    }}
                    style={{ marginTop: 16 }}
                  />
                </Col>
                <Col span={12}>
                  <label>随机种子</label>
                  <Input 
                    type="number" 
                    defaultValue={0}
                    style={{ marginTop: 8 }}
                    addonAfter={<Button size="small">🎲</Button>}
                  />
                </Col>
              </Row>
            </Card>

            {/* 生成按钮 */}
            <div style={{ textAlign: 'center' }}>
              <Button
                type="primary"
                size="large"
                icon={<PlayCircleOutlined />}
                loading={isGenerating}
                onClick={handleGenerate}
                style={{ 
                  minWidth: 200,
                  height: 48,
                  fontSize: 16,
                  background: '#722ed1',
                  borderColor: '#722ed1'
                }}
              >
                {isGenerating ? '生成中...' : '生成音频'}
              </Button>
            </div>

            {/* 生成进度 */}
            {isGenerating && (
              <div style={{ marginTop: 24 }}>
                <Progress 
                  percent={progress} 
                  status="active"
                  strokeColor="#722ed1"
                />
              </div>
            )}
          </Card>
        </Col>

        <Col span={8}>
          {/* 快速操作 */}
          <Card title="快速操作" size="small" style={{ marginBottom: 16 }}>
            <Button block style={{ marginBottom: 8 }}>
              📁 选择模板文本
            </Button>
            <Button block style={{ marginBottom: 8 }}>
              🎙️ 录制参考音频
            </Button>
            <Button block>
              📋 查看历史记录
            </Button>
          </Card>

          {/* 使用统计 */}
          <Card title="今日统计" size="small" style={{ marginBottom: 16 }}>
            <div style={{ textAlign: 'center' }}>
              <div style={{ fontSize: 24, fontWeight: 'bold', color: '#1890ff' }}>
                23
              </div>
              <div style={{ color: '#8c8c8c' }}>已生成音频</div>
            </div>
            <div style={{ 
              display: 'flex', 
              justifyContent: 'space-between',
              marginTop: 16,
              fontSize: 12,
              color: '#8c8c8c'
            }}>
              <span>配额使用: 756/1000</span>
              <span>成功率: 98.5%</span>
            </div>
          </Card>

          {/* 最近生成 */}
          <Card title="最近生成" size="small">
            <div style={{ fontSize: 12 }}>
              <div style={{ 
                padding: '8px 0', 
                borderBottom: '1px solid #f0f0f0',
                display: 'flex',
                justifyContent: 'space-between',
                alignItems: 'center'
              }}>
                <span>我是通义实验室...</span>
                <Button size="small" type="link" icon={<DownloadOutlined />} />
              </div>
              <div style={{ 
                padding: '8px 0', 
                borderBottom: '1px solid #f0f0f0',
                display: 'flex',
                justifyContent: 'space-between',
                alignItems: 'center'
              }}>
                <span>Hello, this is...</span>
                <Button size="small" type="link" icon={<DownloadOutlined />} />
              </div>
            </div>
          </Card>
        </Col>
      </Row>
    </div>
  );
};

export default TTSGenerator;
```

---

## 3. 移动端适配示例

### 3.1 移动端CSS样式
```css
/* mobile-styles.css */
/* 移动端适配样式 */
@media (max-width: 767px) {
  /* 移动端容器 */
  .mobile-container {
    padding: 16px;
    padding-top: 72px; /* 为固定导航留空间 */
  }

  /* 移动端导航 */
  .mobile-nav {
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    z-index: 1000;
    background: #ffffff;
    border-bottom: 1px solid #f0f0f0;
    height: 56px;
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 0 16px;
  }

  .mobile-nav .logo {
    height: 32px;
  }

  .mobile-nav .menu-toggle {
    width: 40px;
    height: 40px;
    border: none;
    background: none;
    cursor: pointer;
    display: flex;
    align-items: center;
    justify-content: center;
  }

  /* 移动端模式标签 */
  .mode-tabs-mobile {
    margin-bottom: 24px;
    overflow-x: auto;
    -webkit-overflow-scrolling: touch;
  }

  .mode-tabs-mobile .tabs-container {
    display: flex;
    gap: 8px;
    min-width: max-content;
    padding: 0 4px;
  }

  .mode-tabs-mobile .tab-item {
    flex: 0 0 auto;
    padding: 8px 16px;
    background: #f5f5f5;
    border-radius: 20px;
    font-size: 14px;
    color: #595959;
    cursor: pointer;
    transition: all 0.3s;
    white-space: nowrap;
  }

  .mode-tabs-mobile .tab-item.active {
    background: #1890ff;
    color: white;
  }

  /* 移动端表单 */
  .mobile-form .form-item {
    margin-bottom: 20px;
  }

  .mobile-form .form-label {
    display: block;
    margin-bottom: 8px;
    font-weight: 500;
    color: #262626;
  }

  .mobile-form .form-input,
  .mobile-form .form-textarea,
  .mobile-form .form-select {
    width: 100%;
    padding: 12px 16px;
    border: 1px solid #d9d9d9;
    border-radius: 8px;
    font-size: 16px; /* 防止iOS缩放 */
    line-height: 1.5;
  }

  .mobile-form .form-textarea {
    min-height: 120px;
    resize: none;
  }

  /* 移动端按钮 */
  .btn-mobile {
    width: 100%;
    height: 48px;
    font-size: 16px;
    border-radius: 8px;
    border: none;
    cursor: pointer;
    transition: all 0.3s;
  }

  .btn-mobile.primary {
    background: #1890ff;
    color: white;
  }

  .btn-mobile.primary:hover {
    background: #40a9ff;
  }

  /* 底部操作栏 */
  .bottom-actions {
    position: fixed;
    bottom: 0;
    left: 0;
    right: 0;
    background: #ffffff;
    border-top: 1px solid #f0f0f0;
    padding: 16px;
    display: flex;
    gap: 12px;
    z-index: 100;
  }

  .bottom-actions .btn {
    flex: 1;
    height: 48px;
    font-size: 16px;
    border-radius: 8px;
  }

  /* 移动端音频播放器 */
  .audio-player-mobile {
    background: #f5f5f5;
    border-radius: 12px;
    padding: 16px;
    margin: 16px 0;
  }

  .audio-controls-mobile {
    display: flex;
    align-items: center;
    gap: 12px;
    margin-bottom: 12px;
  }

  .audio-controls-mobile .play-btn {
    width: 48px;
    height: 48px;
    border-radius: 50%;
    background: #722ed1;
    border: none;
    color: white;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 18px;
    cursor: pointer;
  }

  .audio-controls-mobile .progress {
    flex: 1;
    height: 6px;
    background: #e8e8e8;
    border-radius: 3px;
    overflow: hidden;
  }

  .audio-controls-mobile .progress-bar {
    height: 100%;
    background: #722ed1;
    width: 0%;
    transition: width 0.1s;
  }

  .audio-actions-mobile {
    display: flex;
    justify-content: center;
    gap: 16px;
  }

  .audio-actions-mobile .btn-icon {
    width: 40px;
    height: 40px;
    border-radius: 50%;
    background: #ffffff;
    border: 1px solid #d9d9d9;
    display: flex;
    align-items: center;
    justify-content: center;
    color: #595959;
    cursor: pointer;
  }
}
```

---

## 4. 实施建议

### 4.1 开发步骤
1. **第一步**: 实现增强版Gradio界面
2. **第二步**: 开发React管理后台基础框架
3. **第三步**: 实现核心TTS生成页面
4. **第四步**: 添加任务管理和文件管理功能
5. **第五步**: 完善移动端适配

### 4.2 技术栈建议
- **前端框架**: React 18+ 或 Vue 3+
- **UI组件库**: Ant Design 或 Element Plus
- **状态管理**: Redux Toolkit 或 Pinia
- **构建工具**: Vite 或 Create React App
- **CSS预处理**: Sass 或 Less

### 4.3 部署建议
- **开发环境**: 使用热重载提高开发效率
- **测试环境**: 配置自动化测试和CI/CD
- **生产环境**: 使用CDN加速静态资源
- **监控**: 集成错误监控和性能监控

---

**文档状态**: ✅ 完成  
**下一步**: 开始前端开发实施
