"""
监控相关API视图
"""
from rest_framework import permissions, status
from rest_framework.decorators import api_view, permission_classes
from rest_framework.response import Response
from django.utils import timezone
from datetime import timedelta
from drf_spectacular.utils import extend_schema
from .monitoring import system_monitor
from .alerts import alert_manager
from apps.users.models import UserRole
from loguru import logger


@extend_schema(
    summary="获取系统监控指标",
    description="获取系统性能监控指标",
    tags=["监控"]
)
@api_view(['GET'])
@permission_classes([permissions.IsAuthenticated])
def system_metrics(request):
    """获取系统监控指标"""
    # 只有管理员可以查看系统指标
    if request.user.role != UserRole.ADMIN:
        return Response({'error': '权限不足'}, status=status.HTTP_403_FORBIDDEN)
    
    try:
        # 收集最新指标
        system_monitor.collect_system_metrics()
        system_monitor.collect_database_metrics()
        system_monitor.collect_redis_metrics()
        system_monitor.collect_application_metrics()
        
        # 获取指标数据
        metrics = system_monitor.get_metrics()
        
        # 按类别组织指标
        organized_metrics = {
            'system': {},
            'database': {},
            'redis': {},
            'application': {}
        }
        
        for metric_name, metric_data in metrics.items():
            if metric_name.startswith('system.'):
                organized_metrics['system'][metric_name] = metric_data
            elif metric_name.startswith('database.'):
                organized_metrics['database'][metric_name] = metric_data
            elif metric_name.startswith('redis.'):
                organized_metrics['redis'][metric_name] = metric_data
            elif metric_name.startswith('app.'):
                organized_metrics['application'][metric_name] = metric_data
        
        return Response({
            'timestamp': timezone.now().isoformat(),
            'metrics': organized_metrics
        })
        
    except Exception as e:
        logger.error(f"获取系统指标失败: {e}")
        return Response({'error': str(e)}, status=status.HTTP_500_INTERNAL_SERVER_ERROR)


@extend_schema(
    summary="获取系统告警",
    description="获取系统告警信息",
    tags=["监控"]
)
@api_view(['GET'])
@permission_classes([permissions.IsAuthenticated])
def system_alerts(request):
    """获取系统告警"""
    # 只有管理员可以查看告警
    if request.user.role != UserRole.ADMIN:
        return Response({'error': '权限不足'}, status=status.HTTP_403_FORBIDDEN)
    
    try:
        # 获取查询参数
        active_only = request.GET.get('active_only', 'true').lower() == 'true'
        category = request.GET.get('category')
        
        # 获取告警数据
        if active_only:
            alerts = alert_manager.get_active_alerts()
        else:
            alerts = alert_manager.get_alert_history()
        
        # 按类别过滤
        if category:
            alerts = [alert for alert in alerts if alert.get('category') == category]
        
        # 转换时间格式
        for alert in alerts:
            if 'timestamp' in alert and hasattr(alert['timestamp'], 'isoformat'):
                alert['timestamp'] = alert['timestamp'].isoformat()
            if 'resolved_at' in alert and hasattr(alert['resolved_at'], 'isoformat'):
                alert['resolved_at'] = alert['resolved_at'].isoformat()
        
        return Response({
            'timestamp': timezone.now().isoformat(),
            'alerts': alerts,
            'total_count': len(alerts)
        })
        
    except Exception as e:
        logger.error(f"获取系统告警失败: {e}")
        return Response({'error': str(e)}, status=status.HTTP_500_INTERNAL_SERVER_ERROR)


@extend_schema(
    summary="获取性能统计",
    description="获取系统性能统计信息",
    tags=["监控"]
)
@api_view(['GET'])
@permission_classes([permissions.IsAuthenticated])
def performance_stats(request):
    """获取性能统计"""
    # 只有管理员可以查看性能统计
    if request.user.role != UserRole.ADMIN:
        return Response({'error': '权限不足'}, status=status.HTTP_403_FORBIDDEN)
    
    try:
        from apps.tasks.models import Task, TaskStatus
        from apps.users.models import User
        from apps.files.models import AudioFile
        from django.db.models import Avg, Count, Sum
        
        # 时间范围
        hours = int(request.GET.get('hours', 24))
        start_time = timezone.now() - timedelta(hours=hours)
        
        # 任务性能统计
        from django.db import models
        task_stats = Task.objects.filter(created_at__gte=start_time).aggregate(
            total_tasks=Count('id'),
            completed_tasks=Count('id', filter=models.Q(status=TaskStatus.COMPLETED)),
            failed_tasks=Count('id', filter=models.Q(status=TaskStatus.FAILED)),
            avg_processing_time=Avg('processing_time'),
            avg_queue_time=Avg('queue_time')
        )
        
        # 用户活动统计
        user_stats = {
            'active_users': User.objects.filter(
                last_login__gte=start_time,
                is_active=True
            ).count(),
            'new_users': User.objects.filter(
                date_joined__gte=start_time
            ).count()
        }
        
        # 文件统计
        file_stats = AudioFile.objects.filter(
            created_at__gte=start_time,
            is_active=True
        ).aggregate(
            new_files=Count('id'),
            total_size=Sum('file_size')
        )
        
        # API调用统计（从缓存中获取）
        from django.core.cache import cache
        api_stats = {
            'total_requests': cache.get('api_stats:total_requests', 0),
            'error_rate': cache.get('api_stats:error_rate', 0),
            'avg_response_time': cache.get('api_stats:avg_response_time', 0)
        }
        
        return Response({
            'timestamp': timezone.now().isoformat(),
            'time_range': {
                'start': start_time.isoformat(),
                'end': timezone.now().isoformat(),
                'hours': hours
            },
            'task_performance': task_stats,
            'user_activity': user_stats,
            'file_activity': file_stats,
            'api_performance': api_stats
        })
        
    except Exception as e:
        logger.error(f"获取性能统计失败: {e}")
        return Response({'error': str(e)}, status=status.HTTP_500_INTERNAL_SERVER_ERROR)


@extend_schema(
    summary="获取系统状态概览",
    description="获取系统整体状态概览",
    tags=["监控"]
)
@api_view(['GET'])
@permission_classes([permissions.IsAuthenticated])
def system_overview(request):
    """获取系统状态概览"""
    try:
        # 收集基础指标
        system_monitor.collect_system_metrics()
        system_monitor.collect_database_metrics()
        system_monitor.collect_redis_metrics()
        system_monitor.collect_application_metrics()
        
        metrics = system_monitor.get_metrics()
        active_alerts = alert_manager.get_active_alerts()
        
        # 计算系统健康状态
        health_score = 100
        health_issues = []
        
        # 检查CPU使用率
        cpu_usage = metrics.get('system.cpu.usage', {}).get('value', 0)
        if cpu_usage > 80:
            health_score -= 20
            health_issues.append(f'CPU使用率过高: {cpu_usage:.1f}%')
        
        # 检查内存使用率
        memory_usage = metrics.get('system.memory.usage', {}).get('value', 0)
        if memory_usage > 85:
            health_score -= 20
            health_issues.append(f'内存使用率过高: {memory_usage:.1f}%')
        
        # 检查磁盘使用率
        disk_usage = metrics.get('system.disk.usage', {}).get('value', 0)
        if disk_usage > 90:
            health_score -= 30
            health_issues.append(f'磁盘使用率过高: {disk_usage:.1f}%')
        
        # 检查数据库响应时间
        db_response_time = metrics.get('database.response_time', {}).get('value', 0)
        if db_response_time > 1000:
            health_score -= 15
            health_issues.append(f'数据库响应时间过长: {db_response_time:.1f}ms')
        
        # 检查活跃告警
        if active_alerts:
            health_score -= len(active_alerts) * 5
            health_issues.append(f'存在 {len(active_alerts)} 个活跃告警')
        
        # 确定健康状态
        if health_score >= 90:
            health_status = 'excellent'
        elif health_score >= 70:
            health_status = 'good'
        elif health_score >= 50:
            health_status = 'warning'
        else:
            health_status = 'critical'
        
        # 获取关键指标
        key_metrics = {
            'cpu_usage': cpu_usage,
            'memory_usage': memory_usage,
            'disk_usage': disk_usage,
            'database_response_time': db_response_time,
            'redis_response_time': metrics.get('redis.response_time', {}).get('value', 0),
            'pending_tasks': metrics.get('app.tasks.pending', {}).get('value', 0),
            'failed_tasks': metrics.get('app.tasks.failed', {}).get('value', 0),
            'total_users': metrics.get('app.users.total', {}).get('value', 0),
            'total_files': metrics.get('app.files.total', {}).get('value', 0)
        }
        
        return Response({
            'timestamp': timezone.now().isoformat(),
            'health': {
                'status': health_status,
                'score': max(0, health_score),
                'issues': health_issues
            },
            'key_metrics': key_metrics,
            'alerts': {
                'active_count': len(active_alerts),
                'critical_count': len([a for a in active_alerts if a.get('level') == 'critical']),
                'warning_count': len([a for a in active_alerts if a.get('level') == 'warning'])
            }
        })
        
    except Exception as e:
        logger.error(f"获取系统概览失败: {e}")
        return Response({'error': str(e)}, status=status.HTTP_500_INTERNAL_SERVER_ERROR)
