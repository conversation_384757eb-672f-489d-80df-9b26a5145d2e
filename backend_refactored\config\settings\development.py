"""
开发环境配置
"""
from .base import *
from dotenv import load_dotenv

# 加载.env文件
load_dotenv()

# 调试模式
DEBUG = True

# 允许的主机
ALLOWED_HOSTS = ['*']

# CORS配置 - 开发环境允许所有来源
CORS_ALLOW_ALL_ORIGINS = True

# 数据库配置 - 开发环境使用MySQL（从base.py继承）

# Redis配置 - 开发环境
REDIS_URL = os.environ.get('REDIS_URL', 'redis://localhost:6379/0')

# Celery配置 - 开发环境使用内存存储
CELERY_TASK_ALWAYS_EAGER = True
CELERY_TASK_EAGER_PROPAGATES = True

# 日志配置 - 开发环境输出到控制台
LOGGING['handlers']['console']['level'] = 'DEBUG'
LOGGING['loggers']['cosyvoice']['level'] = 'DEBUG'

# 邮件配置 - 开发环境输出到控制台
EMAIL_BACKEND = 'django.core.mail.backends.console.EmailBackend'

# 静态文件配置
STATICFILES_DIRS = [
    BASE_DIR / 'static',
]

# CosyVoice配置 - 开发环境
COSYVOICE_MODEL_DIR = os.environ.get('COSYVOICE_MODEL_DIR', 'pretrained_models/CosyVoice-300M')

# 开发工具
INSTALLED_APPS += [
    'django_extensions',
]

# 性能调试
if DEBUG:
    try:
        import debug_toolbar
        INSTALLED_APPS.append('debug_toolbar')
        MIDDLEWARE.insert(0, 'debug_toolbar.middleware.DebugToolbarMiddleware')
        INTERNAL_IPS = ['127.0.0.1', 'localhost']
    except ImportError:
        pass
