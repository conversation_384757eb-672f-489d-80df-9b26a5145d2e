"""
文件管理Admin配置
"""
from django.contrib import admin
from django.utils.html import format_html
from django.urls import reverse
from django.utils import timezone
from .models import AudioFile, FileShare, FileOperation


@admin.register(AudioFile)
class AudioFileAdmin(admin.ModelAdmin):
    list_display = ('filename', 'user_display', 'file_type', 'storage_type', 
                   'file_size_display', 'duration_display', 'is_public', 
                   'access_count', 'is_expired_display', 'created_at')
    list_filter = ('file_type', 'storage_type', 'is_public', 'auto_delete', 'created_at')
    search_fields = ('filename', 'original_filename', 'user__username', 'user__email')
    readonly_fields = ('id', 'file_size_display', 'duration_display', 'download_link', 
                      'created_at', 'updated_at', 'last_accessed', 'access_count')
    
    fieldsets = (
        ('基本信息', {
            'fields': ('id', 'user', 'filename', 'original_filename', 'file_type')
        }),
        ('存储信息', {
            'fields': ('storage_type', 'file_path', 'file_url', 'file_size_display')
        }),
        ('音频信息', {
            'fields': ('duration_display', 'sample_rate', 'channels', 'bit_rate', 'mime_type')
        }),
        ('访问控制', {
            'fields': ('is_public', 'expires_at', 'auto_delete')
        }),
        ('统计信息', {
            'fields': ('access_count', 'last_accessed', 'created_at', 'updated_at')
        }),
        ('元数据', {
            'fields': ('metadata', 'tags'),
            'classes': ('collapse',)
        }),
        ('操作', {
            'fields': ('download_link',),
            'classes': ('collapse',)
        })
    )
    
    def user_display(self, obj):
        if obj.user:
            return f"{obj.user.username} ({obj.user.email})"
        return "匿名用户"
    user_display.short_description = '用户'
    
    def file_size_display(self, obj):
        return obj.file_size_human
    file_size_display.short_description = '文件大小'
    
    def duration_display(self, obj):
        return obj.duration_human
    duration_display.short_description = '时长'
    
    def is_expired_display(self, obj):
        if obj.is_expired:
            return format_html('<span style="color: red;">已过期</span>')
        elif obj.expires_at:
            return format_html('<span style="color: orange;">未过期</span>')
        else:
            return format_html('<span style="color: green;">永不过期</span>')
    is_expired_display.short_description = '过期状态'
    
    def download_link(self, obj):
        if obj.pk:
            url = f"/download/{obj.filename}"
            return format_html('<a href="{}" target="_blank">下载文件</a>', url)
        return "保存后可下载"
    download_link.short_description = '下载链接'
    
    actions = ['make_public', 'make_private', 'extend_expiry', 'delete_selected']
    
    def make_public(self, request, queryset):
        updated = queryset.update(is_public=True)
        self.message_user(request, f'成功将 {updated} 个文件设为公开')
    make_public.short_description = '设为公开'
    
    def make_private(self, request, queryset):
        updated = queryset.update(is_public=False)
        self.message_user(request, f'成功将 {updated} 个文件设为私有')
    make_private.short_description = '设为私有'
    
    def extend_expiry(self, request, queryset):
        from datetime import timedelta
        extend_date = timezone.now() + timedelta(days=30)
        updated = queryset.update(expires_at=extend_date)
        self.message_user(request, f'成功延长 {updated} 个文件的过期时间30天')
    extend_expiry.short_description = '延长过期时间30天'


@admin.register(FileShare)
class FileShareAdmin(admin.ModelAdmin):
    list_display = ('audio_file_display', 'share_token_display', 'password_display',
                   'download_count', 'max_downloads', 'is_valid_display', 'created_at')
    list_filter = ('created_at', 'expires_at')
    search_fields = ('audio_file__filename', 'share_token')
    readonly_fields = ('id', 'share_token', 'share_url', 'download_count', 
                      'is_valid_display', 'created_at', 'updated_at')
    
    fieldsets = (
        ('基本信息', {
            'fields': ('id', 'audio_file', 'share_token', 'share_url')
        }),
        ('访问控制', {
            'fields': ('password', 'expires_at', 'max_downloads')
        }),
        ('统计信息', {
            'fields': ('download_count', 'is_valid_display', 'created_at', 'updated_at')
        })
    )
    
    def audio_file_display(self, obj):
        return obj.audio_file.filename
    audio_file_display.short_description = '音频文件'
    
    def share_token_display(self, obj):
        return f"{obj.share_token[:8]}..."
    share_token_display.short_description = '分享令牌'
    
    def password_display(self, obj):
        return "已设置" if obj.password else "无密码"
    password_display.short_description = '访问密码'
    
    def is_valid_display(self, obj):
        if obj.is_valid:
            return format_html('<span style="color: green;">有效</span>')
        else:
            return format_html('<span style="color: red;">无效</span>')
    is_valid_display.short_description = '状态'
    
    def share_url(self, obj):
        if obj.pk:
            url = f"/share/{obj.share_token}"
            return format_html('<a href="{}" target="_blank">{}</a>', url, url)
        return "保存后生成"
    share_url.short_description = '分享链接'


@admin.register(FileOperation)
class FileOperationAdmin(admin.ModelAdmin):
    list_display = ('user_display', 'audio_file_display', 'operation', 
                   'ip_address', 'created_at')
    list_filter = ('operation', 'created_at')
    search_fields = ('user__username', 'audio_file__filename', 'ip_address')
    readonly_fields = ('id', 'user', 'audio_file', 'operation', 'ip_address', 
                      'user_agent', 'extra_data', 'created_at')
    
    def user_display(self, obj):
        if obj.user:
            return obj.user.username
        return "匿名用户"
    user_display.short_description = '用户'
    
    def audio_file_display(self, obj):
        return obj.audio_file.filename
    audio_file_display.short_description = '音频文件'
    
    def has_add_permission(self, request):
        return False
    
    def has_change_permission(self, request, obj=None):
        return False
